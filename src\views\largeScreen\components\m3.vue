<!--
 * @Description: 生产安全事故情况表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-09-05 10:03:54
 * @LastEditTime: 2024-09-05 16:59:35
-->

<template>
  <div class="con">
    <div class="title">生产安全事故情况表</div>
    <ul class="tab">
      <li
        v-for="item in typeList"
        :class="[item.id == active ? 'active' : '']"
        @click="handleChange(item.id)"
      >
        {{ item.name }}
      </li>
    </ul>
    <div class="echarts-con">
      <Echarts :option="option" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ShanghaiDistrict } from '@/utils/constants'
const option = reactive<any>({
  color: ['#95cb77', '#5c6fc4', '#f4c95f', '#e56b69'],
  legend: {
    right: '4',
    top: '8',
    itemWidth: 10,
    itemHeight: 10,
    textStyle: {
      color: 'white'
    }
  },
  grid: {
    left: '0',
    top: '40',
    bottom: '10',
    right: '10',
    containLabel: true
  },

  yAxis: {
    type: 'value',
    axisLabel: {
      //x轴文字的配置
      show: true,
      textStyle: {
        color: '#fff',
        fontSize: '10'
      }
    }
  },
  xAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      //x轴文字的配置
      show: true,
      rotate: 30,
      textStyle: {
        color: '#fff',
        fontSize: '10'
      }
    }
  },
  series: []
})
const typeList = ref([
  { id: 1, name: '事故数量' },
  { id: 2, name: '人数' }
])
const active = ref(1)
const handleChange = (id) => {
  active.value = id
  setArea(id)
}
const setArea = (id) => {
  option.xAxis.data = ShanghaiDistrict
  option.series = [
    {
      name: '工矿商贸',
      type: 'bar',
      stack: '事故类型',
      barWidth: 16,
      data: [1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 2, 1]
    },
    {
      name: '道路交通',
      type: 'bar',
      stack: '事故类型',
      data: [4, 1, 1, 1, 3, 1, 3, 1, 4, 1, 1, 1, 3, 1, 3, 1]
    },
    {
      name: '水上交通',
      type: 'bar',
      stack: '事故类型',
      data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
    },
    {
      name: '铁路运输',
      type: 'bar',
      stack: '事故类型',

      data: [1, 3, 1, 1, 1, 1, 0, 1, 1, 3, 1, 1, 1, 1, 0, 1]
    }
  ]
}
onMounted(async () => {
  setArea(1)
})
</script>

<style scoped lang="scss">
.con {
  height: 100%;
  color: #fff;

  /* background: #071b3d; */
  background: linear-gradient(#3264a4, #051430 30%, #051430 70%);
  border-radius: 6px;
  padding: 10px;
  border: 1px solid #4189f0;
  position: relative;
  .tab {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 10px;
    display: flex;

    cursor: pointer;
    border: 1px solid #4189f0;
    border-radius: 4px;
    > li {
      width: 60px;
      font-size: 12px;
      text-align: center;
      line-height: 26px;

      color: #767e8e;
      font-weight: bold;
    }
    > li:first-child {
      border-right: 1px solid #4189f0;
    }
    .active {
      color: #fff !important;
      background: transparent !important;
    }
  }
  .title {
    font-size: 15px;
    font-weight: bold;
  }
  .echarts-con {
    width: 100%;
    height: calc(100% - 26px);
  }
}
</style>
