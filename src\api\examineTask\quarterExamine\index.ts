/*
 * @Description: 季度考核API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-09-11 13:28:59
 * @LastEditTime: 2024-09-11 14:30:32
 */
import request from '@/config/axios'

// 季度考评 VO
export interface WorkVO {
  id: number // 主键(自增策略)
  assessmentYear: number // 年份
  templateId: number // 考核模板ID
  quarter: string // 季度
  assessmentUnit: number // 考核单位
  workName: string // 季度工作名称
  submitTime: Date // 填写时间
  status: number // 状态  0-未提交 1-已提交
}

// 季度考评 API
export const QuarterExamineApi = {
  // 查询季度考评分页
  getWorkPage: async (params: any) => {
    return await request.get({ url: `/system/quarter/work/page`, params })
  },

  // 查询季度考评详情
  getWork: async (id: number) => {
    return await request.get({ url: `/system/quarter/work/get?id=` + id })
  },

  // 新增季度考评
  createWork: async (data: WorkVO) => {
    return await request.post({ url: `/system/quarter/work/create`, data })
  },

  // 修改季度考评
  updateWork: async (data: any) => {
    return await request.put({ url: `/system/quarter/work/update`, data })
  },

  // 删除季度考评
  deleteWork: async (id: number) => {
    return await request.delete({ url: `/system/quarter/work/delete?id=` + id })
  },

  // 导出季度考评 Excel
  exportWork: async (params) => {
    return await request.download({ url: `/system/quarter/work/export-excel`, params })
  }
}

// 季度考评明细 VO
export interface WorkDetailVO {
  id: number // 主键(自增策略)
  quarterWorkId: number // 季度考评ID
  templateQuotaId: number // 考核模板指标ID
  region: number // 区域
  unitId: number // 被考核单位
  content: string // 内容
  annex: string // 附件
  remark: string // 内容描述
}

// 季度考评明细 API
export const QuarterExamineDetailApi = {
  // 查询季度考评明细分页
  getWorkDetailPage: async (params: any) => {
    return await request.get({ url: `/quarter/work-detail/page`, params })
  },

  // 查询季度考评明细详情
  getWorkDetail: async (id: number) => {
    return await request.get({ url: `/quarter/work-detail/get?id=` + id })
  },

  // 新增季度考评明细
  createWorkDetail: async (data: WorkDetailVO) => {
    return await request.post({ url: `/quarter/work-detail/create`, data })
  },

  // 修改季度考评明细
  updateWorkDetail: async (data: WorkDetailVO) => {
    return await request.put({ url: `/quarter/work-detail/update`, data })
  },

  // 新增/修改季度考评明细
  addEditWorkDetail: async (data: WorkDetailVO) => {
    return await request.post({ url: `/quarter/work-detail/saveOrUpdate`, data })
  },

  // 删除季度考评明细
  deleteWorkDetail: async (id: number) => {
    return await request.delete({ url: `/quarter/work-detail/delete?id=` + id })
  },

  // 导出季度考评明细 Excel
  exportWorkDetail: async (params) => {
    return await request.download({ url: `/quarter/work-detail/export-excel`, params })
  }
}
