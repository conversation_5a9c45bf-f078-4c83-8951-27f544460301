<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-07-27 15:40:21
 * @LastEditTime: 2024-09-05 13:19:51
-->
<template>
  <div ref="chartDom" class="chartDom"></div>
</template>
<script setup name="Echarts" lang="ts">
const { globalProperties } = getCurrentInstance()!.appContext.config
import { debounce } from 'lodash-es'

const unWarp = (obj) => obj && (obj.__v_raw || obj.valueOf() || obj)
const props = defineProps({
  // 图表配置项
  option: {
    type: Object,
    default: {}
  }
})
const chartDom = ref(null)
const chartInstance = ref<any>(null)

// 自适应不同屏幕时改变图表尺寸
const chartResize = debounce(() => {
  nextTick(() => {
    if (!chartInstance.value) {
      return
    }
    chartInstance.value && chartInstance.value.resize()
  })
}, 100)

// 监听图表数据时候变化,重新渲染图表
watch(
  () => props.option,
  (v) => {
    if (!v || !chartInstance.value) return
    unWarp(chartInstance.value).setOption(v, true) // 渲染图表
    chartResize()
  },
  { immediate: true, deep: true }
)
// 页面成功渲染,开始绘制图表
onMounted(() => {
  // 配置为svg形式,预防页面缩放而出现模糊问题;图表过于复杂时建议使用 Canvas
  chartInstance.value = globalProperties.$echarts.init(chartDom.value, null, {
    renderer: 'svg'
  })
  window.addEventListener('resize', chartResize)
})
// 页面销毁前,销毁事件和实例
onBeforeUnmount(() => {
  window.removeEventListener('resize', chartResize)
  chartInstance.value.dispose()
})
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
