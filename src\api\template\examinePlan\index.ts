/*
 * @Description: 考核方案
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-29 16:54:47
 * @LastEditTime: 2024-08-09 16:05:26
 */

import request from '@/config/axios'

// 考核管理——考核方案 VO
export interface SchemeVO {
  id: number // 主键(自增策略)
  examineSchemeTitle: string // 考核方案标题
  examineMethodId: number // 考核方式ID
  schemeStatus: number // 方案状态  0-未启用 1-正常 2-删除 3-关闭
  introduceYear: number // 考核年度
  examineUnitIds: string // 被考核单位
  examineTemplateId: number // 考核模板
}

// 考核管理——考核方案 API
export const SchemeApi = {
  // 查询考核管理——考核方案分页
  getSchemePage: async (params: any) => {
    return await request.get({ url: `/system/scheme/page`, params })
  },

  // 查询考核管理——考核方案详情
  getScheme: async (id: number) => {
    return await request.get({ url: `/system/scheme/get?id=` + id })
  },

  // 新增考核管理——考核方案
  createScheme: async (data: SchemeVO) => {
    return await request.post({ url: `/system/scheme/create`, data })
  },

  // 修改考核管理——考核方案
  updateScheme: async (data: any) => {
    return await request.put({ url: `/system/scheme/update`, data })
  },

  // 删除考核管理——考核方案
  deleteScheme: async (id: number) => {
    return await request.delete({ url: `/system/scheme/delete?id=` + id })
  },

  // 导出考核管理——考核方案 Excel
  exportScheme: async (params) => {
    return await request.download({ url: `/system/scheme/export-excel`, params })
  },

  // 专项工作获取年份及通过年份获取专项工作数据
  getSpecialWorkYear: async (params?) => {
    return await request.get({ url: `/system/scheme/special-work`,params })
  }
}
