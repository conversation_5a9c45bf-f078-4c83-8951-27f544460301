<!--
    * @Description: 城市概况
    * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
    * @Date: 2023-07-26 14:48:51
 * @LastEditTime: 2024-09-05 16:09:09
   -->
<template>
  <div class="con">
    <div class="title">
      考核排名-成员单位
      <ul class="date">
        <li @click="handleRange(1)" :class="acitiveNum == 1 ? 'active' : ''"> 今日 </li>
        <li @click="handleRange(2)" :class="acitiveNum == 2 ? 'active' : ''"> 本周 </li>
        <li @click="handleRange(3)" :class="acitiveNum == 3 ? 'active' : ''"> 本月 </li>
        <li @click="handleRange(4)" :class="acitiveNum == 4 ? 'active' : ''"> 全年 </li>
      </ul>
    </div>
    <div class="content" ref="contentRef">
      <ul class="head">
        <li>排名</li>
        <li>区名称</li>
        <li>总分</li>
        <li>事故分数</li>
        <li>过程分数</li>
      </ul>
      <ul class="body">
        <li v-for="(item, index) in dataList" :key="index">
          <span style="position: relative; height: 100%">
            <img
              v-if="item.imgSrc"
              :src="getAssetUrl(item.imgSrc)"
              alt=""
              style="
                width: 16px;
                height: 16px;
                position: absolute;
                top: 8px;
                left: 50%;
                transform: translateX(-50%);
              "
            />
            <span v-else>{{ index + 1 }}</span>
          </span>
          <span>{{ item.title }}</span>
          <span>{{ item.score1 }}</span>
          <span>{{ item.score2 }}</span>
          <span>{{ item.score3 }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script setup lang="ts">
const dataList = ref([
  {
    imgSrc: '../../../assets/imgs/largeScreen/rank1.png',
    title: '市发展改革委',
    score1: 90,
    score2: 45,
    score3: 45
  },
  {
    imgSrc: '../../../assets/imgs/largeScreen/rank2.png',
    title: '市经济信息化委',
    score1: 87,
    score2: 44,
    score3: 43
  },
  {
    imgSrc: '../../../assets/imgs/largeScreen/rank3.png',
    title: '市公安局',
    score1: 87,
    score2: 44,
    score3: 43
  },
  {
    title: '市人力资源社会保障局',
    score1: 85,
    score2: 43,
    score3: 42
  },
  {
    title: '市住房城乡建设管理委',
    score1: 84,
    score2: 42,
    score3: 42
  },
  {
    title: '市交通委',
    score1: 83,
    score2: 42,
    score3: 41
  },
  {
    title: '市农业农村委',
    score1: 82,
    score2: 41,
    score3: 41
  },
  {
    title: '市生态环境局',
    score1: 81,
    score2: 41,
    score3: 40
  },
  {
    title: '市规划资源局',
    score1: 80,
    score2: 40,
    score3: 40
  },
  {
    title: '市水务局',
    score1: 79,
    score2: 40,
    score3: 39
  },
  {
    title: '市文化旅游局',
    score1: 78,
    score2: 39,
    score3: 39
  },
  {
    title: '市卫生健康委',
    score1: 77,
    score2: 39,
    score3: 38
  },
  {
    title: '市市场监管局',
    score1: 76,
    score2: 38,
    score3: 38
  },
  {
    title: '市体育局',
    score1: 75,
    score2: 38,
    score3: 37
  },
  {
    title: '市绿化市容局',
    score1: 74,
    score2: 37,
    score3: 37
  },
  {
    title: '市国动办',
    score1: 73,
    score2: 37,
    score3: 36
  },
  {
    title: '市房屋管理局',
    score1: 72,
    score2: 36,
    score3: 36
  },
  {
    title: '市城管执法局',
    score1: 71,
    score2: 36,
    score3: 35
  },
  {
    title: '市商务委',
    score1: 70,
    score2: 35,
    score3: 35
  },
  {
    title: '市教委',
    score1: 69,
    score2: 35,
    score3: 34
  },
  {
    title: '市科委',
    score1: 66,
    score2: 33,
    score3: 33
  },
  {
    title: '市民政局',
    score1: 63,
    score2: 32,
    score3: 31
  },
  {
    title: '市民族宗教局',
    score1: 60,
    score2: 30,
    score3: 30
  }
])

const getAssetUrl = (imgUrl) => {
  return new URL(imgUrl, import.meta.url).href
}
const option = ref({})
const contentRef = ref<any>(null)
const acitiveNum = ref(4)
const handleRange = (num) => {
  acitiveNum.value = num
}
const randomScore = () => {
  return Math.floor(Math.random() * 100)
}
onMounted(() => {
  let h = contentRef.value.offsetHeight

  let domList = contentRef.value.getElementsByTagName('li')
  for (let item of domList) {
    item.style.height = (h - 30) / 10 + 'px'
    item.style.lineHeight = (h - 30) / 10 + 'px'
  }
  option.value = {
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['45%', '65%'],
        avoidLabelOverlap: false,

        labelLine: {
          show: true
        },
        itemStyle: {
          normal: {
            borderColor: '#fff'
          }
        },

        label: {
          position: 'outer',
          alignTo: 'edge',
          margin: 0,
          formatter(param) {
            return param.name + ' (' + param.percent * 2 + '%)'
          }
        },
        data: [
          { value: 1048, name: 'Search' },
          { value: 735, name: 'Direct' },
          { value: 580, name: 'Email' },
          { value: 484, name: 'Union' },
          { value: 300, name: 'Video' }
        ]
      }
    ]
  }
})
</script>

<style scoped lang="scss">
.con {
  height: 100%;
  color: #fff;
  background: #071b3d;
  //background: linear-gradient(#3264a4, #051430 30%, #051430 70%);

  padding: 10px;
  border-radius: 6px;
  border: 1px solid #4189f0;
  .active {
    color: #fff !important;
  }
  .title {
    font-size: 15px;
    font-weight: bold;
    position: relative;
    margin: 4px 0 2px 0;
    .date {
      position: absolute;
      display: flex;
      right: 10px;
      top: 4px;
      font-size: 12px;
      color: #767e8e;

      > li {
        flex: 1;
        margin: 0 4px;
        font-weight: bold;
        cursor: pointer;
      }
    }
  }
  .content {
    width: 100%;
    height: 100%;
    padding-top: 4px;
    overflow: hidden;
    font-size: 12px;
    text-align: left;
    .head {
      display: flex;

      background: #153373;
      > li {
        overflow: hidden;
        line-height: 38px !important;
        height: 38px !important;
      }
    }
    .head {
      > li:nth-child(1) {
        width: 14%;
        text-align: center;
      }
      > li:nth-child(2) {
        width: 38%;
      }
      > li:nth-child(3) {
        width: 16%;
      }
      > li:nth-child(4) {
        width: 16%;
      }
      > li:nth-child(5) {
        width: 16%;
      }
    }
    .body {
      height: 100%;
      overflow: hidden;
      margin-top: 2px;
      > li {
        line-height: 32px !important;
        height: 32px !important;
        > span {
          display: inline-block;
          overflow: hidden;
        }
        > span:nth-child(1) {
          width: 14%;
          text-align: center;
        }
        > span:nth-child(2) {
          width: 38%;
        }
        > span:nth-child(3) {
          width: 16%;
          padding-left: 6px;
        }
        > span:nth-child(4) {
          width: 16%;
          padding-left: 12px;
        }
        > span:nth-child(5) {
          width: 10%;
        }
      }
      > li:nth-child(even) {
        background: #153373;
      }

      > li:nth-child(1),
      > li:nth-child(3) {
        > span:nth-child(1) {
          color: #ed9d32;
        }
      }
    }
  }
}
</style>
