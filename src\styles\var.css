:root {
  --el-color-primary: #3f95ff; /* 主色调 */
  --el-text-color-primary: #505050; /* 文字色（一级标题，主要内容） */
  --el-color-warning: #ff7d1d; /* 提醒色（重要数据，提示类消息） */
  --el-color-success: #22cfa3; /* 成功色 */
  --el-color-danger: #d82c20; /* 警示色 */
  --el-font-size-base: 16px;

  --login-bg-color: #293146;

  --left-menu-max-width: 240px;

  --left-menu-min-width: 64px;

  --left-menu-bg-color: #001529;

  --left-menu-bg-light-color: #0f2438;

  --left-menu-bg-active-color: var(--el-color-primary);

  --left-menu-text-color: #bfcbd9;

  --left-menu-text-active-color: #fff;

  --left-menu-collapse-bg-active-color: var(--el-color-primary);
  /* left menu end */

  /* logo start */
  --logo-height: 50px;

  --logo-title-text-color: #fff;
  /* logo end */

  /* header start */
  --top-header-bg-color: '#fff';

  --top-header-text-color: 'inherit';

  --top-header-hover-color: #f6f6f6;

  --top-tool-height: var(--logo-height);

  --top-tool-p-x: 0;

  --tags-view-height: 35px;
  /* header start */

  /* tab menu start */
  --tab-menu-max-width: 80px;

  --tab-menu-min-width: 30px;

  --tab-menu-collapse-height: 36px;
  /* tab menu end */

  --app-content-padding: 20px;

  --app-content-bg-color: #f5f7f9;

  --app-footer-height: 30px;

  --transition-time-02: 0.2s;
}

.dark {
  --app-content-bg-color: var(--el-bg-color);
}

html,
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  font-size: 16px; /* 默认字体大小 */
  line-height: 22px; /* 默认行高 */
}
h1 {
  font-family: 'FZZZHongGBJW', 'Microsoft YaHei', Arial, sans-serif; /* 使用方正正中黑简体 */
  font-size: 24px;
  line-height: 24px;
}
h2 {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  font-weight: bold; /* 加粗 */
  font-size: 18px;
  line-height: 24px;
}

* {
  margin: 0;
  padding: 0;
}

li {
  list-style: none;
}

.el-select-dropdown__item.selected {
  color: #fff;
  background-color: #009cff;
}

.el-date-editor .el-range__icon,
.el-select .el-input .el-select__caret.el-icon {
  color: var(--el-color-primary);
}

.el-form-item--default {
  --font-size: 16px;
}
.el-table--default,
.el-breadcrumb {
  font-size: 16px !important;
}

.el-tree-node .el-select-dropdown__item.selected {
  color: var(--el-color-primary);
}

.el-table .el-popper {
  max-width: 500px;
}
