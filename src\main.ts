/*
 * @Description: main.ts
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-11-28 16:13:46
 * @LastEditTime: 2024-09-05 11:03:33
 */
// 引入unocss css
import '@/plugins/unocss'
// 导入全局的svg图标
import '@/plugins/svgIcon'
// 初始化多语言
import { setupI18n } from '@/plugins/vueI18n'
// 引入状态管理
import { setupStore } from '@/store'
// 全局组件
import { setupGlobCom } from '@/components'
// 引入 element-plus
import { setupElementPlus } from '@/plugins/elementPlus'
// 引入 form-create
import { setupFormCreate } from '@/plugins/formCreate'
// 引入全局样式
import '@/styles/index.scss'
// 引入动画
import '@/plugins/animate.css'
// 路由
import router, { setupRouter } from '@/router'
// 权限
import { setupAuth } from '@/directives'
import { createApp } from 'vue'
import App from './App.vue'
import './permission'
// import '@/plugins/tongji' // 百度统计
import Logger from '@/utils/Logger'
import VueDOMPurifyHTML from 'vue-dompurify-html' // 解决v-html 的安全隐患
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 去除谷歌浏览器的scroll、wheel等事件警告
;(function () {
  if (typeof EventTarget !== 'undefined') {
    let func = EventTarget.prototype.addEventListener
    EventTarget.prototype.addEventListener = function (type, fn, capture) {
      ;(this as any).func = func
      if (typeof capture !== 'boolean') {
        capture = capture || {}
        capture.passive = false
      }
      ;(this as any).func(type, fn, capture)
    }
  }
})()

// 创建实例
const setupAll = async () => {
  const app = createApp(App)
  // 挂载全局指令
  app.config.globalProperties.$echarts = echarts
  // 注册element-icon
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }

  await setupI18n(app)

  setupStore(app)

  setupGlobCom(app)

  setupElementPlus(app)

  setupFormCreate(app)

  setupRouter(app)

  setupAuth(app)

  await router.isReady()

  app.use(VueDOMPurifyHTML)

  app.mount('#app')
}

setupAll()

Logger.prettyPrimary(`欢迎使用`, import.meta.env.VITE_APP_TITLE)
