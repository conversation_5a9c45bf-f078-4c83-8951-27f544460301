/*
 * @Description: 考核完成率模块使用Echarts
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-14 11:51:31
 * @LastEditTime: 2024-08-15 14:12:24
 */
import { EChartsOption } from 'echarts'

const { t } = useI18n()

export const pieOptions: EChartsOption = {
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c}家 ({d}%)'
  },
  color: ['#0096fa', '#e9e9e9'],
  legend: {
    orient: 'vertical',
    left: 'left',
    data: ['完成率', '未完成率']
  },
  series: [
    {
      name: '考核完成率',
      type: 'pie',
      radius: '55%',
      center: ['50%', '60%'],
      data: [
        { value: 335, name: '完成率', label: { position: 'outside' } },
        { value: 310, name: '未完成率', label: { position: 'outside' } }
      ],
      label: {
        formatter: function (params) {
          // 根据 name 返回不同的颜色
          if (params.name === '完成率') {
            return `{blue|${params.name}\t${params.value}家\t${params.percent}%}`
          } else {
            return `${params.name}\t${params.value}家\t${params.percent}%`
          }
        },
        rich: {
          blue: {
            color: '#3ba1ff'
          }
        }
      }
    }
  ]
}

export const radarOption: EChartsOption = {
  radar: {
    // shape: 'circle',
    indicator: [
      { name: '季报', max: 100 },
      { name: '半年报', max: 100 },
      { name: '基础概况', max: 100 },
      { name: '专项工作', max: 100 },
      { name: '年报', max: 100 }
    ]
  },
  series: [
    {
      name: `xxx${t('workplace.index')}`,
      type: 'radar',
      data: [
        {
          value: [42, 30, 20, 35, 80]
        }
      ],
      label: {
        show: true,
        formatter: function (params) {
          return params.value + '%'
        }
      }
    }
  ]
}

export const barOptions: EChartsOption = {
  tooltip: {
    trigger: 'axis',
    formatter: function (params) {
      let result = params[0].name + '<br />' // 显示地区名 (t2)
      params.forEach((item) => {
        result += `<span style="display:inline-block;margin-bottom: 3px;margin-right:3px;
        border-radius:50%;width:5px;height:5px;background-color:#108ee9;"></span>` // 添加蓝色圆点
        result += ` ${item.value['t3']} 家<br />`
      })
      return result
    },
    axisPointer: {
      type: 'shadow'
    }
  },
  dataset: {
    dimensions: ['t2', 't3'],
    source: []
  },
  grid: {
    left: 50,
    right: 20,
    bottom: 20
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true
    },
    axisLabel: {
      interval: 0 // 强制显示所有标签
    }
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      type: 'bar'
    }
  ]
}
