import request from '@/config/axios'

// 考核企业注册信息 VO
export interface CompanyRegisterVO {
  id: number // 考核企业注册信息ID
  userId: number // 关联用户ID
  organizationalCode: string // 组织机构代码
  companyName: string // 单位名称
  legalRepresentativeName: string // 法人代表
  creditCode: string // 统一社会信用代码
  contactPhone: string // 注册手机号
  email: string // 注册邮箱地址
  areaId: string // 注册地址编码
  companyAddressDetail: string // 注册地址详细地址
  region: number // 所属区域
  industry: number // 所属行业
  managerPhone: string // 法人联系电话
  legalRepresentativePhone: string // 负责人联系电话
  account: string // 账号
  accountStatus: number // 账号状态
  establishmentDate: Date // 成立时间
  companyNature: number // 企业性质
  companyScale: number // 企业规模
  majorHazardSources: number // 是否有重大危险源
  nationalEconomyType: number // 国民经济类型
  involvement: string // 企业是否涉及
  totalEmployees: number // 员工总数
  specialOperationsPersonnel: number // 特种作业人员数量
  safetyManagementPersonnel: number // 专/兼职安全管理人员
  fixedAssets: number // 固定资产
  remarks: string // 备注
  managerUser: string // 负责人
  legalRepresentativeEmail: string // 法人代表邮箱
  hazardousMaterialUsage: string // 危化品使用量
}

// 考核企业注册信息 API
export const CompanyRegisterApi = {
  // 查询考核企业注册信息分页
  getCompanyRegisterPage: async (params: any) => {
    return await request.get({ url: `/system/company-register/page`, params })
  },

  // 查询考核企业注册信息详情
  getCompanyRegister: async (id: number) => {
    return await request.get({ url: `/system/company-register/get?id=` + id })
  },

  // 新增考核企业注册信息
  createCompanyRegister: async (data: CompanyRegisterVO) => {
    return await request.post({ url: `/system/company-register/create-sub-company`, data })
  },

  // 修改考核企业注册信息
  updateCompanyRegister: async (data: CompanyRegisterVO) => {
    return await request.put({ url: `/system/company-register/update`, data })
  },

  // 删除考核企业注册信息
  deleteCompanyRegister: async (id: number) => {
    return await request.delete({ url: `/system/company-register/delete?id=` + id })
  },

  // 导出考核企业注册信息 Excel
  exportCompanyRegister: async (params) => {
    return await request.download({ url: `/system/company-register/export-excel`, params })
  }
}
