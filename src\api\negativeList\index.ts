/*
 * @Description: Negative<PERSON><PERSON>
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-09-06 09:51:55
 * @LastEditTime: 2024-10-24 15:23:59
 */
import request from '@/config/axios'

// 负面清单 VO
export interface NegativeVO {
  id: number // 主键
  evaluatedUnit: string // 被考核单位
  evaluatedUnitName: string // 被考核单位
  content: string // 负面清单内容
  deductScore: number // 扣分
  attachments: string // 附件
  remark: string // 说明
}

// 负面清单 API
export const NegativeApi = {
  // 查询负面清单分页
  getNegativePage: async (params: any) => {
    return await request.get({ url: `/examine/negative/page`, params })
  },

  // 查询负面清单详情
  getNegative: async (id: number) => {
    return await request.get({ url: `/examine/negative/get?id=` + id })
  },

  // 新增负面清单
  createNegative: async (data: NegativeVO) => {
    return await request.post({ url: `/examine/negative/create`, data })
  },

  // 修改负面清单
  updateNegative: async (data: any) => {
    return await request.put({ url: `/examine/negative/update`, data })
  },

  // 删除负面清单
  deleteNegative: async (id: number) => {
    return await request.delete({ url: `/examine/negative/delete?id=` + id })
  },

  // 导出负面清单 Excel
  exportNegative: async (params) => {
    return await request.download({ url: `/examine/negative/export-excel`, params })
  },
}
