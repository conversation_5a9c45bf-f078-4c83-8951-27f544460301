<!--
 * @Description: 治本攻坚-治本攻坚考评情况
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-18 10:01:32
 * @LastEditTime: 2024-10-31 14:39:13
-->

<template>
  <ContentWrap>
    <div class="text-20px font-bold mb-20px">治本攻坚考评情况</div>
    <!-- 搜索工作栏 -->
    <template v-if="isAssessmentUnit">
      <el-form
        v-hasPermi="['root:cause:queryItem']"
        class="mb-5px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="80px"
      >
        <el-form-item label="考评时间" prop="quarter">
          <QuarterPicker
            class="!w-280px"
            v-model="queryParams.quarter"
            format="YYYY年第q季度"
            valueFormat="YYYY-Q"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" /> 搜索
          </el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        </el-form-item>
      </el-form>
      <el-row class="mb5 flex">
        <el-button v-hasPermi="['root:cause:create']" type="primary" @click="openForm">
          <Icon icon="ep:plus" class="mr-5px" />填 报
        </el-button>
        <el-button
          v-hasPermi="['root:cause:export']"
          class="position-absolute right-0"
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
        >
          <Icon class="mr-5px" icon="ep:upload" /> 导 出
        </el-button>
      </el-row>
    </template>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" align="center" type="index" width="60" />
      <el-table-column label="考评名称" align="center" prop="evaluationName" min-width="120">
        <template #default="{ row }">
          <el-link
            v-if="route.path === '/rootCause'"
            type="primary"
            :underline="false"
            @click="handleExamine('detail', row)"
          >
            {{ row.evaluationName }}
          </el-link>
          <span v-else>{{ row.evaluationName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="考评季度" align="center" prop="quarter" width="200">
        <template #default="{ row }">
          {{ row.quarter ? row.quarter.split('-').join('年第') + '季度' : '' }}
        </template>
      </el-table-column>
      <el-table-column v-if="isAssessmentUnit" label="备注" align="center" prop="remarks" />
      <!-- <template v-else>
        <el-table-column label="主要成效" prop="accomplishmentList">
          <template #default="{ row }">
            <template v-if="row.accomplishmentList.length > 0">
              <div v-for="(item, index) in row.accomplishmentList" :key="index" class="mb-5px">
                {{ index + 1 }}.{{ item }}
              </div>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="突出问题" prop="outstandingIssueList">
          <template #default="{ row }">
            <template v-if="row.outstandingIssueList.length > 0">
              <div v-for="(item, index) in row.outstandingIssueList" :key="index" class="mb-5px">
                {{ index + 1 }}.{{ item }}
              </div>
            </template>
          </template>
        </el-table-column>
      </template> -->
      <el-table-column
        label="考评时间"
        align="center"
        prop="submitTime"
        width="180"
        :formatter="dateFormatter2"
      />
      <template v-if="isAssessmentUnit">
        <el-table-column label="状态" align="center" prop="status" width="120px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.REPORT_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkPermi(['root:cause:oper'])"
          label="操作"
          align="center"
          :width="route.path === '/rootCause' ? '200' : '320'"
        >
          <template #default="scope">
            <!-- 只有治本攻坚菜单展示考评和提交按钮 -->
            <template v-if="scope.row.status === 0 && route.path === '/rootCause'">
              <el-button link type="primary" @click="handleExamine('edit', scope.row)">
                考评
              </el-button>
              <el-button link type="warning" @click="handleSubmit(scope.row.id)"> 提交 </el-button>
            </template>
            <!-- 只有考核过程的季度考评才展示 -->
            <template v-if="route.path === '/examineProcess/quarterExamine'">
              <el-button link type="primary" @click="comprehensiveEvaluation('edit', scope.row)">
                综合考评
              </el-button>
              <el-button link type="primary" @click="comprehensiveEvaluation('detail', scope.row)">
                综合考评详情
              </el-button>
              <el-button link type="warning" @click="handleOverall(scope.row)">
                总体情况
              </el-button>
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- 表单弹窗：添加/修改 -->
    <RootCauseForm ref="formRef" @success="getList" />
    <!-- 总体情况弹窗 -->
    <OverallDialog ref="overallDialogRef" summaryType="5" />
  </ContentWrap>
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { checkPermi } from '@/utils/permission'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { EvaluationApi, WorkEvaluationApi } from '@/api/rootCause'
import RootCauseForm from './rootCauseForm.vue'
import { useUserStore } from '@/store/modules/user'
import dayjs from 'dayjs'
import advancedFormat from 'dayjs/plugin/advancedFormat'

defineOptions({ name: 'RootCause' })
dayjs.extend(advancedFormat)
const resultFlag = inject('resultFlag', false)
const emit = defineEmits(['updateCurrentView'])
const message = useMessage() // 消息弹窗
const route = useRoute()
const router = useRouter()
const loading = ref(true) // 列表的加载中
const list = ref<any>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryFormRef = ref() // 搜索的表单
const queryParams: any = ref({
  pageNo: 1,
  pageSize: 10,
  evaluationName: undefined,
  quarter: undefined,
  remarks: undefined,
  evaluationTime: [],
  status: undefined,
  createTime: []
})
const userStore = useUserStore()
const roles = userStore.getRoles
const exportLoading = ref(false) // 导出的加载中
const isAssessmentUnit = computed(
  () => roles.includes('assessment_unit') || roles.includes('super_admin')
)
const overallDialogRef = ref()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    let data
    if (isAssessmentUnit.value) {
      const queryData = {
        ...queryParams.value
      }
      data = await EvaluationApi.getEvaluationPage(queryData)
    } else {
      const queryData = {
        ...queryParams.value,
        evaluationType: '0'
      }
      data = await WorkEvaluationApi.getWorkEvaluationPage(queryData)
    }
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const handleExamine = (flag, row) => {
  router.push({
    path: '/rootCauseDetail',
    query: {
      flag,
      quarter: row.quarter
    }
  })
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = () => {
  formRef.value.open()
}

/* 综合考评 */
const comprehensiveEvaluation = (flag, row) => {
  router.push({
    path: '/examineSituation',
    query: {
      quarter: row.quarter,
      flag,
      backPath: route.path
    }
  })
}

const handleSubmit = async (id) => {
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  await EvaluationApi.updateEvaluation({ id, status: 1 })
  message.success('操作成功')
  // 刷新列表
  await getList()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await WorkEvaluationApi.exportWorkEvaluation({
      ...queryParams.value,
      quarter: dayjs().format('YYYY-Q'),
      evaluationType: '0'
    })
    download.excel(data, '各区各部门工作考评情况.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const handleOverall = (row) => {
  overallDialogRef.value.openDialog(row.quarter)
}

const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(async () => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-link) {
  width: 100%;
  text-align: center;
}
</style>
