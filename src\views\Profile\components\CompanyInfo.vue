<!--
 * @Description: 
 * @Author: sunyunwu
 * @LastEditors: sunyunwu
 * @Date: 2024-06-26 08:52:52
 * @LastEditTime: 2024-06-28 13:15:10
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" :label-width="160">
    <el-form-item label="企业名称" prop="companyName">
      <el-input v-model="formData.companyName" placeholder="请输入企业名称" />
    </el-form-item>
    <el-form-item label="注册地址" prop="companyAddressDetail">
      <el-row :gutter="0" style="width: 100%">
        <el-col :span="10">
          <el-form-item prop="areaId">
            <el-cascader
              v-model="formData.areaId"
              :props="defaultProps"
              :options="areaList"
              clearable
              style="width: 100%"
              placeholder="请选择地区"
            />
          </el-form-item>
        </el-col>
        <el-col :span="13" :offset="1">
          <el-form-item prop="companyAddressDetail">
            <el-input
              style="width: 100%"
              v-model="formData.companyAddressDetail"
              placeholder="详细地址"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form-item>
    <el-form-item label="法人代表" prop="legalRepresentativeName">
      <el-input v-model="formData.legalRepresentativeName" placeholder="请输入法人代表" />
    </el-form-item>
    <el-form-item label="法人代表电话" prop="contactPhone">
      <el-input v-model="formData.contactPhone" placeholder="请输入法人代表电话" />
    </el-form-item>
    <el-form-item label="成立日期" prop="incorporationDate">
      <el-date-picker
        v-model="formData.incorporationDate"
        type="date"
        value-format="YYYY-MM-DD"
        placeholder="请选择成立日期"
        style="width: 100%"
      />
    </el-form-item>
    <el-form-item label="注册资金（万元）" prop="registeredCapital">
      <el-input-number
        v-model="formData.registeredCapital"
        :min="1"
        :max="99999999"
        style="width: 100%"
        controls-position="right"
        placeholder="请输入注册资金"
      />
    </el-form-item>
    <el-form-item
      label="工商注册号"
      prop="businessRegistrationNumber"
      placeholder="请输入工商注册号"
    >
      <el-input v-model="formData.businessRegistrationNumber" />
    </el-form-item>
    <el-form-item label="统一社会信用代码" prop="creditCode" placeholder="请输入统一社会信用代码">
      <el-input v-model="formData.creditCode" />
    </el-form-item>
    <el-form-item>
      <XButton :title="t('common.save')" type="primary" @click="submit(formRef)" />
      <XButton :title="t('common.reset')" type="danger" @click="reset(formRef)" />
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
import { getUserProfile, updateCompany, createCompany, getCompany } from '@/api/system/user/profile'
import { isContactWay } from '@/utils/validate'
import * as AreaApi from '@/api/system/area'
import { useUserStore } from '@/store/modules/user'
import { cloneDeep } from 'lodash-es'
defineOptions({ name: 'CompanyInfo' })
const defaultProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}
const userStore = useUserStore()
const areaList = ref([]) // 地区列表
const { t } = useI18n()
const message = useMessage()
const formRef = ref<FormInstance>()
const userId = userStore.getUser.id
const userParty = userStore.getUser.userParty
const formData: any = ref({})

const validateContact = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入联系电话'))
  }
  if (!isContactWay(value)) {
    callback(new Error('联系电话格式不正确'))
  } else {
    callback()
  }
}
const rules = reactive<FormRules>({
  companyName: [
    {
      required: true,
      message: '企业名称不能为空',
      trigger: 'blur'
    }
  ],

  legalRepresentativeName: [
    {
      required: true,
      message: '法人代表不能为空',
      trigger: 'blur'
    }
  ],
  contactPhone: [
    {
      required: true,
      validator: validateContact,
      trigger: 'blur'
    }
  ],
  creditCode: [
    {
      required: true,
      message: '统一社会信用代码不能为空',
      trigger: 'blur'
    }
  ],
  areaId: [{ required: true, message: '地区不能为空', trigger: ['blur', 'change'] }],
  companyAddressDetail: [{ required: true, message: '地址不能为空', trigger: 'blur' }],
  incorporationDate: [
    {
      required: true,
      message: '成立日期不能为空',
      trigger: 'change'
    }
  ],
  registeredCapital: [
    {
      required: true,
      message: '注册资金不能为空',
      trigger: 'blur'
    }
  ],
  businessRegistrationNumber: [
    {
      required: true,
      message: '工商注册号不能为空',
      trigger: 'blur'
    }
  ]
})

const submit = (formEl: FormInstance | undefined) => {
  console.log(formData)
  if (!formEl) return
  formEl.validate(async (valid) => {
    if (valid) {
      let data = cloneDeep(formData.value)

      data.areaId = data.areaId.join('/')
      data.userId = userId
      if (formData.value.userId) {
        await updateCompany(data)
        message.success(t('common.updateSuccess'))
      } else {
        await createCompany(data)
        message.success('创建成功')
      }
    }
  })
}

const reset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  Object.keys(formData.value).forEach((key) => {
    formData.value[key] = ''
  })
  formEl.resetFields()
}

onMounted(async () => {
  areaList.value = await AreaApi.getAreaTree()
  const data = await getUserProfile()
  /* if (!data.companyRegister) {
    let ndata = await getCompany({ userId:userId })

  } */
  if (data.companyRegister) {
    data.companyRegister.areaId = data.companyRegister.areaId
      ? data.companyRegister.areaId.split('/').map(Number)
      : []
  }

  formData.value = data.companyRegister
})
</script>
