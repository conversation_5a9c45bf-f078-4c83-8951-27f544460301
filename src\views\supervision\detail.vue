<!--
 * @Description: 督查督办上报、考核、详情页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-28 09:08:13
 * @LastEditTime: 2024-10-31 11:18:01
-->
<template>
  <ContentWrap>
    <div class="text-20px font-bold"> {{ flagMap[flag] }}</div>
    <el-divider></el-divider>
    <el-form
      ref="formRef"
      :rules="flag !== 'detail' ? formRules : undefined"
      :model="detailData"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="督查督办名称" prop="supervisionName">
        <div class="font-bold">{{ detailData.supervisionName }}</div>
      </el-form-item>
      <el-form-item label="督查督办内容" prop="content">
        <div class="font-bold">{{ detailData.content }}</div>
      </el-form-item>
      <el-form-item label="被考核单位" prop="evaluatedUnitName">
        <div class="font-bold">{{ detailData.evaluatedUnitName }}</div>
      </el-form-item>
      <el-form-item label="截止日期" prop="dueDate">
        {{ formatDate(detailData.dueDate, 'YYYY-MM-DD') }}
      </el-form-item>
      <!-- <el-form-item label="关联扣分规则" prop="relatedPenaltyRule">
        <dict-tag :type="DICT_TYPE.DEDUCTION_RULES" :value="detailData.relatedPenaltyRule" />
      </el-form-item> -->
      <el-form-item label="说明" prop="remark">
        <el-input
          class="!max-w-400px"
          v-if="flag !== 'detail' && flag !== 'examine'"
          clearable
          v-model="detailData.remark"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
        />
        <div v-else>{{ detailData.remark }}</div>
      </el-form-item>
      <el-form-item label="附件材料" prop="attachments">
        <UploadFile
          v-if="flag !== 'detail' && flag !== 'examine'"
          v-model="detailData.attachments"
          :file-size="100"
          :limit="5"
        />
        <FileListPreview v-else :fileUrl="detailData.attachments" />
      </el-form-item>
      <el-form-item label="考核结果" prop="completionStatus" v-if="flag !== 'report'">
        <el-radio-group v-model="detailData.completionStatus">
          <el-radio
            v-if="flag !== 'detail'"
            v-for="dict in getIntDictOptions(DICT_TYPE.EXAMINE_RESULTS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
          <dict-tag v-else :type="DICT_TYPE.EXAMINE_RESULTS" :value="detailData.completionStatus" />
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="handleClose" :disabled="formLoading">取 消</el-button>
        <el-button
          v-if="flag !== 'detail'"
          type="primary"
          @click="submitForm"
          :disabled="formLoading"
        >
          提 交
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { SupervisionTasksApi } from '@/api/supervision'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { cloneDeep } from 'lodash-es'

defineOptions({ name: 'SupervisionDetail' })

const flag = ref('') // report: 上报  examine: 考核  detail: 详情
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const route: any = useRoute() // 查询参数
const formLoading = ref(false) // 表单的加载中
const formRef = ref()
const detailData = ref<any>({}) // 详情数据
const formRules = reactive({
  completionStatus: [{ required: true, message: '必填项不能为空', trigger: 'blur' }]
})
const flagMap = {
  report: '上报信息',
  examine: '考核',
  detail: '详情'
}

/** 获得数据 */
const getInfo = async () => {
  formLoading.value = true
  try {
    detailData.value = await SupervisionTasksApi.getSupervisionTasks(route.query.id)
  } finally {
    formLoading.value = false
  }
}
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const submitData = cloneDeep(detailData.value)
    await SupervisionTasksApi.updateSupervisionTasks({
      ...submitData,
      status: submitData.status <= 3 ? submitData.status + 1 : submitData.status
    })
    message.success('操作成功')
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  push(route.query.backPath || '/supervision')
}

/** 初始化 **/
onMounted(async () => {
  await getInfo()
  if (route.query.optFlag) {
    flag.value = route.query.optFlag
  }
})
</script>
