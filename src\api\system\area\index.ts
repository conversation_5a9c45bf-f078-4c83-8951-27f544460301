import request from '@/config/axios'

// 获得地区树
export const getAreaTree = async () => {
  return await request.get({ url: '/system/area/tree' })
}

// 获得 IP 对应的地区名
export const getAreaByIp = async (ip: string) => {
  return await request.get({ url: '/system/area/get-by-ip?ip=' + ip })
}

// 获得 ID 对应的地区名
export const getAreaById = async (id: string) => {
  return await request.get({ url: '/system/area/get-by-id?id=' + id })
}

// 批量获取地区树
export const getAreaTreeList = async (data) => {
  return await request.post({ url: '/system/area/get-by-id-list', data })
}
