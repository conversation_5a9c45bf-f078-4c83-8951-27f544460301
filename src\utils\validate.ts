/**
 * @description 判读是否为外链
 * @param path
 * @returns {boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:|\/\/)/.test(path)
}
/**
 * @description 校验密码是否小于6位
 * @param value
 * @returns {boolean}
 */
export function isPassword(value) {
  return value.length >= 6
}

/**
 * @description 判断是否为数字
 * @param value
 * @returns {boolean}
 */
export function isNumber(value) {
  const reg = /^[0-9]*$/
  return reg.test(value)
}

/**
 * @description 判断是否是名称
 * @param value
 * @returns {boolean}
 */
export function isName(value) {
  const reg = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/
  return reg.test(value)
}

/**
 * @description 判断是否为IP
 * @param ip
 * @returns {boolean}
 */
export function isIP(ip) {
  const reg =
    /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
  return reg.test(ip)
}

/**
 * @description 判断是否是传统网站
 * @param url
 * @returns {boolean}
 */
export function isUrl(url) {
  const reg =
    /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
}

/**
 * @description 判断是否是小写字母
 * @param value
 * @returns {boolean}
 */
export function isLowerCase(value) {
  const reg = /^[a-z]+$/
  return reg.test(value)
}

/**
 * @description 判断是否是大写字母
 * @param value
 * @returns {boolean}
 */
export function isUpperCase(value) {
  const reg = /^[A-Z]+$/
  return reg.test(value)
}

/**
 * @description 判断是否是大写字母开头
 * @param value
 * @returns {boolean}
 */
export function isAlphabets(value) {
  const reg = /^[A-Za-z]+$/
  return reg.test(value)
}

/**
 * @description 判断是否是字符串
 * @param value
 * @returns {boolean}
 */
export function isString(value) {
  return typeof value === 'string' || value instanceof String
}

/**
 * @description 判断是否是数组
 * @param arg
 */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}

/**
 * @description 判断是否是端口号
 * @param value
 * @returns {boolean}
 */
export function isPort(value) {
  const reg =
    /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/
  return reg.test(value)
}

/**
 * @description 判断是否是手机号
 * @param value
 * @returns {boolean}
 */
export function isPhone(value) {
  const reg = /^1\d{10}$/
  return reg.test(value)
}

/**
 * @description 判断是否是身份证号(第二代)
 * @param value
 * @returns {boolean}
 */
export function isIdCard(value) {
  const reg =
    /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  return reg.test(value)
}

/**
 * @description 判断是否是邮箱
 * @param value
 * @returns {boolean}
 */
export function isEmail(value) {
  const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
  return reg.test(value)
}

/**
 * @description 判断是否是传真
 * @param value
 * @returns {boolean}
 */
// 010-26737123-888
export function isFax(value) {
  const reg = /^(?:\d{3,4}-)?\d{7,8}(?:-\d{1,6})?$/
  return reg.test(value)
}
/**
 * @description 判断是否中文
 * @param value
 * @returns {boolean}
 */
export function isChina(value) {
  const reg = /^[\u4E00-\u9FA5]{2,4}$/
  return reg.test(value)
}

/**
 * @description 判断是否为空
 * @param value
 * @returns {boolean}
 */
export function isBlank(value) {
  return (
    value === null ||
    false ||
    value === '' ||
    value() === '' ||
    value.toLocaleLowerCase()() === 'null'
  )
}

/**
 * @description 判断是否为固话
 * @param value
 * @returns {boolean}
 */
export function isTel(value) {
  const reg =
    /^(400|800)([0-9\\-]{7,10})|(([0-9]{4}|[0-9]{3})([- ])?)?([0-9]{7,8})(([- 转])*([0-9]{1,4}))?$/
  return reg.test(value)
}

/**
 * @description 判断是否为数字且最多两位小数
 * @param value
 * @returns {boolean}
 */
export function isNum(value) {
  const reg = /^\d+(\.\d{1,2})?$/
  return reg.test(value)
}
/**
 * @description 判断是否为企业信用代码
 * @param value
 * @returns {boolean}
 */
export function isCreditCode(value) {
  const reg = /^[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/g
  return reg.test(value)
}
/**
 * @description 判断是否为json
 * @param value
 * @returns {boolean}
 */
export function isJson(value) {
  if (typeof value === 'string') {
    const obj = JSON.parse(value)
    return !!(typeof obj === 'object' && obj)
  }
  return false
}

/**
 * @description 判断obj是否为一个整数
 * @param obj
 * @returns {boolean}
 */
export function isInteger(obj) {
  return Math.floor(obj) === obj
}
/*
 * 金额(小数)验证
 * 验证规则：不可为空，十进制位以上不能为0 , 整数位最多 `integer` 位  , 小数位最多 `decimal` 位, ##可为负数##。
 * str   验证字符串
 * decimal  验证几位小数，默认2位
 * integer  验证几位整数，默认9位
 */
export function validateDecimal(str, decimal = 2, integer = 9) {
  const reg = new RegExp(
    '^((^-?[1-9]\\d{0,' + (integer - 1) + '})|(^[0-9])){1}(\\.\\d{1,' + decimal + '})?$'
  )
  return reg.test(str)
}
/**
 * @description: 联系方式验证（固定电话 + 手机）
 * @author: duanjiefei
 */
export function isContactWay(str) {
  // 空值则校验通过，必填会有必填校验
  if (!str) return true
  // 固话
  const fixedLineTel = new RegExp('^(0[0-9]{2,3}-)?([2-9][0-9]{6,7})+(-[0-9]{1,4})?$'),
    // 手机
    mobile = /^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/

  return fixedLineTel.test(str) || mobile.test(str)
}
/*
 * @author: duanjiefei
 * 金额(小数)验证
 * 验证规则：不可为空，十进制位以上不能为0 , 整数位最多 `integer` 位  , 小数位最多 `decimal` 位, ##不可为负数##。
 * str   验证字符串
 * decimal  验证几位小数，默认2位
 * integer  验证几位整数，默认9位
 */
export function validateDecimalPositive(str, decimal = 2, integer = 9) {
  const reg = new RegExp(
    '^((^[1-9]\\d{0,' + (integer - 1) + '})|(^[0-9])){1}(\\.\\d{1,' + decimal + '})?$'
  )

  return reg.test(str)
}

/**
 * @description: 计算两个⽇期之间的天数
    date1  开始⽇期 yyyy-MM-dd
    date2  结束⽇期 yyyy-MM-dd
    如果⽇期相同返回⼀天开始⽇期⼤于结束⽇期，返回0
 * @author: huangzhaoqi
 */
export function getDaysBetween(date1, date2) {
  const startDate = Date.parse(date1)
  const endDate = Date.parse(date2)
  if (startDate > endDate) {
    return 0
  }
  if (startDate == endDate) {
    return 1
  }
  const days = (endDate - startDate) / (1 * 24 * 60 * 60 * 1000)
  return days
}


// 用户名 
export function isUsername(str) {
  const reg = new RegExp('^[a-zA-Z0-9]{6,16}$')
  return reg.test(str)
}

// 密码
export function isPwd(str) {
  const reg = new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$')
  return reg.test(str)
}