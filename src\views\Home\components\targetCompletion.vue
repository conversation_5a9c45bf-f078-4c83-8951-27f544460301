<template>
  <div class="flex flex-wrap gap-2">
    <div v-for="item in echartsList" class="b-1-#d7d7d7 flex px-25px py-5px w-190px h-132px">
      <div class="pt-15px w-80px">
        <div class="mb-15px text-16px font-bold">{{ item.title }}</div>
        <el-statistic :title="item.label" :value="statisticsEchartsData[item.props]">
          <template #suffix>
            <div class="text-12px">
              {{ item.unit }}
            </div>
          </template>
        </el-statistic>
      </div>
      <Echart
        v-if="item.hasCharts"
        class="mt-20px"
        :height="100"
        :width="100"
        :options="pieOptionsDataGroup[`pieOptionsData${item.id}`]"
      />

      <div
        v-else
        class="h-70px w-70px mt-35px mx-20px rounded-full f-c"
        :style="`background-color: rgba(${item.rgb});`"
      >
        <img class="w-55%" :src="getAssetUrl(item.props)" alt="" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { set, cloneDeep } from 'lodash-es'
import { pieOptions } from '../echarts-data'
import { getHomeEchartsData } from '@/api/statistics'

const pieOptionsDataGroup = ref<any>({
  pieOptionsData1: cloneDeep(pieOptions),
  pieOptionsData5: cloneDeep(pieOptions)
})
const statisticsEchartsData = ref<any>({
  basicOverview: undefined,
  quarterlyReport: undefined,
  semiAnnualReport: undefined,
  annualReport: undefined,
  specialWork: undefined,
  accidentReport: undefined
})
const echartsList = ref([
  {
    id: 1,
    title: '督查督办',
    props: 'basicOverview',
    label: '完成率',
    unit: '%',
    hasCharts: true,
    options: pieOptionsDataGroup.value.pieOptionsData1
  },
  {
    id: 4,
    title: '年报',
    props: 'annualReport',
    label: '已完成',
    unit: ' 份',
    hasCharts: false,
    rgb: '122, 133, 246'
  },
  {
    id: 5,
    title: '专项工作',
    props: 'specialWork',
    label: '完成率',
    unit: '%',
    hasCharts: true,
    options: pieOptionsDataGroup.value.pieOptionsData5
  },
  {
    id: 6,
    title: '事故数量',
    props: 'accidentReport',
    label: '已发生',
    unit: ' 起',
    hasCharts: false,
    rgb: '255, 69, 82'
  }
])
const getEchartsData = async (year?) => {
  const res = await getHomeEchartsData({ year })
  statisticsEchartsData.value = res
  const data1 = [
    { value: statisticsEchartsData.value.basicOverview, name: '已完成' },
    { value: 100 - statisticsEchartsData.value.basicOverview, name: '未完成' }
  ]
  const data5 = [
    { value: statisticsEchartsData.value.specialWork, name: '已完成' },
    { value: 100 - statisticsEchartsData.value.specialWork, name: '未完成' }
  ]
  set(pieOptionsDataGroup.value.pieOptionsData1, 'series[0].data', data1)
  set(pieOptionsDataGroup.value.pieOptionsData5, 'series[0].data', data5)
}

const getAssetUrl = (name) => {
  return new URL(`../../../assets/imgs/home/<USER>
}

defineExpose({
  getEchartsData
})
</script>

<style lang="scss" scoped>
:deep(.el-statistic__number) {
  font-size: 26px;
  font-weight: bold;
}
</style>
