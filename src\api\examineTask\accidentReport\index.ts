/*
 * @Description: accidentReport API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-23 10:57:24
 * @LastEditTime: 2024-10-23 16:06:22
 */
import request from '@/config/axios'

// 事故统计数据汇总 VO
export interface AccidentSummaryVO {
  id: number // 唯一标识
  region: string // 区域
  cityAreaType: string // 城区类型
  industrialAccidents: number // 工矿商贸事故起数
  industrialVictims: number // 工矿商贸事故人数
  roadAccidents: number // 道路交通事故起数
  roadVictims: number // 道路交通事故人数
  waterAccidents: number // 水上交通事故起数
  waterVictims: number // 水上交通事故人数
  railAccidents: number // 铁路交通事故起数
  railVictims: number // 铁路交通事故人数
  completionDate: Date // 完成日期
}

// 事故统计数据汇总 API
export const AccidentSummaryApi = {
  // 查询事故统计数据汇总分页
  getAccidentSummaryPage: async (params: any) => {
    return await request.get({ url: `/examine/accident-summary/page`, params })
  },

  // 查询事故统计数据汇总详情
  getAccidentSummary: async (id: number) => {
    return await request.get({ url: `/examine/accident-summary/get?id=` + id })
  },

  // 新增事故统计数据汇总
  createAccidentSummary: async (data: AccidentSummaryVO) => {
    return await request.post({ url: `/examine/accident-summary/create`, data })
  },

  // 修改事故统计数据汇总
  updateAccidentSummary: async (data: AccidentSummaryVO) => {
    return await request.put({ url: `/examine/accident-summary/update`, data })
  },

  // 新增修改事故统计数据汇总
  saveOrUpdateAccidentSummary: async (data: any) => {
    return await request.post({ url: `/examine/accident-summary/saveOrUpdate`, data })
  },

  // 删除事故统计数据汇总
  deleteAccidentSummary: async (id: number) => {
    return await request.delete({ url: `/examine/accident-summary/delete?id=` + id })
  },

  // 导出事故统计数据汇总 Excel
  exportAccidentSummary: async (params) => {
    return await request.download({ url: `/examine/accident-summary/export-excel`, params })
  },

  // 获得导入事故统计模板
  getImportTemplate: async (params) => {
    return await request.download({ url: `/examine/accident-summary/get-import-template`, params })
  },
  
  // 导入接口地址
  upUrl: '/examine/accident-summary/import'
}
