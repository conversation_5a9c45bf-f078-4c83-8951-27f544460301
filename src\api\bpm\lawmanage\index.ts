/*
 * @Description: 流程-内容管理相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-03-19 15:57:21
 * @LastEditTime: 2024-03-19 16:37:44
 */
import request from '@/config/axios'

export interface LawManageVO {
  id: number
  userId: number
  title: string
  documentNumber: string
  effectivenessLevel: string
  executionLevel: string
  type: number
  industry: number
  managementMethods: string
  interpret: string
  interpretAnnex: string
  implementationDate: any
  pushDate: any
  annex: string
  relatedRegulations: string
  state: number
  expireDate: any
  processInstanceId: string
}

// 查询LAW 内容管理审核分页
export const getLawManagePage = async (params) => {
  return await request.get({ url: `/bpm/law-manage/page`, params })
}

// 查询LAW 内容管理审核详情
export const getLawManage = async (id: number) => {
  return await request.get({ url: `/bpm/law-manage/get?id=` + id })
}

// 新增LAW 内容管理审核
export const createLawManage = async (data: LawManageVO) => {
  return await request.post({ url: `/bpm/law-manage/create`, data })
}

// 修改LAW 内容管理审核
export const updateLawManage = async (data: LawManageVO) => {
  return await request.put({ url: `/bpm/law-manage/update`, data })
}

// 删除LAW 内容管理审核
export const deleteLawManage = async (id: number) => {
  return await request.delete({ url: `/bpm/law-manage/delete?id=` + id })
}

// 导出LAW 内容管理审核 Excel
export const exportLawManage = async (params) => {
  return await request.download({ url: `/bpm/law-manage/export-excel`, params })
}
