/*
 * @Description: constants
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-11-28 16:13:46
 * @LastEditTime: 2024-09-05 15:29:35
 */
/**
 *
 * 枚举类
 */

// ========== COMMON 模块 ==========
// 是否
export const PermissionButtonEnum = {
  YES: 'true', // 是
  NO: 'false' // 否
}
// 全局通用状态枚举
export const CommonStatusEnum = {
  ENABLE: 0, // 开启
  DISABLE: 1 // 禁用
}

// 全局用户类型枚举
export const UserTypeEnum = {
  MEMBER: 1, // 会员
  ADMIN: 2 // 管理员
}

// ========== SYSTEM 模块 ==========
/**
 * 菜单的类型枚举
 */
export const SystemMenuTypeEnum = {
  DIR: 1, // 目录
  MENU: 2, // 菜单
  BUTTON: 3 // 按钮
}

/**
 * 角色的类型枚举
 */
export const SystemRoleTypeEnum = {
  SYSTEM: 1, // 内置角色
  CUSTOM: 2 // 自定义角色
}

/**
 * 数据权限的范围枚举
 */
export const SystemDataScopeEnum = {
  ALL: 1, // 全部数据权限
  DEPT_CUSTOM: 2, // 指定部门数据权限
  DEPT_ONLY: 3, // 部门数据权限
  DEPT_AND_CHILD: 4, // 部门及以下数据权限
  DEPT_SELF: 5 // 仅本人数据权限
}

/**
 * 用户的社交平台的类型枚举
 */
export const SystemUserSocialTypeEnum = {
  DINGTALK: {
    title: '钉钉',
    type: 20,
    source: 'dingtalk',
    img: 'https://s1.ax1x.com/2022/05/22/OzMDRs.png'
  },
  WECHAT_ENTERPRISE: {
    title: '企业微信',
    type: 30,
    source: 'wechat_enterprise',
    img: 'https://s1.ax1x.com/2022/05/22/OzMrzn.png'
  }
}

// ========== INFRA 模块 ==========
/**
 * 代码生成模板类型
 */
export const InfraCodegenTemplateTypeEnum = {
  CRUD: 1, // 基础 CRUD
  TREE: 2, // 树形 CRUD
  SUB: 3 // 主子表 CRUD
}

/**
 * 任务状态的枚举
 */
export const InfraJobStatusEnum = {
  INIT: 0, // 初始化中
  NORMAL: 1, // 运行中
  STOP: 2 // 暂停运行
}

/**
 * API 异常数据的处理状态
 */
export const InfraApiErrorLogProcessStatusEnum = {
  INIT: 0, // 未处理
  DONE: 1, // 已处理
  IGNORE: 2 // 已忽略
}

// ========== PAY 模块 ==========
/**
 * 支付渠道枚举
 */
export const PayChannelEnum = {
  WX_PUB: {
    code: 'wx_pub',
    name: '微信 JSAPI 支付'
  },
  WX_LITE: {
    code: 'wx_lite',
    name: '微信小程序支付'
  },
  WX_APP: {
    code: 'wx_app',
    name: '微信 APP 支付'
  },
  WX_BAR: {
    code: 'wx_bar',
    name: '微信条码支付'
  },
  ALIPAY_PC: {
    code: 'alipay_pc',
    name: '支付宝 PC 网站支付'
  },
  ALIPAY_WAP: {
    code: 'alipay_wap',
    name: '支付宝 WAP 网站支付'
  },
  ALIPAY_APP: {
    code: 'alipay_app',
    name: '支付宝 APP 支付'
  },
  ALIPAY_QR: {
    code: 'alipay_qr',
    name: '支付宝扫码支付'
  },
  ALIPAY_BAR: {
    code: 'alipay_bar',
    name: '支付宝条码支付'
  },
  MOCK: {
    code: 'mock',
    name: '模拟支付'
  }
}

/**
 * 支付的展示模式每局
 */
export const PayDisplayModeEnum = {
  URL: {
    mode: 'url'
  },
  IFRAME: {
    mode: 'iframe'
  },
  FORM: {
    mode: 'form'
  },
  QR_CODE: {
    mode: 'qr_code'
  },
  APP: {
    mode: 'app'
  }
}

/**
 * 支付类型枚举
 */
export const PayType = {
  WECHAT: 'WECHAT',
  ALIPAY: 'ALIPAY',
  MOCK: 'MOCK'
}

/**
 * 支付订单状态枚举
 */
export const PayOrderStatusEnum = {
  WAITING: {
    status: 0,
    name: '未支付'
  },
  SUCCESS: {
    status: 10,
    name: '已支付'
  },
  CLOSED: {
    status: 20,
    name: '未支付'
  }
}

// ========== MALL - 商品模块 ==========
/**
 * 商品 SPU 状态
 */
export const ProductSpuStatusEnum = {
  RECYCLE: {
    status: -1,
    name: '回收站'
  },
  DISABLE: {
    status: 0,
    name: '下架'
  },
  ENABLE: {
    status: 1,
    name: '上架'
  }
}

// ========== MALL - 营销模块 ==========
/**
 * 优惠劵模板的有限期类型的枚举
 */
export const CouponTemplateValidityTypeEnum = {
  DATE: {
    type: 1,
    name: '固定日期可用'
  },
  TERM: {
    type: 2,
    name: '领取之后可用'
  }
}

/**
 * 优惠劵模板的领取方式的枚举
 */
export const CouponTemplateTakeTypeEnum = {
  USER: {
    type: 1,
    name: '直接领取'
  },
  ADMIN: {
    type: 2,
    name: '指定发放'
  },
  REGISTER: {
    type: 3,
    name: '新人券'
  }
}

/**
 * 营销的商品范围枚举
 */
export const PromotionProductScopeEnum = {
  ALL: {
    scope: 1,
    name: '通用劵'
  },
  SPU: {
    scope: 2,
    name: '商品劵'
  },
  CATEGORY: {
    scope: 3,
    name: '品类劵'
  }
}

/**
 * 营销的条件类型枚举
 */
export const PromotionConditionTypeEnum = {
  PRICE: {
    type: 10,
    name: '满 N 元'
  },
  COUNT: {
    type: 20,
    name: '满 N 件'
  }
}

/**
 * 优惠类型枚举
 */
export const PromotionDiscountTypeEnum = {
  PRICE: {
    type: 1,
    name: '满减'
  },
  PERCENT: {
    type: 2,
    name: '折扣'
  }
}

// ========== MALL - 交易模块 ==========
/**
 * 分销关系绑定模式枚举
 */
export const BrokerageBindModeEnum = {
  ANYTIME: {
    mode: 1,
    name: '首次绑定'
  },
  REGISTER: {
    mode: 2,
    name: '注册绑定'
  },
  OVERRIDE: {
    mode: 3,
    name: '覆盖绑定'
  }
}
/**
 * 分佣模式枚举
 */
export const BrokerageEnabledConditionEnum = {
  ALL: {
    condition: 1,
    name: '人人分销'
  },
  ADMIN: {
    condition: 2,
    name: '指定分销'
  }
}
/**
 * 佣金记录业务类型枚举
 */
export const BrokerageRecordBizTypeEnum = {
  ORDER: {
    type: 1,
    name: '获得推广佣金'
  },
  WITHDRAW: {
    type: 2,
    name: '提现申请'
  }
}
/**
 * 佣金提现状态枚举
 */
export const BrokerageWithdrawStatusEnum = {
  AUDITING: {
    status: 0,
    name: '审核中'
  },
  AUDIT_SUCCESS: {
    status: 10,
    name: '审核通过'
  },
  AUDIT_FAIL: {
    status: 20,
    name: '审核不通过'
  },
  WITHDRAW_SUCCESS: {
    status: 11,
    name: '提现成功'
  },
  WITHDRAW_FAIL: {
    status: 21,
    name: '提现失败'
  }
}
/**
 * 佣金提现类型枚举
 */
export const BrokerageWithdrawTypeEnum = {
  WALLET: {
    type: 1,
    name: '钱包'
  },
  BANK: {
    type: 2,
    name: '银行卡'
  },
  WECHAT: {
    type: 3,
    name: '微信'
  },
  ALIPAY: {
    type: 4,
    name: '支付宝'
  }
}

/**
 * 配送方式枚举
 */
export const DeliveryTypeEnum = {
  EXPRESS: {
    type: 1,
    name: '快递发货'
  },
  PICK_UP: {
    type: 2,
    name: '到店自提'
  }
}
/**
 * 交易订单 - 状态
 */
export const TradeOrderStatusEnum = {
  UNPAID: {
    status: 0,
    name: '待支付'
  },
  UNDELIVERED: {
    status: 10,
    name: '待发货'
  },
  DELIVERED: {
    status: 20,
    name: '已发货'
  },
  COMPLETED: {
    status: 30,
    name: '已完成'
  },
  CANCELED: {
    status: 40,
    name: '已取消'
  }
}

export const ReviewStatusEnum = {
  // ========== 企业自评 1_00 ==========
  SUBMIT_BASE: '100', // 待提交_基础信息
  SUBMIT_MEMBER: '101', // 待提交_成员信息
  SUBMIT_SELF: '102', // 待提交_自评信息
  COMPANY_SELF: '103', // 待提交_附件信息
  // ========== 组织单位审核 2_00 ==========
  ASSOCIATION_REVIEW: '200', //组织单位待审核
  SUBMIT_SUP_BASE: '201', //待补充资料_基础信息
  SUBMIT_SUP_MEMBER: '202', // 待补充资料_成员信息
  SUBMIT_SUP_SELF: '203', // 待补充资料_自评信息
  SUBMIT_SUP_ATTACHMENT: '204', // 待补充资料_附件信息
  // ========== 定级部门确认 3_00 ==========
  GOVERNMENT_EDIT: '300', //定级部门确认组织部门审核 (签收、退回)
  ASSOCIATION_ASSIGN_THIRD_PARTY: '301', // 组织单位待指派评审单位
  ASSOCIATION_ANEW_THIRD_PARTY: '302', //  组织单位待重新指派评审单位
  // ========== 评审单位审核 4_00 ==========
  THIRD_PARTY_RECEIVED: '400', // 评审单位待审核 (通过、不符合申请条件、申请回避)
  THIRD_PARTY_ASSIGNED: '401', // 评审机构管理员待成立现场评审组,
  THIRD_PARTY_SET_PLAN: '402', // 评审组长待设置评审计划,
  THIRD_PARTY_WAIT_START: '403', // 待现场评审 // 是否达到拟申请等级 (否则已完成、是则进行整改)
  THIRD_PARTY_WAIT_SUBMIT: '404', // 待提交现场评审,
  COMPANY_WAIT_RECTIFY: '405', // 企业待整改
  THIRD_PARTY_RECTIFY_DOUBLE_CHECK: '406', // 评审单位待现场复核 // 评审单位现场复核 (通过、不通过)
  ASSOCIATION_RECTIFY_DOUBLE_CHECK: '407', // 组织单位待现场复核 // 组织单位现场复核 (通过、不通过)
  // ========== 已撤回/退回/重新安排评审 5_00 ==========
  REJECTED0: '501', // 企业已撤回
  REJECTED1: '502', // 组织单位待终止申请,
  REJECTED2: '503', // 定级部门已退回组织单位审核 //组织单位重新审核
  // ========== 已完成 6_00 ==========
  COMPLETED: '600', // 已完成
  TERM_APPL: '601', // 组织单位终止申请
  THIRD_PARTY_RECTIFY_AUDIT: '602', // 评审单位审核整改不通过
  ASSOCIATION_RECTIFY_AUDIT: '603' // 组织单位现场复核不通过
}

export const UserPartyEnum = {
  ENTERPRISE: '1', // 企业
  ORGUNIT: '2', // 组织单位
  RATEUNIT: '3', // 标准化定级部门
  REVIEWUNIT: '4' // 负责现场评审单位
}

export const NoticeEnum = {
  A01: 'A01', //材料补正公告",
  A02: 'A02', //终止申请公告
  A03: 'A03', //现场评审公告

  N01: 'N01', // 材料审核通知

  N02: 'N02', // 定级部门确认通知
  N03: 'N03', //组织单位待终止申请通知
  N04: 'N04', //补充材料通知

  N05: 'N05', //组织单位指派通知
  N06: 'N06', //定级部门退回通知

  N07: 'N07', //评审单位审核通知

  N08: 'N08', //成立现场评审组通知
  N09: 'N09', //评审单位申请退回通知
  N10: 'N10', //评审单位申请回避通知

  N11: 'N11', //设置评审计划通知
  N12: 'N12', //现场评审通知
  N13: 'N13', //终止申请公告
  N14: 'N14', // 整改
  N15: 'N15' // 延期
}

// 考核结果类型Map
export const ExamineResultTypeMap = {
  '0': 'basicScore',
  '1': 'accidentScore',
  '2': 'yearlyScore',
  '3': 'specialScore'
}

// 上海十六个区
export const ShanghaiDistrict = [
  '黄浦区',
  '徐汇区',
  '长宁区',
  '静安区',
  '普陀区',
  '虹口区',
  '杨浦区',
  '闵行区',
  '宝山区',
  '嘉定区',
  '浦东新区区',
  '金山区',
  '松江区',
  '青浦区',
  '奉贤区',
  '崇明区'
]
