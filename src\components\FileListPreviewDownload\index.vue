<!--
 * @Description: 文件列表预览下载组件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-05 15:43:42
 * @LastEditTime: 2024-11-05 15:50:52
-->

<template>
  <div class="flex flex-col items-start gap5" v-if="props.fileUrl">
    <el-tooltip v-for="(item, index) in fileList" :content="item.split('/').pop()" placement="top">
      <div class="flex">
        <el-link :key="index" type="primary" :underline="false">
          <Icon @click="handlePreview(item)" :size="22" icon="ep:document" />
        </el-link>

        <el-link class="ml-10px" :key="index" type="warning" :underline="false">
          <div class="flex items-center" @click="handleDownload(item)">
            <Icon class="ml-5px" :size="22" icon="ep:download" />
            下载
          </div>
        </el-link>
      </div>
    </el-tooltip>
    <!-- 文件预览 -->
    <PreviewDialog ref="previewDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { isArray } from '@/utils/is'

const props = defineProps({
  fileUrl: {
    type: [String, Array],
    required: true
  }
})
const fileList = ref<any>([])
const previewDialogRef = ref()
const handlePreview = (url) => {
  previewDialogRef.value.fileLoad(url)
}
const handleDownload = (item) => {
  window.open(item)
}

watch(
  () => props.fileUrl,
  (val) => {
    if (!val) return
    if (isArray(val)) {
      fileList.value = val
    } else {
      fileList.value = val.split(',')
    }
  },
  {
    immediate: true
  }
)
</script>
