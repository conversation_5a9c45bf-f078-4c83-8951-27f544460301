<template>
  <ContentWrap>
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center">
        <el-button @click="handleBack" type="info" plain class="mr-4">
          <Icon icon="ep:back" class="mr-1" />
          返回
        </el-button>
        <h1 class="text-xl font-semibold">申请改革创新加分申请表</h1>
      </div>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        v-loading="formLoading"
        :validate-on-rule-change="false"
      >
        <el-form-item label="项目名称" prop="projectName">
          <el-input
            v-model="formData.projectName"
            placeholder="请输入项目名称"
            :disabled="isDetail"
          />
        </el-form-item>
        <el-form-item label="主要工作情况" prop="mainWork">
          <el-input
            type="textarea"
            :autosize="{
              maxRows: 10,
              minRows: 5
            }"
            v-model="formData.mainWork"
            placeholder="请描述主要工作情况，300字内"
            :disabled="isDetail"
          />
        </el-form-item>
        <el-form-item label="主要创新点" prop="mainInnovations">
          <el-input
            type="textarea"
            :autosize="{
              maxRows: 10,
              minRows: 5
            }"
            v-model="formData.mainInnovations"
            placeholder="请描述主要创新点，400字内"
            :disabled="isDetail"
          />
        </el-form-item>

        <el-form-item label="附件" prop="attachment">
          <template v-if="isDetail">
            <FileListPreview v-if="formData.attachment" :fileUrl="formData.attachment" />
            <div v-else class="text-gray-400">暂无文件</div>
          </template>
          <UploadFile v-else v-model="formData.attachment" :fileType="['pdf', 'docx']" />
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input
            v-model="formData.contactPerson"
            placeholder="请输入联系人"
            :disabled="isDetail"
          />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactPhone">
          <el-input
            v-model="formData.contactPhone"
            placeholder="请输入联系方式"
            :disabled="isDetail"
            maxlength="11"
          />
        </el-form-item>

        <!-- 详情模式显示状态信息 -->
        <template v-if="isDetail">
          <el-form-item label="状态">
            <dict-tag :type="DICT_TYPE.REPORT_STATUS" :value="formData.status" />
          </el-form-item>
          <el-form-item label="创建时间" v-if="formData.createTime">
            {{ formatDate(formData.createTime) }}
          </el-form-item>
        </template>
      </el-form>

      <div class="flex justify-center mt-6 space-x-4">
        <el-button v-if="!isDetail" @click="handleSubmit" type="primary" :loading="submitLoading">
          {{ formType === 'create' ? '保存' : '更新' }}
        </el-button>
        <el-button @click="handleBack">
          {{ isDetail ? '返回' : '取消' }}
        </el-button>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ApplyApi, ApplyVO } from '@/api/examineTask/bonusApply'
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { useCache } from '@/hooks/web/useCache'

/** 考核奖金申报 页面表单 */
defineOptions({ name: 'ApplyFormPage' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const route = useRoute()
const router = useRouter()
const { wsCache } = useCache()

const formLoading = ref(false) // 表单的加载中：数据加载
const submitLoading = ref(false) // 提交按钮的加载状态
const formType = ref('') // 表单的类型：create - 新增；update - 修改；detail - 详情
const formData: any = ref({
  id: undefined,
  unitId: undefined,
  year: undefined,
  projectName: undefined,
  mainWork: undefined,
  attachment: undefined,
  contactPerson: undefined,
  contactPhone: undefined,
  mainInnovations: undefined,
  status: undefined,
  createTime: undefined
})

// 是否为详情模式
const isDetail = computed(() => formType.value === 'detail')

// 动态表单验证规则
const formRules = computed(() => {
  // 详情模式不需要验证
  if (isDetail.value) {
    return {}
  }

  return {
    projectName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
    contactPerson: [{ required: true, message: '联系人不能为空', trigger: 'blur' }],
    contactPhone: [{ required: true, message: '联系方式不能为空', trigger: 'blur' }]
  }
})

const formRef = ref() // 表单 Ref

/** 返回列表页 */
const handleBack = () => {
  router.back()
}

/** 初始化数据 */
const initData = async () => {
  const { type, id } = route.query
  formType.value = type as string

  // 设置年份
  formData.value.year = wsCache.get('year')

  if (id) {
    formLoading.value = true
    try {
      formData.value = await ApplyApi.getApply(Number(id))
      // 数据加载完成后，清除表单验证状态
      await nextTick()
      formRef.value?.clearValidate()
    } catch (error) {
      message.error('获取数据失败')
      handleBack()
    } finally {
      formLoading.value = false
    }
  } else if (type === 'create') {
    // 新增时重置表单
    resetForm()
  }
}

/** 提交表单 */
const handleSubmit = async () => {
  if (isDetail.value) return

  // 校验表单
  await formRef.value?.validate()

  // 提交请求
  submitLoading.value = true
  try {
    const data = formData.value as unknown as ApplyVO
    if (formType.value === 'create') {
      await ApplyApi.createApply(data)
      message.success(t('common.createSuccess'))
    } else {
      await ApplyApi.updateApply(data)
      message.success(t('common.updateSuccess'))
    }
    handleBack()
  } catch (error) {
    message.error('操作失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    unitId: undefined,
    year: wsCache.get('year'),
    projectName: undefined,
    mainWork: undefined,
    attachment: undefined,
    contactPerson: undefined,
    contactPhone: undefined,
    mainInnovations: undefined,
    status: undefined,
    createTime: undefined
  }
  nextTick(() => {
    formRef.value?.resetFields()
    formRef.value?.clearValidate()
  })
}

/** 初始化 **/
onMounted(() => {
  initData()
})
</script>
