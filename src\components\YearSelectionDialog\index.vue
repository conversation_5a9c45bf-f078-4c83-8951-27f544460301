<!--
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-07-21 16:53:36
 * @LastEditTime: 2025-07-25 14:54:00
-->
<template>
  <div v-if="dialogVisible" class="year-selection-mask">
    <div class="year-selection-dialog">
      <div class="dialog-header">
        <h3>选择年份</h3>
      </div>
      <div class="year-list">
        <div
          v-for="year in availableYears"
          :key="year"
          :class="['year-item', { 'current-year': year === currentYear, 'selected': year.toString() === selectedYear }]"
          @click="selectYear(year)"
        >
          {{ year }}
          <span v-if="year === currentYear" class="current-tag">当前年份</span>
        </div>
      </div>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache()
const dialogVisible = ref(false)
const selectedYear = ref(new Date().getFullYear().toString())
const currentYear = new Date().getFullYear()

// 生成从2025年到当前年份的年份列表
const availableYears = computed(() => {
  const years = []
  for (let year = currentYear; year >= 2025; year--) {
    years.push(year)
  }
  return years
})

const openDialog = () => {
  dialogVisible.value = true
}

const selectYear = (year: number) => {
  selectedYear.value = year.toString()
}

const handleConfirm = () => {
  wsCache.set('year', selectedYear.value)
  dialogVisible.value = false
  window.location.href = '/'
}

defineExpose({
  openDialog
})
</script>

<style scoped lang="scss">
.year-selection-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.year-selection-dialog {
  background: #fff;
  border-radius: 8px;
  width: 350px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dialog-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  text-align: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
}

.year-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px 0;
}

.year-item {
  padding: 12px 20px;
  cursor: pointer;
  font-size: 16px;
  text-align: center;
  transition: all 0.2s;
  position: relative;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  &.current-year {
    color: #409eff;
    font-weight: 600;
  }
  
  &.selected {
    background-color: #ecf5ff;
    color: #409eff;
    font-weight: 600;
  }
  
  &.selected.current-year {
    background-color: #ecf5ff;
  }
}

.current-tag {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #409eff;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.dialog-footer {
  padding: 20px;
  text-align: center;
  border-top: 1px solid #eee;
}
</style>
