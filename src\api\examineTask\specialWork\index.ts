/*
 * @Description: 专项工作
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-31 14:06:14
 * @LastEditTime: 2024-08-05 11:29:31
 */

import request from '@/config/axios'

// 专项工作 VO
export interface WorkVO {
  id: number // 主键(自增策略)
  unitId: number // 被考核单位
  workName: string // 专项工作名称
  issuanceTime: Date // 下发时间
  submitTime: Date // 填写时间
  status: number // 状态  0-未提交 1-已提交
}

// 专项工作 API
export const WorkApi = {
  // 查询专项工作分页
  getWorkPage: async (params) => {
    return await request.get({ url: `/system/special/work/page`, params })
  },

  // 查询专项工作详情
  getWork: async (id: number) => {
    return await request.get({ url: `/system/special/work/get?id=` + id })
  },

  // 新增专项工作
  createWork: async (data: WorkVO) => {
    return await request.post({ url: `/system/special/work/create`, data })
  },

  // 修改专项工作
  updateWork: async (data: any) => {
    return await request.put({ url: `/system/special/work/update`, data })
  },

  // 删除专项工作
  deleteWork: async (id: number) => {
    return await request.delete({ url: `/system/special/work/delete?id=` + id })
  },

  // 导出专项工作 Excel
  exportWork: async (params) => {
    return await request.download({ url: `/system/special/work/export-excel`, params })
  }
}

// 专项工作详情 API
export const WorkDetailApi = {
  // 查询专项工作详情分页
  getWorkDetailPage: async (params) => {
    return await request.get({ url: `/system/special/work/detail/page`, params })
  },

  // 查询专项工作详情
  getWorkDetail: async (id: number) => {
    return await request.get({ url: `/system/special/work/detail/get?id=` + id })
  },

  // 新增专项工作详情
  createWorkDetail: async (data: any) => {
    return await request.post({ url: `/system/special/work/detail/create`, data })
  },

  // 修改专项工作详情
  updateWorkDetail: async (data: any) => {
    return await request.put({ url: `/system/special/work/detail/update`, data })
  },

  // 删除专项工作
  deleteWorkDetail: async (id: number) => {
    return await request.delete({ url: `/system/special/work/detail/delete?id=` + id })
  },

  // 导出专项工作 Excel
  exportWorkDetail: async (params) => {
    return await request.download({ url: `/system/special/work/detail/export-excel`, params })
  }
}
