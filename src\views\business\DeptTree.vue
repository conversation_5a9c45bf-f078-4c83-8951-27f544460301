<!--
 * @Description: DeptTree
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-02 16:23:45
 * @LastEditTime: 2024-08-09 10:39:15
-->
<template>
  <div class="head-container">
    <el-input v-model="deptName" class="mb-20px" clearable placeholder="请输入部门名称">
      <template #prefix>
        <Icon icon="ep:search" />
      </template>
    </el-input>
  </div>
  <div class="head-container">
    <el-tree
      ref="treeRef"
      :data="deptList"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :props="defaultProps"
      default-expand-all
      highlight-current
      node-key="id"
      @node-click="handleNodeClick"
    />
  </div>
</template>

<script lang="ts" setup>
import { ElTree } from 'element-plus'
import * as DeptApi from '@/api/system/dept'
import { defaultProps, handleTree } from '@/utils/tree'

defineOptions({ name: 'SystemUserDeptTree' })

const resultFlag = inject('resultFlag', false)
const deptName = ref('')
const deptList = ref<Tree[]>([]) // 树形结构
const treeRef = ref<InstanceType<typeof ElTree>>()

/** 获得部门树 */
const getTree = async () => {
  let res
  if (resultFlag) {
    const sessionObj: any = JSON.parse(sessionStorage.getItem('examineResult') as any)
    res = await DeptApi.getChildDeptList({ id: sessionObj.unitId, hasParent: true })
    if (res.length > 0) {
      emits('node-click', res[0])
      nextTick(() => {
        treeRef.value?.setCurrentKey(res[0].id)
      })
    }
  } else {
    res = await DeptApi.getSimpleDeptList()
  }
  deptList.value = []
  deptList.value.push(...handleTree(res))
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 处理部门被点击 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  emits('node-click', row)
}
const emits = defineEmits(['node-click'])

/** 监听deptName */
watch(deptName, (val) => {
  treeRef.value!.filter(val)
})

const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(() => {
  getTree().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getTree()
  }
})
</script>
