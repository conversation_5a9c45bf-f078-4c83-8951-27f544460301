<!--
 * @Description: 工作季度考评情况填报
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-18 10:01:32
 * @LastEditTime: 2024-10-18 10:35:01
-->

<template>
  <Dialog title="编辑" v-model="dialogVisible" center top="5vh" width="800">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="考评名称" prop="evaluationName">
        <el-input v-model="formData.evaluationName" placeholder="请输入考评名称" />
      </el-form-item>
      <el-form-item label="考评季度" prop="quarter">
        <QuarterPicker
          class="!w-full"
          v-model="formData.quarter"
          format="YYYY年第q季度"
          valueFormat="YYYY-Q"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 6 }"
          v-model="formData.remark"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { EvaluationApi } from '@/api/rootCause'
import { cloneDeep } from 'lodash-es'

/** 考核模板 表单 */
defineOptions({ name: 'RootCauseForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData: any = ref({
  id: undefined,
  evaluationName: undefined,
  quarter: undefined,
  remarks: undefined,
  evaluationTime: undefined,
  status: undefined
})
const formRules = reactive({
  evaluationName: [{ required: true, message: '考评名称不能为空', trigger: 'blur' }],
  quarter: [{ required: true, message: '考评季度不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  resetForm()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const submitData = cloneDeep(formData.value)
    await EvaluationApi.createEvaluation(submitData)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    evaluationName: undefined,
    quarter: undefined,
    remarks: undefined,
    evaluationTime: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}
</script>
