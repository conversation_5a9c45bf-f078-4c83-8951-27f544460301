<template>
  <Dialog title="督查督办" v-model="dialogVisible" center top="5vh" width="800">
    <div class="px-30px pb-10px" v-if="tableData?.length">
      <div class="text-16px flex flex-col gap3" v-for="(item, index) in tableData">
        <div>督查督办名称：{{ item.supervisionName }}</div>
        <div>督查督办内容：{{ item.content }}</div>
        <div>截止日期： {{ formatDate(item?.dueDate, 'YYYY-MM-DD') }}</div>
        <div class="flex">附件内容： <FileListPreviewDownload :fileUrl="item.attachments" /></div>
        <div>上报说明：{{ item.remark }}</div>
        <div>完成时间：{{ formatDate(item?.createTime, 'YYYY-MM-DD') }}</div>
        <div> 考核结果： {{ getDictLabel(DICT_TYPE.EXAMINE_RESULTS, item.completionStatus) }} </div>
        <el-divider v-if="index !== tableData.length - 1"></el-divider>
      </div>
    </div>
    <el-empty v-else></el-empty>
  </Dialog>
</template>
<script setup lang="ts">
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { SupervisionTasksApi } from '@/api/supervision'

/** 考核模板 表单 */
defineOptions({ name: 'SupervisionForm' })

const props = defineProps({
  year: {
    type: Number
  }
})

const tableData: any = ref([])
const dialogVisible = ref(false) // 弹窗的是否展示

/** 打开弹窗 */
const open = async (row) => {
  try {
    let queryData = {
      pageSize: -1,
      pageNo: 1,
      assessmentYear: props.year,
      evaluatedUnit: row.unitId,
      status: 4
    }
    const data = await SupervisionTasksApi.getSupervisionTasksPage(queryData)
    tableData.value = data.list
  } finally {
    dialogVisible.value = true
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
