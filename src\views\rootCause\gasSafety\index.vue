<!--
 * @Description: 燃气安全专项治理燃气管网改造进度
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-18 10:59:04
 * @LastEditTime: 2024-10-31 13:40:54
-->

<template>
  <ContentWrap>
    <el-row class="mb4" v-if="roles.includes('assessment_unit')">
      <template v-if="flag !== 'detail'">
        <el-button type="warning" plain @click="handleImport">
          <Icon icon="ep:download" />
          导入
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:upload" />导出
        </el-button>
        <el-button type="primary" plain @click="handleSetKeyword"> 设置关键字 </el-button>
      </template>
      <el-button type="warning" plain @click="handleOverall"> 总体情况 </el-button>
    </el-row>
    <el-table v-loading="loading" :data="list" :row-class-name="handleRowClass">
      <!-- show-summary
       :summary-method="getSummaries" -->
      <el-table-column label="序号" align="center" type="index" width="60" />
      <el-table-column label="区域" align="center" prop="unitName" />
      <el-table-column label="道路管" align="center">
        <el-table-column label="实施量(公里)" align="center" prop="roadPipesImplemented">
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.roadPipesImplemented"
              :min="0"
            />
            <span v-else>{{ row.roadPipesImplemented }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实施量占计划量比例" align="center" prop="roadPipesRatio">
          <template #default="{ row }">
            <template v-if="row.isEditing">
              <el-input-number
                controls-position="right"
                v-model="row.editData.roadPipesRatio"
                :min="0"
                :max="100"
              />%
            </template>
            <span v-else>{{ row.roadPipesRatio !== null ? row.roadPipesRatio + '%' : '' }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="街坊管" align="center">
        <el-table-column label="实施量(公里)" align="center" prop="streetPipesImplemented">
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.streetPipesImplemented"
              :min="0"
            />
            <span v-else>{{ row.streetPipesImplemented }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实施量占计划量比例" align="center" prop="streetPipesRatio">
          <template #default="{ row }">
            <template v-if="row.isEditing">
              <el-input-number
                controls-position="right"
                v-model="row.editData.streetPipesRatio"
                :min="0"
                :max="100"
              />%
            </template>
            <span v-else>{{
              row.streetPipesRatio !== null ? row.streetPipesRatio + '%' : ''
            }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="地上管改造(户)" align="center" prop="aboveGroundTransformed">
        <template #default="{ row }">
          <el-input-number
            controls-position="right"
            v-if="row.isEditing"
            v-model="row.editData.aboveGroundTransformed"
            :min="0"
          />
          <span v-else>{{ row.aboveGroundTransformed }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="flag !== 'detail'" label="操作" align="center" width="200">
        <template #default="{ row }">
          <el-button v-if="!row.isEditing" link type="primary" @click="handlEdit(row)">
            编辑
          </el-button>
          <template v-else>
            <el-button link type="warning" @click="handlCancel(row)"> 取消 </el-button>
            <el-button link type="success" @click="handlConfirm(row)"> 确定 </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- 导入弹窗 -->
  <ImportForm
    ref="importFormRef"
    @success="getList"
    @importTemplate="importTemplate"
    :upUrl="GasPipelineProgressApi.upUrl"
  />
  <!-- 设置关键词弹窗 -->
  <KeyWordForm ref="formRef" dict-type="examine_gas_pipeline_progress" />
  <!-- 总体情况弹窗 -->
  <OverallDialog ref="overallDialogRef" :quarter="route.query.quarter" summaryType="3" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { GasPipelineProgressApi } from '@/api/rootCause/gasSafety'
import { cloneDeep } from 'lodash-es'
import KeyWordForm from '../KeyWordForm.vue'
import { useUserStore } from '@/store/modules/user'

defineProps({
  flag: {
    type: String
  }
})
defineOptions({ name: 'GasSafety' })
const roles = useUserStore().getRoles
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const formRef = ref()
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list = ref<any>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams: any = reactive({
  unitId: undefined,
  unitName: undefined,
  roadPipesImplemented: undefined,
  roadPipesRatio: undefined,
  streetPipesImplemented: undefined,
  streetPipesRatio: undefined,
  aboveGroundTransformed: undefined,
  status: undefined,
  quarter: route.query.quarter,
  createTime: []
})
const exportLoading = ref(false) // 导出的加载中
const overallDialogRef = ref()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const queryData = {
      ...queryParams
    }
    const data = await GasPipelineProgressApi.getGasPipelineProgressPage(queryData)
    list.value = data.list.map((item) => ({ ...item, isEditing: false }))
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const handlEdit = (row) => {
  row.editData = {
    roadPipesImplemented: row.roadPipesImplemented,
    roadPipesRatio: row.roadPipesRatio,
    streetPipesImplemented: row.streetPipesImplemented,
    streetPipesRatio: row.streetPipesRatio,
    aboveGroundTransformed: row.aboveGroundTransformed
  }
  row.isEditing = true
}
const handlCancel = (row) => {
  row.editData = {}
  row.isEditing = false
}
const handlConfirm = async (row) => {
  // 提交请求
  try {
    const submitData = {
      ...row.editData,
      id: row.id,
      quarter: route.query.quarter,
      unitId: row.unitId,
      unitName: row.unitName
    }
    await GasPipelineProgressApi.saveOrUpdateGasPipelineProgress(submitData)
    message.success('操作成功')
    await getList()
  } finally {
    row.isEditing = false
  }
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await GasPipelineProgressApi.exportGasPipelineProgress(queryParams)
    download.excel(data, '燃气安全专项治理燃气管网改造进度.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const importTemplate = async () => {
  const res = await GasPipelineProgressApi.getImportTemplate({ quarter: route.query.quarter })
  download.excel(res, '燃气安全专项治理燃气管网改造进度导入模板.xls')
}
/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open({ quarter: route.query.quarter })
}

// 计算合计
const getSummaries: any = (param) => {
  const { columns, data } = param
  const sums: (string | VNode)[] = []
  columns.forEach((column, index) => {
    if (index === 13) return
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    const values = data.map((item) => Number(item[column.property]))
    if (!values.every((value) => Number.isNaN(value))) {
      sums[index] = `${values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)}`
    } else {
      sums[index] = ''
    }
  })

  return sums
}

const handleRowClass = ({ row, rowIndex }) => {
  if (row.topFlag) return '!bg-#92d050'
  else if (row.bottomFlag) return '!bg-#ffff00'
}

const handleSetKeyword = () => {
  formRef.value.open()
}

const handleOverall = () => {
  overallDialogRef.value.openDialog()
}

const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(() => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>

<style lang="scss" scoped>
:deep() {
  .el-table {
    --el-table-row-hover-bg-color: unset;
  }
}
</style>
