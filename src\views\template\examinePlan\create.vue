<!--
 * @Description: 考核方案新增
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-29 16:53:51
 * @LastEditTime: 2025-07-25 16:16:26
-->

<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="120px"
    v-loading="formLoading"
  >
    <div class="text-22px pl-35px pb-25px font-bold">
      {{ flag === 'add' ? '新增' : flag === 'edit' ? '修改' : '查看' }}考核方案
    </div>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="方案名称" prop="examineSchemeTitle">
          <el-input
            :disabled="flag === 'detail'"
            v-model="formData.examineSchemeTitle"
            placeholder="请输入方案名称"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="绑定考核方式" prop="examineMethodId">
          <el-select
            :disabled="flag === 'detail'"
            class="w-100%"
            v-model="formData.examineMethodId"
            placeholder="请选择考核方式"
          >
            <el-option
              v-for="item in examineMethodList"
              :key="item.id"
              :label="item.examineMethod"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="考核项目" prop="examineItems">
          <el-select
            :disabled="flag === 'detail'"
            class="w-100%"
            v-model="formData.examineItems"
            placeholder="请选择考核项目"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.EXAMINE_ITEMS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" v-if="formData.examineItems && formData.examineItems !== 1">
      <el-col :span="8">
        <el-form-item label="考核年度" prop="introduceYear">
          <el-date-picker
            :disabled="flag === 'detail'"
            class="!w-100%"
            v-model="formData.introduceYear"
            type="year"
            placeholder="请选择考核年度"
            :disabled-date="disabledDate"
            value-format="YYYY"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" v-if="formData.examineItems && formData.examineItems === 1">
      <el-col :span="8">
        <el-form-item label="考核季度" prop="quarter">
          <QuarterPicker
            :disabled="flag === 'detail'"
            class="!w-full"
            ref="quarterPickerRef"
            v-model="formData.quarter"
            format="YYYY年第q季度"
            valueFormat="YYYY-Q"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="被考核单位" prop="examineUnitIds">
          <el-tree-select
            ref="treeRef"
            class="!w-full"
            :disabled="flag === 'detail'"
            v-model="formData.examineUnitIds"
            :data="deptList"
            multiple
            :default-expanded-keys="[101]"
            :props="defaultProps"
            node-key="id"
            placeholder="请选择部门"
            show-checkbox
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="考核指标" prop="examineTemplateId">
          <el-select
            :disabled="flag === 'detail'"
            class="w-100%"
            v-model="formData.examineTemplateId"
            placeholder="请选择考核指标"
            @change="getTemplateDetail"
          >
            <el-option
              v-for="item in examineTemplateList"
              :key="item.id"
              :label="item.templateName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" v-if="formData.examineTemplateId">
      <el-col :span="24">
        <el-table
          :data="tableData"
          :stripe="true"
          :show-overflow-tooltip="true"
          :span-method="objectSpanMethod"
          max-height="600"
        >
          <el-table-column type="index" label="序号" width="60"></el-table-column>
          <el-table-column label="评分标准任务" align="center">
            <el-table-column label="类型" align="center" prop="primaryElement" width="160" />
            <el-table-column label="指标名称" align="center" prop="secondaryElement" width="190" />
            <el-table-column label="评分标准" align="center" prop="thirdElement" />
            <el-table-column label="分值" align="center" prop="standardScore" width="150" />
            <el-table-column
              label="评分规则"
              align="center"
              prop="examineContent"
              min-width="300"
            />
          </el-table-column>
          <el-table-column label="附件" align="center" prop="annex" width="150">
            <template #default="scope">
              <FileListPreview :fileUrl="scope.row.annex" />
            </template>
          </el-table-column>
          <el-table-column label="内容描述" align="center" prop="remark" min-width="200" />
        </el-table>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="mt-5">
      <el-form-item>
        <el-button type="info" @click="handleClose" :disabled="formLoading">取 消</el-button>
        <el-button
          type="primary"
          v-if="flag !== 'detail'"
          @click="submitForm"
          :disabled="formLoading"
        >
          保 存
        </el-button>
      </el-form-item>
    </el-row>
  </el-form>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { defaultProps, handleTree } from '@/utils/tree'
import { SchemeApi } from '@/api/template/examinePlan'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { cloneDeep } from 'lodash-es'
import { MethodApi } from '@/api/template/examineMethod'
import * as DeptApi from '@/api/system/dept'
import { getTemplatePage } from '@/api/system/template'
import { TemplateEvaluationApi } from '@/api/template/checkTemplate'
import { getSpanArrays } from '@/utils/tableSpanMethod'

defineOptions({ name: 'ExaminePlanCreate' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const route = useRoute()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  examineSchemeTitle: undefined,
  examineMethodId: undefined,
  schemeStatus: undefined,
  introduceYear: undefined,
  examineUnitIds: undefined,
  examineTemplateId: undefined,
  quarter: undefined
})
const deptList = ref<any>([])
const examineMethodList = ref<any>([])
const examineTemplateList = ref<any>([])
const flag = ref<any>('add')
const formRules = reactive({
  examineSchemeTitle: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  examineMethodId: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  introduceYear: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  quarter: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  examineUnitIds: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  examineItems: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  examineTemplateId: [{ required: true, message: '必填项不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const treeRef = ref()

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const submitData = cloneDeep(formData.value)
    submitData.examineUnitIds = submitData.examineUnitIds.join(',')
    if (flag.value === 'add') {
      delete submitData.id
      await SchemeApi.createScheme(submitData)
      message.success(t('common.createSuccess'))
    } else {
      await SchemeApi.updateScheme(submitData)
      message.success(t('common.updateSuccess'))
    }
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  push('/template/examinePlan')
}

const fetchExamineMethodList = async () => {
  const data = await MethodApi.getMethodPage({
    pageNo: 1,
    pageSize: 100
  })
  examineMethodList.value = data.list
}
const fetchExamineTemplateList = async () => {
  const data = await getTemplatePage({
    pageNo: 1,
    pageSize: 100
  })
  examineTemplateList.value = data.list
}

const disabledDate = (time) => {
  // 获取今年
  const thisYear = new Date().getFullYear()
  // 获取选择的年份
  const year = time.getFullYear()
  // 如果选择的年份小于今年，则禁用该日期
  return year < thisYear
}

const tableData = ref<any>([])
const getTemplateDetail = async () => {
  try {
    const data = await TemplateEvaluationApi.getTemplateEvaluationPage({
      templateId: formData.value.examineTemplateId,
      pageSize: -1,
      pageNo: 1
    })
    tableData.value = data.list.map((item) => ({
      ...item.specialWorkDetailRespVO,
      primaryElement: item.primaryElement,
      secondaryElement: item.secondaryElement,
      thirdElement: item.thirdElement,
      examineContent: item.examineContent,
      templateQuotaId: item.id
    }))
    getSpanArr()
  } finally {
  }
}

// 存储各列的合并信息
const spanArrays: any = ref([])

// 定义每列的合并规则配置
const columnMergeRules = [
  // 第一列：类型 (primaryElement)
  ['primaryElement'],
  // 第二列：指标名称 (secondaryElement)
  ['primaryElement', 'secondaryElement'],
  // 第三列：评分标准 (thirdElement)
  ['primaryElement', 'secondaryElement', 'thirdElement'],
  // 第四列：分值 (standardScore)
  ['primaryElement', 'secondaryElement', 'standardScore'],
  // 第五列：评分规则 (examineContent)
  ['primaryElement', 'secondaryElement', 'thirdElement', 'examineContent']
]

const getSpanArr = () => {
  // 根据配置动态生成各列的合并信息
  spanArrays.value = getSpanArrays(tableData.value, columnMergeRules)
}

const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 只处理需要合并的列
  if (columnIndex >= 1 && columnIndex <= 5) {
    // 由于序号列不参与合并，需要调整索引
    const adjustedIndex = columnIndex - 1
    const spanArr = spanArrays.value[adjustedIndex]
    if (spanArr) {
      const _row = spanArr[rowIndex]
      const _col = _row > 0 ? 1 : 0
      return {
        rowspan: _row,
        colspan: _col
      }
    }
  }
  // 其他列不合并
  return {
    rowspan: 1,
    colspan: 1
  }
}

onMounted(async () => {
  flag.value = route.query.optFlag
  const deptRes = await DeptApi.getChildDeptList({ id: 101, hasParent: true })
  deptList.value = handleTree(deptRes, 'id')
  fetchExamineMethodList()
  fetchExamineTemplateList()
  if (route.query.id) {
    formData.value = await SchemeApi.getScheme(route.query.id as any)
    getTemplateDetail()
    formData.value.examineUnitIds = formData.value.examineUnitIds.split(',').map(Number)
    formData.value.introduceYear = formData.value.introduceYear + ''
    formData.value.examineItems = +formData.value.examineItems
  }
})
</script>
