<!--
 * @Description: 考核弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-08 09:40:45
 * @LastEditTime: 2024-09-05 14:32:04
-->
<template>
  <el-dialog
    center
    title="考核"
    v-model="dialogVisible"
    width="680px"
    append-to-body
    destroy-on-close
  >
    <el-form
      v-loading="formLoading"
      class="-mb-15px"
      :model="formData"
      ref="formRef"
      :inline="true"
      label-width="88px"
      :rules="formRules"
    >
      <el-form-item label="考核分数" prop="score">
        <el-input
          @input="(v) => (formData.score = v.replace(/[^\d.]/g, ''))"
          v-model="formData.score"
          placeholder="请输入考核分数"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">提 交</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ScoreApi } from '@/api/examineResult/resultScore'
import { cloneDeep } from 'lodash-es'
import { ExamineResultTypeMap } from '@/utils/constants'

defineOptions({ name: 'ExamineDialog' })

const props = defineProps({
  currentTabId: {
    type: String,
    default: ''
  }
})
const formRules = reactive({
  score: [{ required: true, message: '考核分数不能为空', trigger: 'blur' }]
})
const emit = defineEmits(['fetch-data'])
const dialogVisible = ref(false)
const message = useMessage() // 消息弹窗
const formLoading = ref(false)
const formData = ref<any>({
  score: undefined
})
const primaryKeyId = ref('')
const detailKeyId = ref('')
const formRef = ref() // 搜索的表单
const openDialog = async (row) => {
  primaryKeyId.value = row.id
  if (row.specialDetailId) {
    detailKeyId.value = row.specialDetailId
  }
  resetForm()
  dialogVisible.value = true
}

const handleSubmit = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const sessionObj: any = JSON.parse(sessionStorage.getItem('examineResult') as any)
    const primaryKey =
      props.currentTabId === '2' || props.currentTabId === '3' ? 'specialWorkId' : 'supervisionId'
    const submitData = {
      ...cloneDeep(formData.value),
      [primaryKey]: primaryKeyId.value,
      specialDetailId: detailKeyId.value ? detailKeyId.value : undefined,
      scoreStatus: 1,
      examineYear: sessionObj.examineYear,
      examineType: props.currentTabId,
      unitId: sessionObj.unitId,
      unitName: sessionObj.unitName,
      scoreToplimit: sessionObj[`${ExamineResultTypeMap[props.currentTabId]}Toplimit`]
    }
    await ScoreApi.addEditScore(submitData)
    dialogVisible.value = false
    message.success('提交成功')
    emit('fetch-data')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    score: undefined
  }
  formRef.value?.resetFields()
}
defineExpose({
  openDialog
})
</script>
