<!--
 * @Description: 总体情况弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-28 09:36:33
 * @LastEditTime: 2024-10-28 15:46:45
-->

<template>
  <el-dialog
    top="5vh"
    center
    title="填写总体情况"
    v-model="dialogVisible"
    width="1280px"
    append-to-body
    destroy-on-close
  >
    <div class="text-18px font-bold mb-10px">
      {{ realQuarter ? realQuarter?.split('-').join('年第') + '季度' : '' }}
    </div>
    <el-form
      v-loading="formLoading"
      class="-mb-15px"
      :model="formData"
      ref="formRef"
      label-width="88px"
      :rules="formRules"
      label-position="top"
    >
      <el-form-item label="一、全市总体情况" prop="citySummary">
        <el-input
          type="textarea"
          :autosize="{ minRows: 8, maxRows: 10 }"
          v-model="formData.citySummary"
          placeholder="请输入内容"
        />
      </el-form-item>
      <el-form-item label="二、各区各部门情况" prop="deptSummary">
        <el-input
          type="textarea"
          :autosize="{ minRows: 8, maxRows: 10 }"
          v-model="formData.deptSummary"
          placeholder="请输入内容"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="handleSubmit">提 交</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { OverviewApi } from '@/api/rootCause/overall'
import { cloneDeep } from 'lodash-es'

defineOptions({ name: 'ExamineDialog' })

const props = defineProps({
  summaryType: {
    type: String
  },
  quarter: {
    type: String
  }
})
const formRules = reactive({
  // score: [{ required: true, message: '考核分数不能为空', trigger: 'blur' }]
})
const realQuarter = ref()
const emit = defineEmits(['fetch-data'])
const dialogVisible = ref(false)
const message = useMessage() // 消息弹窗
const formLoading = ref(false)
const formData = ref<any>({
  citySummary: undefined,
  deptSummary: undefined
})
const formRef = ref() // 搜索的表单
const openDialog = async (q) => {
  resetForm()
  realQuarter.value = q || props.quarter
  const res = await OverviewApi.getOverviewPage({
    quarter: realQuarter.value,
    summaryType: props.summaryType
  })
  formData.value = res.list?.[0] || {
    citySummary: undefined,
    deptSummary: undefined
  }
  dialogVisible.value = true
}

const handleSubmit = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const submitData = {
      ...cloneDeep(formData.value),
      quarter: props.quarter,
      summaryType: props.summaryType
    }
    await OverviewApi.saveOrUpdateOverview(submitData)
    dialogVisible.value = false
    message.success('提交成功')
    emit('fetch-data')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    citySummary: undefined,
    deptSummary: undefined
  }
  formRef.value?.resetFields()
}
defineExpose({
  openDialog
})
</script>
