pipeline {
  agent {
    node {
      label 'nodejs'
    }

  }
  stages {
    stage('拉取阿里云效代码ts') {
      agent none
      steps {
        container('nodejs') {
          git(url: 'https://codeup.aliyun.com/bw/law/law-admin-web.git', credentialsId: 'aly-yunxiao-id', branch: 'master', changelog: true, poll: false)
          sh 'ls -al'
          sh 'ls -al'
        }

      }
    }

    stage('nodejs编译') {
      agent none
      steps {
        container('nodejs') {
          sh 'npm install --registry=https://registry.npm.taobao.org'
          sh 'npm run build:prod'
          sh 'ls'
        }

      }
    }

    stage('构建镜像') {
      agent none
      steps {
        container('nodejs') {
          sh 'ls'
          sh 'docker build -t law-admin-web:latest -f Dockerfile .'
        }

      }
    }

   stage('推送镜像至Harbor') {
      agent none
      steps {
        container('nodejs') {
          withCredentials([usernamePassword(credentialsId : 'harbor-id' ,passwordVariable : 'HARBOR_PWD_VAL' ,usernameVariable : 'HARBOR_USER_VAL' ,)]) {
            sh 'echo "$HARBOR_PWD_VAL" | docker login $REGISTRY -u "$HARBOR_USER_VAL" --password-stdin'
            sh 'docker tag law-admin-web:latest $REGISTRY/$DOCKERHUB_NAMESPACE/law-admin-web:SNAPSHOT-$BUILD_NUMBER'
            sh 'docker push $REGISTRY/$DOCKERHUB_NAMESPACE/law-admin-web:SNAPSHOT-$BUILD_NUMBER'
          }

        }

      }
    }

   stage('部署到生产环境') {
      agent none
      steps {
        kubernetesDeploy(configs: 'deploy/**', enableConfigSubstitution: true, kubeconfigId: "$KUBECONFIG_CREDENTIAL_ID")
      }
    }


  }
  environment {
    DOCKER_CREDENTIAL_ID = 'dockerhub-id'
    GITHUB_CREDENTIAL_ID = 'github-id'
    KUBECONFIG_CREDENTIAL_ID = 'demo-kubeconfig'
    REGISTRY = '*************:30002'
    DOCKERHUB_NAMESPACE = 'law'
    GITHUB_ACCOUNT = 'kubesphere'
    APP_NAME = 'devops-java-sample'
  }
  parameters {
    string(name: 'TAG_NAME', defaultValue: '', description: '')
  }
}