/**
 *
 * @param component 需要注册的组件
 * @param alias 组件别名
 * @returns any
 */
export const withInstall = <T>(component: T, alias?: string) => {
  const comp = component as any
  comp.install = (app: any) => {
    app.component(comp.name || comp.displayName, component)
    if (alias) {
      app.config.globalProperties[alias] = component
    }
  }
  return component as T & Plugin
}

/**
 * @param str 需要转下划线的驼峰字符串
 * @returns 字符串下划线
 */
export const humpToUnderline = (str: string): string => {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase()
}

/**
 * @param str 需要转驼峰的下划线字符串
 * @returns 字符串驼峰
 */
export const underlineToHump = (str: string): string => {
  if (!str) return ''
  return str.replace(/\-(\w)/g, (_, letter: string) => {
    return letter.toUpperCase()
  })
}

/**
 * 驼峰转横杠
 */
export const humpToDash = (str: string): string => {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase()
}

/**
 * 横杠转大驼峰
 */
export const dashToPascal = (str: string): string => {
  return str
    .split('-') // 按横杠分隔
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // 首字母大写，剩余字母小写
    .join('') // 组合成一个字符串
}

export const setCssVar = (prop: string, val: any, dom = document.documentElement) => {
  dom.style.setProperty(prop, val)
}

/**
 * 查找数组对象的某个下标
 * @param {Array} ary 查找的数组
 * @param {Functon} fn 判断的方法
 */
// eslint-disable-next-line
export const findIndex = <T = Recordable>(ary: Array<T>, fn: Fn): number => {
  if (ary.findIndex) {
    return ary.findIndex(fn)
  }
  let index = -1
  ary.some((item: T, i: number, ary: Array<T>) => {
    const ret: T = fn(item, i, ary)
    if (ret) {
      index = i
      return ret
    }
  })
  return index
}

export const trim = (str: string) => {
  return str.replace(/(^\s*)|(\s*$)/g, '')
}

/**
 * @param {Date | number | string} time 需要转换的时间
 * @param {String} fmt 需要转换的格式 如 yyyy-MM-dd、yyyy-MM-dd HH:mm:ss
 */
export function formatTime(time: Date | number | string, fmt: string) {
  if (!time) return ''
  else {
    const date = new Date(time)
    const o = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'H+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds(),
      'q+': Math.floor((date.getMonth() + 3) / 3),
      S: date.getMilliseconds()
    }
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    }
    for (const k in o) {
      if (new RegExp('(' + k + ')').test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
        )
      }
    }
    return fmt
  }
}

/**
 * 生成随机字符串
 */
export function toAnyString() {
  const str: string = 'xxxxx-xxxxx-4xxxx-yxxxx-xxxxx'.replace(/[xy]/g, (c: string) => {
    const r: number = (Math.random() * 16) | 0
    const v: number = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString()
  })
  return str
}

/**
 * 首字母大写
 */
export function firstUpperCase(str: string) {
  return str.toLowerCase().replace(/( |^)[a-z]/g, (L) => L.toUpperCase())
}

export const generateUUID = () => {
  if (typeof crypto === 'object') {
    if (typeof crypto.randomUUID === 'function') {
      return crypto.randomUUID()
    }
    if (typeof crypto.getRandomValues === 'function' && typeof Uint8Array === 'function') {
      const callback = (c: any) => {
        const num = Number(c)
        return (num ^ (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (num / 4)))).toString(
          16
        )
      }
      return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, callback)
    }
  }
  let timestamp = new Date().getTime()
  let performanceNow =
    (typeof performance !== 'undefined' && performance.now && performance.now() * 1000) || 0
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    let random = Math.random() * 16
    if (timestamp > 0) {
      random = (timestamp + random) % 16 | 0
      timestamp = Math.floor(timestamp / 16)
    } else {
      random = (performanceNow + random) % 16 | 0
      performanceNow = Math.floor(performanceNow / 16)
    }
    return (c === 'x' ? random : (random & 0x3) | 0x8).toString(16)
  })
}

/**
 * element plus 的文件大小 Formatter 实现
 *
 * @param row 行数据
 * @param column 字段
 * @param cellValue 字段值
 */
// @ts-ignore
export const fileSizeFormatter = (row, column, cellValue) => {
  const fileSize = cellValue
  const unitArr = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const srcSize = parseFloat(fileSize)
  const index = Math.floor(Math.log(srcSize) / Math.log(1024))
  const size = srcSize / Math.pow(1024, index)
  const sizeStr = size.toFixed(2) //保留的小数位数
  return sizeStr + ' ' + unitArr[index]
}

/* 常规文件改变 */
export const fileFormatter = (cellValue) => {
  const fileSize = cellValue
  const unitArr = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const srcSize = parseFloat(fileSize)
  const index = Math.floor(Math.log(srcSize) / Math.log(1024))
  const size = srcSize / Math.pow(1024, index)
  const sizeStr = size.toFixed(2) //保留的小数位数
  return sizeStr + ' ' + unitArr[index]
}

/**
 * 将值复制到目标对象，且以目标对象属性为准，例：target: {a:1} source:{a:2,b:3} 结果为：{a:2}
 * @param target 目标对象
 * @param source 源对象
 */
export const copyValueToTarget = (target, source) => {
  const newObj = Object.assign({}, target, source)
  // 删除多余属性
  Object.keys(newObj).forEach((key) => {
    // 如果不是target中的属性则删除
    if (Object.keys(target).indexOf(key) === -1) {
      delete newObj[key]
    }
  })
  // 更新目标对象值
  Object.assign(target, newObj)
}

/**
 * 将一个整数转换为分数保留两位小数
 * @param num
 */
export const formatToFraction = (num: number | string | undefined): number => {
  if (typeof num === 'undefined') return 0
  const parsedNumber = typeof num === 'string' ? parseFloat(num) : num
  return parseFloat((parsedNumber / 100).toFixed(2))
}

/**
 * 将一个数转换为 1.00 这样
 * 数据呈现的时候使用
 *
 * @param num 整数
 */
export const floatToFixed2 = (num: number | string | undefined): string => {
  let str = '0.00'
  if (typeof num === 'undefined') {
    return str
  }
  const f = formatToFraction(num)
  const decimalPart = f.toString().split('.')[1]
  const len = decimalPart ? decimalPart.length : 0
  switch (len) {
    case 0:
      str = f.toString() + '.00'
      break
    case 1:
      str = f.toString() + '0'
      break
    case 2:
      str = f.toString()
      break
  }
  return str
}

/**
 * 将一个分数转换为整数
 * @param num
 */
export const convertToInteger = (num: number | string | undefined): number => {
  if (typeof num === 'undefined') return 0
  const parsedNumber = typeof num === 'string' ? parseFloat(num) : num
  // TODO 分转元后还有小数则四舍五入
  return Math.round(parsedNumber * 100)
}

/**
 * 元转分
 */
export const yuanToFen = (amount: string | number): number => {
  return convertToInteger(amount)
}

/**
 * 分转元
 */
export const fenToYuan = (price: string | number): number => {
  return formatToFraction(price)
}

/**
 * 计算环比
 *
 * @param value 当前数值
 * @param reference 对比数值
 */
export const calculateRelativeRate = (value?: number, reference?: number) => {
  // 防止除0
  if (!reference) return 0

  return ((100 * ((value || 0) - reference)) / reference).toFixed(0)
}

/**
 * YYYY-MM 的 年-月 格式转换成 YYYY-Q 的 年-季度 格式
 *
 * @param value 当前数值
 * @param reference 对比数值
 */
export const convertToQuarter = (dateString) => {
  if (!dateString) return
  // 将输入的日期字符串分割成年和月
  const [year, month] = dateString?.split('-')?.map(Number)

  // 计算季度
  const quarter = Math.ceil(month / 3)

  // 返回格式化的季度字符串
  return `${year}-${quarter}`
}

// 定义允许的文件扩展名
const wordExtensions = ['doc', 'docx']
const excelExtensions = ['xls', 'xlsx']
const pptExtensions = ['ppt', 'pptx']

/**
 * 判断是否是 Word 文件
 * @param fileName 文件名
 * @returns 如果是 Word ，返回 true；否则返回 false
 */
export function isWordFile(fileName: string): boolean {
  // 获取文件扩展名
  const fileExtension = fileName.split('.').pop()?.toLowerCase()

  // 检查扩展名是否在允许的列表中
  if (!fileExtension) return false

  return wordExtensions.includes(fileExtension)
}

export function isExcelFile(fileName: string): boolean {
  // 获取文件扩展名
  const fileExtension = fileName.split('.').pop()?.toLowerCase()

  // 检查扩展名是否在允许的列表中
  if (!fileExtension) return false

  return excelExtensions.includes(fileExtension)
}
export function isPdfFile(fileName: string): boolean {
  // 获取文件扩展名
  const fileExtension = fileName.split('.').pop()?.toLowerCase()

  // 检查扩展名是否在允许的列表中
  if (!fileExtension) return false

  return fileExtension === 'pdf'
}

/**
 * 判断是否是 .docx 文件（仅支持新版本Word格式）
 * @param fileName 文件名
 * @returns 如果是 .docx 格式，返回 true；否则返回 false
 * @note vue-office-docx 组件仅支持 .docx 格式，不支持 .doc 格式
 */
export function isDocxFile(fileName: string): boolean {
  // 获取文件扩展名
  const fileExtension = fileName.split('.').pop()?.toLowerCase()

  // 检查扩展名是否为 docx
  if (!fileExtension) return false

  return fileExtension === 'docx'
}

/**
 * 判断是否是 .doc 文件（老版本Word格式）
 * @param fileName 文件名
 * @returns 如果是 .doc 格式，返回 true；否则返回 false
 * @note .doc 格式不支持在线预览，建议直接下载
 */
export function isDocFile(fileName: string): boolean {
  // 获取文件扩展名
  const fileExtension = fileName.split('.').pop()?.toLowerCase()

  // 检查扩展名是否为 doc
  if (!fileExtension) return false

  return fileExtension === 'doc'
}

/**
 * 判断是否是 PPT 文件
 * @param fileName 文件名
 * @returns 如果是 PPT ，返回 true；否则返回 false
 */
export function isPptFile(fileName: string): boolean {
  // 获取文件扩展名
  const fileExtension = fileName.split('.').pop()?.toLowerCase()

  // 检查扩展名是否在允许的列表中
  if (!fileExtension) return false

  return pptExtensions.includes(fileExtension)
}

/**
 * 判断是否是 .pptx 文件（仅支持新版本PPT格式）
 * @param fileName 文件名
 * @returns 如果是 .pptx 格式，返回 true；否则返回 false
 * @note vue-office-pptx 组件仅支持 .pptx 格式，不支持 .ppt 格式
 */
export function isPptxFile(fileName: string): boolean {
  // 获取文件扩展名
  const fileExtension = fileName.split('.').pop()?.toLowerCase()

  // 检查扩展名是否为 pptx
  if (!fileExtension) return false

  return fileExtension === 'pptx'
}

// 是否是图片链接
export function isImgPath(path: string): boolean {
  return /(https?:\/\/|data:image\/).*?\.(png|jpg|jpeg|gif|svg|webp|ico)/gi.test(path)
}
