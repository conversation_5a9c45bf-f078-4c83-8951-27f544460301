import request from '@/config/axios'

// 考核奖金申报 VO
export interface ApplyVO {
  id: number // 主键ID
  unitId: number // 被考核单位ID
  year: number // 年份
  projectName: string // 项目名称
  mainWork: string // 主要工作情况
  attachment: string // 附件
  contactPerson: string // 联系人
  contactPhone: string // 联系方式
}

// 考核奖金申报 API
export const ApplyApi = {
  // 查询考核奖金申报分页
  getApplyPage: async (params: any) => {
    return await request.get({ url: `/system/apply/page`, params })
  },

  // 查询考核奖金申报详情
  getApply: async (id: number) => {
    return await request.get({ url: `/system/apply/get?id=` + id })
  },

  // 新增考核奖金申报
  createApply: async (data: ApplyVO) => {
    return await request.post({ url: `/system/apply/create`, data })
  },

  // 修改考核奖金申报
  updateApply: async (data: ApplyVO) => {
    return await request.put({ url: `/system/apply/update`, data })
  },

  // 删除考核奖金申报
  deleteApply: async (id: number) => {
    return await request.delete({ url: `/system/apply/delete?id=` + id })
  },

  // 导出考核奖金申报 Excel
  exportApply: async (params) => {
    return await request.download({ url: `/system/apply/export-excel`, params })
  },
}
