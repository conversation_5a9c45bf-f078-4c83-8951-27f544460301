<!--
 * @Description: 考核方式
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-29 16:43:12
 * @LastEditTime: 2024-09-05 09:35:35
-->
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="98px"
    >
      <el-form-item label="方式名称" prop="examineMethod">
        <el-input
          v-model="queryParams.examineMethod"
          placeholder="请输入方式名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="考核频率" prop="examineFrequency">
        <el-select
          v-model="queryParams.examineFrequency"
          placeholder="请选择考核频率"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.ASSESSMENT_FREQUENCY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="考核单位" prop="examineUnitName">
        <el-input
          v-model="queryParams.examineUnitName"
          placeholder="请输入考核单位"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="考核形式" prop="assessmentFormat">
        <el-select
          v-model="queryParams.assessmentFormat"
          placeholder="请选择考核形式"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.ASSESSMENT_FORM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>

      <el-row :gutter="10" class="mb-8px">
        <el-form-item class="ml-5px">
          <el-button type="primary" plain @click="handleAddEdit('add')">
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" align="center" type="index" width="60" />
      <el-table-column label="方式名称" align="center" prop="examineMethod" />
      <el-table-column label="考核频率" align="center" prop="examineFrequency">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ASSESSMENT_FREQUENCY" :value="scope.row.examineFrequency" />
        </template>
      </el-table-column>
      <el-table-column label="考核单位" align="center" prop="examineUnitName" />
      <!-- <el-table-column label="考核人员" align="center" prop="examinersName" /> -->
      <el-table-column label="上限分" align="center" prop="upperLimitScore" />
      <el-table-column label="基础分" align="center" prop="baseScore" />
      <!-- <el-table-column label="事故分值占比" align="center" prop="accidentPercentage">
        <template #default="scope"> {{ scope.row.accidentPercentage }} % </template>
      </el-table-column>
      <el-table-column label="考核过程分值占比" align="center" prop="yearlyPercentage">
        <template #default="scope"> {{ scope.row.yearlyPercentage }} % </template>
      </el-table-column>
      <el-table-column label="治本攻坚分值占比" align="center" prop="specialPercentage">
        <template #default="scope"> {{ scope.row.specialPercentage }} % </template>
      </el-table-column>
      <el-table-column label="督查督办分值占比" align="center" prop="basicPercentage">
        <template #default="scope"> {{ scope.row.basicPercentage }} % </template>
      </el-table-column> -->
      <el-table-column label="汇总方式" align="center" prop="summaryMethod1">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SUMMARY_METHOD" :value="scope.row.summaryMethod1" />
        </template>
      </el-table-column>
      <el-table-column label="考核形式" align="center" prop="assessmentFormat">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ASSESSMENT_FORM" :value="scope.row.assessmentFormat" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template #default="scope">
          <el-button link type="primary" @click="handleAddEdit('edit', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { MethodApi } from '@/api/template/examineMethod'

defineOptions({ name: 'ExamineMethod' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter()
const loading = ref(true) // 列表的加载中
const list = ref<any>([]) // 列表的数据
const total = ref(0) // 列表的总页数

const queryParams: any = reactive({
  pageNo: 1,
  pageSize: 10,
  examineMethod: undefined,
  examineFrequency: undefined,
  examineUnitId: undefined,
  examineUnitName: undefined,
  examiners: undefined,
  examineWeight: undefined,
  baseScore: undefined,
  upperLimitScore: undefined,
  summaryMethod1: undefined,
  summaryMethod2: undefined,
  assessmentFormat: undefined,
  examineQuotaId: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MethodApi.getMethodPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 新增
const handleAddEdit = (type, id?) => {
  if (type === 'add') {
    push(`/template/examineMethodCreate`)
  } else {
    push(`/template/examineMethodCreate?id=${id}`)
  }
}

/** 搜索按钮操作 */
const handleQuery = async () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await MethodApi.deleteMethod(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await MethodApi.exportMethod(queryParams)
    download.excel(data, '考核方式.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(() => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-link) {
  width: 100%;
  text-align: center;
}
</style>
