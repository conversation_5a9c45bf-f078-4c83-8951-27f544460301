<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="108px">
      <el-form-item label="被考核单位" prop="unitName">
        <el-input
          v-model="queryParams.unitName"
          placeholder="请输入被考核单位"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="方案名称" prop="planName">
        <el-input
          v-model="queryParams.planName"
          placeholder="请输入方案名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" type="primary"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-row class="mb-2">
      <el-button v-if="!isExaminingUnit" type="primary" plain @click="openForm('create')">
        <Icon icon="ep:plus" class="mr-5px" /> 新增
      </el-button>
      <el-button type="success" plain @click="handleExport" :loading="exportLoading">
        <Icon icon="ep:download" class="mr-5px" /> 导出
      </el-button>
    </el-row>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="被考核单位" align="center" prop="unitName" />
      <el-table-column label="年份" align="center" prop="year" width="120" />
      <el-table-column label="方案名称" align="center" prop="planName" width="200" />
      <el-table-column label="报备方案" align="center" prop="reportPlan">
        <template #default="scope">
          <FileListPreview class="justify-center" :fileUrl="scope.row.reportPlan" />
        </template>
      </el-table-column>
      <el-table-column
        label="报备填报时间"
        align="center"
        prop="reportTime"
        :formatter="dateFormatter"
        width="185px"
      />
      <el-table-column label="报备状态" align="center" prop="reportStatus" width="110">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.REPORT_STATUS" :value="scope.row.reportStatus" />
        </template>
      </el-table-column>

      <el-table-column label="报备操作" align="center" v-if="!isExaminingUnit">
        <template #default="scope">
          <template v-if="scope.row.reportStatus === 0">
            <el-button link type="primary" @click="openForm('update', scope.row.id, 'report')">
              编辑
            </el-button>
            <el-button
              link
              type="success"
              @click="handleSubmit(scope.row.id, 'report')"
              :disabled="scope.row.reportStatus === 1"
            >
              提交
            </el-button>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="正式方案" align="center" prop="finalPlan">
        <template #default="scope">
          <template v-if="isExaminingUnit">
            <FileListPreview
              class="justify-center"
              v-if="scope.row.finalStatus === 1"
              :fileUrl="scope.row.finalPlan"
            />
            <div v-else>-</div>
          </template>

          <FileListPreview class="justify-center" v-else :fileUrl="scope.row.finalPlan" />
        </template>
      </el-table-column>
      <el-table-column
        label="正式填报时间"
        align="center"
        prop="finalTime"
        :formatter="dateFormatter"
        width="185px"
      >
        <template #default="scope">
          <template v-if="isExaminingUnit">
            <div v-if="scope.row.finalStatus === 1">{{ formatDate(scope.row.finalTime) }}</div>
            <div v-else>-</div>
          </template>
          <div v-else>{{ formatDate(scope.row.finalTime) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="正式填报状态" align="center" prop="finalStatus" width="130">
        <template #default="scope">
          <template v-if="isExaminingUnit">
            <dict-tag
              v-if="scope.row.finalStatus === 1"
              :type="DICT_TYPE.REPORT_STATUS"
              :value="scope.row.finalStatus"
            />
            <div v-else>-</div>
          </template>

          <template v-else>
            <dict-tag :type="DICT_TYPE.REPORT_STATUS" :value="scope.row.finalStatus" />
          </template>
        </template>
      </el-table-column>
      <el-table-column label="正式操作" align="center" v-if="!isExaminingUnit">
        <template #default="scope">
          <template v-if="scope.row.reportStatus === 1 && scope.row.finalStatus === 0">
            <el-button link type="primary" @click="openForm('update', scope.row.id, 'final')">
              编辑
            </el-button>
            <el-button link type="success" @click="handleSubmit(scope.row.id, 'final')">
              提交
            </el-button>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" v-else>
        <template #default="scope">
          <el-button link type="primary" @click="openForm('detail', scope.row.id, 'final')">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ReportForm ref="formRef" @success="getList" :year="wsCache.get('year')" />
</template>

<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter, formatDate } from '@/utils/formatTime'
import download from '@/utils/download'
import { ReportApi, ReportVO } from '@/api/examineTask/schemeReport'
import ReportForm from './ReportForm.vue'
import { useCache } from '@/hooks/web/useCache'

/** 考核方案报备 列表 */
defineOptions({ name: 'Report' })

const route = useRoute()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { wsCache } = useCache()
const loading = ref(true) // 列表的加载中
const list = ref<ReportVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  unitId: undefined,
  unitName: undefined,
  year: undefined,
  planName: undefined,
  reportPlan: undefined,
  reportTime: [],
  reportStatus: undefined,
  finalPlan: undefined,
  finalTime: [],
  finalStatus: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 计算属性：是否为考核单位 */
const isExaminingUnit = computed(() => {
  return route.path === '/examineProcess/schemeReport'
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const year = wsCache.get('year')
    const data = await ReportApi.getReportPage({
      ...queryParams,
      year,
      reportStatus: isExaminingUnit.value ? 1 : queryParams.reportStatus
    })
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number, operationType?: 'report' | 'final') => {
  formRef.value.open(type, id, operationType)
}

/** 提交操作 */
const handleSubmit = async (id: number, type: 'report' | 'final') => {
  try {
    // 提交的二次确认
    await message.confirm(`确认要提交该${type === 'report' ? '报备' : '正式'}方案吗？`)

    const updateData: Partial<ReportVO> = { id }
    if (type === 'report') {
      updateData.reportStatus = 1
    } else {
      updateData.finalTime = new Date().getTime()
      updateData.finalStatus = 1
    }

    // 发起提交
    await ReportApi.updateReport(updateData as ReportVO)
    message.success(`${type === 'report' ? '报备' : '正式'}方案提交成功`)
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ReportApi.exportReport(queryParams)
    download.excel(data, '考核方案报备.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
