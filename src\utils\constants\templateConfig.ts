/**
 * 考核模板相关配置常量
 */

// 计算类型选项
export const CALCULATE_TYPE_OPTIONS = [
  { label: '⬆️', value: 'up' },
  { label: '⬇️', value: 'down' },
  { label: '每', value: 'each' }
]

// 运算符选项
export const OPERATOR_OPTIONS = [
  { label: '>', value: 'gt' },
  { label: '<', value: 'lt' },
  { label: '≥', value: 'gte' },
  { label: '≤', value: 'lte' }
]

// 计算类型映射
export const CALCULATE_TYPE_MAP = {
  up: '⬆️',
  down: '⬇️',
  each: '每'
}

// 运算符映射
export const OPERATOR_MAP = {
  gt: '>',
  lt: '<',
  gte: '≥',
  lte: '≤'
}

// 获取计算类型标签
export const getCalculateTypeLabel = (value: string) => {
  return CALCULATE_TYPE_MAP[value] || value
}

// 获取运算符标签
export const getOperatorLabel = (value: string) => {
  return OPERATOR_MAP[value] || value
} 