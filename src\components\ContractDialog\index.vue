<template>
  <el-dialog
    title="合同选择"
    v-model="dialogVisible"
    width="1000px"
    append-to-body
    destroy-on-close
  >
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="88px"
    >
      <el-form-item label="合同名称" prop="contractName">
        <el-input
          v-model="queryParams.contractName"
          placeholder="请输入合同名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      highlight-current-row
      @current-change="handleCurrentChange"
    >
      <el-table-column label="合同名称" align="center" prop="contractName" />
      <el-table-column label="客户" align="center" prop="customerName" />
      <el-table-column label="签约人" align="center" prop="signedUserName" />
      <el-table-column label="合同金额" align="center" prop="currentContractAmount" />
      <!-- <el-table-column label="开票金额" align="center" prop="invoiceAmount" />
      <el-table-column label="调整金额" align="center" prop="industry" />
      <el-table-column label="收款金额" align="center" prop="amountCollected" />
      <el-table-column label="未结金额" align="center" prop="industry" />
      <el-table-column label="状态" align="center" prop="result">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CONTRACT_STATUS" :value="scope.row.result" />
        </template>
      </el-table-column> -->
      <el-table-column
        label="签署日期"
        align="center"
        prop="signingDate"
        :formatter="dateFormatter2"
      />
      <el-table-column label="合同号" align="center" prop="contractCode" />
      <!-- <el-table-column label="合同类别" align="center" prop="contractCategoryId" />
      <el-table-column label="项目关联" align="center" prop="projectId" /> -->
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <template #footer>
      <el-button size="small" @click="dialogVisible = false">取 消</el-button>
      <el-button size="small" type="primary" @click="chooseCustomer">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter2 } from '@/utils/formatTime'
import { ContractApi } from '@/api/sales/contract'

defineOptions({ name: 'ContractDialog' })

const emit = defineEmits(['fetch-data'])

let dialogVisible = ref(false)
let currentRow = ref()
const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  contractName: undefined,
  deptId: undefined,
  customerId: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ContractApi.getContractPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const openDialog = async () => {
  dialogVisible.value = true
  await getList()
}

const handleCurrentChange = (val) => {
  currentRow.value = val
}

const chooseCustomer = () => {
  if (currentRow.value) {
    dialogVisible.value = false
    emit('fetch-data', currentRow.value)
    message.success('选择成功')
  } else {
    message.error('请选择合同')
  }
}

defineExpose({
  openDialog
})
</script>
