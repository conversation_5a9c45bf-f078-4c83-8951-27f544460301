/*
 * @Description: 突出问题隐患区域场所综合治理任务 API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-18 10:39:25
 * @LastEditTime: 2024-10-18 17:05:34
 */
import request from '@/config/axios'

// 突出问题隐患区域场所综合治理任务推进 VO
export interface IssueManagementVO {
  id: number // 唯一标识ID
  unitId: number // 考核单位
  unitName: string // 单位名称
  reportedCases: number // 问题突出隐患区域场所排摸上报数
  rectifiedCases: number // 已完成整改数
  rectificationRate: number // 整改率
  status: number // 状态   0-待提交  1-已提交
  quarter: string // 填写季度
}

// 突出问题隐患区域场所综合治理任务推进 API
export const IssueManagementApi = {
  // 查询突出问题隐患区域场所综合治理任务推进分页
  getIssueManagementPage: async (params: any) => {
    return await request.get({ url: `/examine/issue-management/page`, params })
  },

  // 查询突出问题隐患区域场所综合治理任务推进详情
  getIssueManagement: async (id: number) => {
    return await request.get({ url: `/examine/issue-management/get?id=` + id })
  },

  // 新增突出问题隐患区域场所综合治理任务推进
  createIssueManagement: async (data: IssueManagementVO) => {
    return await request.post({ url: `/examine/issue-management/create`, data })
  },

  // 修改突出问题隐患区域场所综合治理任务推进
  updateIssueManagement: async (data: IssueManagementVO) => {
    return await request.put({ url: `/examine/issue-management/update`, data })
  },
  
  // 新增修改突出问题隐患区域场所综合治理任务推进
  saveOrUpdateIssueManagement: async (data: IssueManagementVO) => {
    return await request.post({ url: `/examine/issue-management/saveOrUpdate`, data })
  },

  // 删除突出问题隐患区域场所综合治理任务推进
  deleteIssueManagement: async (id: number) => {
    return await request.delete({ url: `/examine/issue-management/delete?id=` + id })
  },

  // 导出突出问题隐患区域场所综合治理任务推进 Excel
  exportIssueManagement: async (params) => {
    return await request.download({ url: `/examine/issue-management/export-excel`, params })
  },

  // 获得导入模板
  getImportTemplate: async (params) => {
    return await request.download({ url: `/examine/issue-management/get-import-template`, params })
  },

  // 导入接口地址
  upUrl: '/examine/issue-management/import'
}
