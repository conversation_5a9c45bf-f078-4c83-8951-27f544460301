<template>
  <div class="container leading-40px">
    <div class="text-34px mb-30px text-center">区级政府基本工作数据报告</div>

    <!-- 安全生产会议情况 -->
    <div class="content">
      <div class="content-title">一、安全生产会议情况</div>
      <div>
        1. 党委（党组）研究安全生产
        <el-input-number :disabled="flag === 'detail'" v-model="formData.partyMeetingCount" />
        次，听取安全生产工作汇报
        <el-input-number :disabled="flag === 'detail'" v-model="formData.workReportCount" />
        次。
      </div>
      <div>
        2. 主要负责人召开安全工作会议
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.primarySafetyMeetingCount"
        />
        次。
      </div>
      <div>
        3. 分管负责人召开安全工作会议
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.deputySafetyMeetingCount"
        />
        次。
      </div>
    </div>

    <!-- 生产安全事故情况 -->
    <div class="content">
      <div class="content-title">二、生产安全事故情况</div>
      <div>
        1. 工矿商贸事故
        <el-input-number :disabled="flag === 'detail'" v-model="formData.industrialAccidentCount" />
        起，死亡
        <el-input-number :disabled="flag === 'detail'" v-model="formData.industrialDeathCount" />
        人； 生产经营性道路交通事故
        <el-input-number :disabled="flag === 'detail'" v-model="formData.trafficAccidentCount" />
        起，死亡
        <el-input-number :disabled="flag === 'detail'" v-model="formData.trafficDeathCount" />
        人； 生产经营性火灾事故
        <el-input-number :disabled="flag === 'detail'" v-model="formData.fireAccidentCount" />
        起，死亡
        <el-input-number :disabled="flag === 'detail'" v-model="formData.fireDeathCount" />
        人； 农业机械事故
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.agriculturalAccidentCount"
        />
        起，死亡
        <el-input-number :disabled="flag === 'detail'" v-model="formData.agriculturalDeathCount" />
        人。
      </div>
      <div>
        2. 较大及以上事故
        <el-input-number :disabled="flag === 'detail'" v-model="formData.majorAccidentCount" />
        起，死亡
        <el-input-number :disabled="flag === 'detail'" v-model="formData.majorDeathCount" />
        人。
      </div>
    </div>

    <!-- 行政执法情况 -->
    <div class="content">
      <div class="content-title">三、行政执法情况</div>
      <div>本地区分行业领域行政执法情况。</div>
      <!-- 工矿商贸领域 -->
      <div>
        1. 工矿商贸领域，检查
        <el-input-number :disabled="flag === 'detail'" v-model="formData.miningInspections" />
        户单位，覆盖率
        <el-input-number :disabled="flag === 'detail'" v-model="formData.miningCoverage" />%
        。查出隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.miningHiddenDangers" />
        条，整改隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.miningHiddenRectified" />
        条（其中重大事故隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.miningMajorHiddenDangers"
        />
        条，整改隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.miningMajorHiddenRectified"
        />
        条，一案双罚
        <el-input-number :disabled="flag === 'detail'" v-model="formData.miningDualPenaltyCases" />
        条，行政处罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.miningAdministrativeFines"
        />
        万元）。立案查处
        <el-input-number :disabled="flag === 'detail'" v-model="formData.miningCasesFiled" />
        条，行政处罚
        <el-input-number :disabled="flag === 'detail'" v-model="formData.miningCasesFineAmount" />
        万元，吊销证照（单位、个人）
        <el-input-number :disabled="flag === 'detail'" v-model="formData.miningRevokedLicenses" />
        起，责令停产停业
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.miningSuspendedBusinesses"
        />
        户。
      </div>

      <!-- 建筑施工领域 -->
      <div>
        2. 建筑施工领域，检查
        <el-input-number :disabled="flag === 'detail'" v-model="formData.constructionInspections" />
        户单位，覆盖率
        <el-input-number :disabled="flag === 'detail'" v-model="formData.constructionCoverage" />%
        。查出隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.constructionHiddenDangers"
        />
        条，整改隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.constructionHiddenRectified"
        />
        条（其中重大事故隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.constructionMajorHiddenDangers"
        />
        条，整改隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.constructionMajorHiddenRectified"
        />
        条，一案双罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.constructionDualPenaltyCases"
        />
        条，行政处罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.constructionAdministrativeFines"
        />
        万元）。立案查处
        <el-input-number :disabled="flag === 'detail'" v-model="formData.constructionCasesFiled" />
        条，行政处罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.constructionCasesFineAmount"
        />
        万元，吊销证照（单位、个人）
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.constructionRevokedLicenses"
        />
        起，责令停产停业
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.constructionSuspendedBusinesses"
        />
        户。
      </div>

      <!-- 消防安全领域 -->
      <div>
        3. 消防安全领域，检查
        <el-input-number :disabled="flag === 'detail'" v-model="formData.fireSafetyInspections" />
        户单位，覆盖率
        <el-input-number :disabled="flag === 'detail'" v-model="formData.fireSafetyCoverage" />%
        。查出隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.fireSafetyHiddenDangers" />
        条，整改隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.fireSafetyHiddenRectified"
        />
        条（其中重大事故隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.fireSafetyMajorHiddenDangers"
        />
        条，整改隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.fireSafetyMajorHiddenRectified"
        />
        条，一案双罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.fireSafetyDualPenaltyCases"
        />
        条，行政处罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.fireSafetyAdministrativeFines"
        />
        万元）。立案查处
        <el-input-number :disabled="flag === 'detail'" v-model="formData.fireSafetyCasesFiled" />
        条，行政处罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.fireSafetyCasesFineAmount"
        />
        万元，吊销证照（单位、个人）
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.fireSafetyRevokedLicenses"
        />
        起，责令停产停业
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.fireSafetySuspendedBusinesses"
        />
        户。
      </div>

      <!-- 特种设备领域 -->
      <div>
        4. 特种设备领域，检查
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.specialEquipmentInspections"
        />
        户单位，覆盖率
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.specialEquipmentCoverage"
        />% 。查出隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.specialEquipmentHiddenDangers"
        />
        条，整改隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.specialEquipmentHiddenRectified"
        />
        条（其中重大事故隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.specialEquipmentMajorHiddenDangers"
        />
        条，整改隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.specialEquipmentMajorHiddenRectified"
        />
        条，一案双罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.specialEquipmentDualPenaltyCases"
        />
        条，行政处罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.specialEquipmentAdministrativeFines"
        />
        万元）。立案查处
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.specialEquipmentCasesFiled"
        />
        条，行政处罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.specialEquipmentCasesFineAmount"
        />
        万元，吊销证照（单位、个人）
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.specialEquipmentRevokedLicenses"
        />
        起，责令停产停业
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.specialEquipmentSuspendedBusinesses"
        />
        户。
      </div>
      <!-- 交通运输领域 -->
      <div>
        5. 交通运输领域，检查
        <el-input-number :disabled="flag === 'detail'" v-model="formData.transportInspections" />
        户单位，覆盖率
        <el-input-number :disabled="flag === 'detail'" v-model="formData.transportCoverage" />%
        。查出隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.transportHiddenDangers" />
        条，整改隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.transportHiddenRectified"
        />
        条（其中重大事故隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.transportMajorHiddenDangers"
        />
        条，整改隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.transportMajorHiddenRectified"
        />
        条，一案双罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.transportDualPenaltyCases"
        />
        条，行政处罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.transportAdministrativeFines"
        />
        万元）。立案查处
        <el-input-number :disabled="flag === 'detail'" v-model="formData.transportCasesFiled" />
        条，行政处罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.transportCasesFineAmount"
        />
        万元，吊销证照（单位、个人）
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.transportRevokedLicenses"
        />
        起，责令停产停业
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.transportSuspendedBusinesses"
        />
        户。
      </div>

      <!-- 城镇燃气领域 -->
      <div>
        6. 城镇燃气领域，检查
        <el-input-number :disabled="flag === 'detail'" v-model="formData.gasInspections" />
        户单位，覆盖率
        <el-input-number :disabled="flag === 'detail'" v-model="formData.gasCoverage" />% 。查出隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.gasHiddenDangers" />
        条，整改隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.gasHiddenRectified" />
        条（其中重大事故隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.gasMajorHiddenDangers" />
        条，整改隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.gasMajorHiddenRectified" />
        条，一案双罚
        <el-input-number :disabled="flag === 'detail'" v-model="formData.gasDualPenaltyCases" />
        条，行政处罚
        <el-input-number :disabled="flag === 'detail'" v-model="formData.gasAdministrativeFines" />
        万元）。立案查处
        <el-input-number :disabled="flag === 'detail'" v-model="formData.gasCasesFiled" />
        条，行政处罚
        <el-input-number :disabled="flag === 'detail'" v-model="formData.gasCasesFineAmount" />
        万元，吊销证照（单位、个人）
        <el-input-number :disabled="flag === 'detail'" v-model="formData.gasRevokedLicenses" />
        起，责令停产停业
        <el-input-number :disabled="flag === 'detail'" v-model="formData.gasSuspendedBusinesses" />
        户。
      </div>

      <!-- 其他领域 -->
      <div>
        7. 其他领域，检查
        <el-input-number :disabled="flag === 'detail'" v-model="formData.otherInspections" />
        户单位，覆盖率
        <el-input-number :disabled="flag === 'detail'" v-model="formData.otherCoverage" />%
        。查出隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.otherHiddenDangers" />
        条，整改隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.otherHiddenRectified" />
        条（其中重大事故隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.otherMajorHiddenDangers" />
        条，整改隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.otherMajorHiddenRectified"
        />
        条，一案双罚
        <el-input-number :disabled="flag === 'detail'" v-model="formData.otherDualPenaltyCases" />
        条，行政处罚
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.otherAdministrativeFines"
        />
        万元）。立案查处
        <el-input-number :disabled="flag === 'detail'" v-model="formData.otherCasesFiled" />
        条，行政处罚
        <el-input-number :disabled="flag === 'detail'" v-model="formData.otherCasesFineAmount" />
        万元，吊销证照（单位、个人）
        <el-input-number :disabled="flag === 'detail'" v-model="formData.otherRevokedLicenses" />
        起，责令停产停业
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.otherSuspendedBusinesses"
        />
        户。
      </div>
    </div>

    <!-- 社会共治情况 -->
    <div class="content">
      <div class="content-title">四、社会共治情况</div>

      <!-- 安全生产宣传教育情况 -->
      <div>
        1. 安全生产宣传教育情况。 举办党政领导干部培训班
        <el-input-number :disabled="flag === 'detail'" v-model="formData.leadershipTrainingCount" />
        期，培训
        <el-input-number :disabled="flag === 'detail'" v-model="formData.leadershipTrainedCount" />
        人； 组织企业主要负责人安全培训班
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.businessLeaderTrainingCount"
        />
        期，培训
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.businessLeaderTrainedCount"
        />
        人；
        推动危化品、交通运输、建筑施工、金属冶炼等高危行业生产经营单位从业人员安全技能提升，年内从业人员培训
        <el-input-number :disabled="flag === 'detail'" v-model="formData.highRiskTrainingCount" />
        万人。 学好用好重大事故隐患判定标准，开展解读宣贯
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.majorAccidentTrainingSessions"
        />
        次，培训
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.majorAccidentTrainedPeople"
        />
        万人。
      </div>

      <!-- 安全生产责任险情况 -->
      <div>
        2. 安全生产责任险情况。 危化、金属冶炼高危行业企业安责险应保企业
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.highRiskInsuranceRequired"
        />
        家，在保企业
        <el-input-number :disabled="flag === 'detail'" v-model="formData.highRiskInsured" />
        家。 建筑施工企业安责险应保企业
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.constructionInsuranceRequired"
        />
        家，在保企业
        <el-input-number :disabled="flag === 'detail'" v-model="formData.constructionInsured" />
        家。 交通运输企业安责险应保企业
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.transportInsuranceRequired"
        />
        家，在保企业
        <el-input-number :disabled="flag === 'detail'" v-model="formData.transportInsured" />
        家。
      </div>

      <!-- 安全生产举报奖励情况 -->
      <div>
        3. 安全生产举报奖励情况。 全区各级各部门接办举报
        <el-input-number :disabled="flag === 'detail'" v-model="formData.reportsHandled" />
        万件，经核查属实后兑现奖励
        <el-input-number :disabled="flag === 'detail'" v-model="formData.rewardAmount" />
        万元。 通过举报查实消除事故隐患
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.dangersResolvedByReports"
        />
        万件次，制止违法行为
        <el-input-number :disabled="flag === 'detail'" v-model="formData.illegalActionsStopped" />
        万件次。
      </div>
    </div>

    <div class="f-c mt-50px">
      <el-button @click="handleCancel">取消</el-button>
      <el-button v-if="flag === 'edit'" type="primary" @click="handleSubmit">保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { WorkReportApi } from '@/api/examineTask/yearWork/index'

const { replace, push } = useRouter()
const { query }: any = useRoute()
const message = useMessage()

const formData = ref({
  partyMeetingCount: '', // 党委会议次数
  workReportCount: '', // 工作汇报次数
  primarySafetyMeetingCount: '', // 主要负责人安全会议次数
  deputySafetyMeetingCount: '', // 分管负责人安全会议次数
  industrialAccidentCount: '', // 工矿商贸事故起数
  industrialDeathCount: '', // 工矿商贸事故死亡人数
  trafficAccidentCount: '', // 生产经营性道路交通事故起数
  trafficDeathCount: '', // 道路交通事故死亡人数
  fireAccidentCount: '', // 生产经营性火灾事故起数
  fireDeathCount: '', // 火灾事故死亡人数
  agriculturalAccidentCount: '', // 农业机械事故起数
  agriculturalDeathCount: '', // 农业机械事故死亡人数
  majorAccidentCount: '', // 较大事故起数
  majorDeathCount: '', // 较大事故死亡人数
  miningInspections: '', // 工矿商贸领域检查单位数
  miningCoverage: '', // 工矿商贸领域覆盖率
  miningHiddenDangers: '', // 工矿商贸领域隐患数
  miningHiddenRectified: '', // 工矿商贸领域整改隐患数
  miningMajorHiddenDangers: '', // 工矿商贸领域重大事故隐患数
  miningMajorHiddenRectified: '', // 工矿商贸领域重大隐患整改数
  miningDualPenaltyCases: '', // 工矿商贸领域一案双罚条数
  miningAdministrativeFines: '', // 工矿商贸领域行政处罚金额
  miningCasesFiled: '', // 工矿商贸领域立案查处条数
  miningCasesFineAmount: '', // 工矿商贸领域行政处罚金额
  miningRevokedLicenses: '', // 工矿商贸领域吊销证照数
  miningSuspendedBusinesses: '', // 工矿商贸领域责令停产停业数
  constructionInspections: '', // 建筑施工领域检查单位数
  constructionCoverage: '', // 建筑施工领域覆盖率
  constructionHiddenDangers: '', // 建筑施工领域隐患数
  constructionHiddenRectified: '', // 建筑施工领域整改隐患数
  constructionMajorHiddenDangers: '', // 建筑施工领域重大事故隐患数
  constructionMajorHiddenRectified: '', // 建筑施工领域重大隐患整改数
  constructionDualPenaltyCases: '', // 建筑施工领域一案双罚条数
  constructionAdministrativeFines: '', // 建筑施工领域行政处罚金额
  constructionCasesFiled: '', // 建筑施工领域立案查处条数
  constructionCasesFineAmount: '', // 建筑施工领域行政处罚金额
  constructionRevokedLicenses: '', // 建筑施工领域吊销证照数
  constructionSuspendedBusinesses: '', // 建筑施工领域责令停产停业数
  fireSafetyInspections: '', // 消防安全领域检查单位数
  fireSafetyCoverage: '', // 消防安全领域覆盖率
  fireSafetyHiddenDangers: '', // 消防安全领域隐患数
  fireSafetyHiddenRectified: '', // 消防安全领域整改隐患数
  fireSafetyMajorHiddenDangers: '', // 消防安全领域重大事故隐患数
  fireSafetyMajorHiddenRectified: '', // 消防安全领域重大隐患整改数
  fireSafetyDualPenaltyCases: '', // 消防安全领域一案双罚条数
  fireSafetyAdministrativeFines: '', // 消防安全领域行政处罚金额
  fireSafetyCasesFiled: '', // 消防安全领域立案查处条数
  fireSafetyCasesFineAmount: '', // 消防安全领域行政处罚金额
  fireSafetyRevokedLicenses: '', // 消防安全领域吊销证照数
  fireSafetySuspendedBusinesses: '', // 消防安全领域责令停产停业数
  specialEquipmentInspections: '', // 特种设备领域检查单位数
  specialEquipmentCoverage: '', // 特种设备领域覆盖率
  specialEquipmentHiddenDangers: '', // 特种设备领域隐患数
  specialEquipmentHiddenRectified: '', // 特种设备领域整改隐患数
  specialEquipmentMajorHiddenDangers: '', // 特种设备领域重大事故隐患数
  specialEquipmentMajorHiddenRectified: '', // 特种设备领域重大隐患整改数
  specialEquipmentDualPenaltyCases: '', // 特种设备领域一案双罚条数
  specialEquipmentAdministrativeFines: '', // 特种设备领域行政处罚金额
  specialEquipmentCasesFiled: '', // 特种设备领域立案查处条数
  specialEquipmentCasesFineAmount: '', // 特种设备领域行政处罚金额
  specialEquipmentRevokedLicenses: '', // 特种设备领域吊销证照数
  specialEquipmentSuspendedBusinesses: '', // 特种设备领域责令停产停业数
  transportInspections: '', // 交通运输领域检查单位数
  transportCoverage: '', // 交通运输领域覆盖率
  transportHiddenDangers: '', // 交通运输领域隐患数
  transportHiddenRectified: '', // 交通运输领域整改隐患数
  transportMajorHiddenDangers: '', // 交通运输领域重大事故隐患数
  transportMajorHiddenRectified: '', // 交通运输领域重大隐患整改数
  transportDualPenaltyCases: '', // 交通运输领域一案双罚条数
  transportAdministrativeFines: '', // 交通运输领域行政处罚金额
  transportCasesFiled: '', // 交通运输领域立案查处条数
  transportCasesFineAmount: '', // 交通运输领域行政处罚金额
  transportRevokedLicenses: '', // 交通运输领域吊销证照数
  transportSuspendedBusinesses: '', // 交通运输领域责令停产停业数
  gasInspections: '', // 城镇燃气领域检查单位数
  gasCoverage: '', // 城镇燃气领域覆盖率
  gasHiddenDangers: '', // 城镇燃气领域隐患数
  gasHiddenRectified: '', // 城镇燃气领域整改隐患数
  gasMajorHiddenDangers: '', // 城镇燃气领域重大事故隐患数
  gasMajorHiddenRectified: '', // 城镇燃气领域重大隐患整改数
  gasDualPenaltyCases: '', // 城镇燃气领域一案双罚条数
  gasAdministrativeFines: '', // 城镇燃气领域行政处罚金额
  gasCasesFiled: '', // 城镇燃气领域立案查处条数
  gasCasesFineAmount: '', // 城镇燃气领域行政处罚金额
  gasRevokedLicenses: '', // 城镇燃气领域吊销证照数
  gasSuspendedBusinesses: '', // 城镇燃气领域责令停产停业数
  otherInspections: '', // 其他领域检查单位数
  otherCoverage: '', // 其他领域覆盖率
  otherHiddenDangers: '', // 其他领域隐患数
  otherHiddenRectified: '', // 其他领域整改隐患数
  otherMajorHiddenDangers: '', // 其他领域重大事故隐患数
  otherMajorHiddenRectified: '', // 其他领域重大隐患整改数
  otherDualPenaltyCases: '', // 其他领域一案双罚条数
  otherAdministrativeFines: '', // 其他领域行政处罚金额
  otherCasesFiled: '', // 其他领域立案查处条数
  otherCasesFineAmount: '', // 其他领域行政处罚金额
  otherRevokedLicenses: '', // 其他领域吊销证照数
  otherSuspendedBusinesses: '', // 其他领域责令停产停业数
  leadershipTrainingCount: '', // 党政领导干部培训期数
  leadershipTrainedCount: '', // 党政领导干部培训人数
  businessLeaderTrainingCount: '', // 企业负责人培训期数
  businessLeaderTrainedCount: '', // 企业负责人培训人数
  highRiskTrainingCount: '', // 高危行业从业人员培训人数
  majorAccidentTrainingSessions: '', // 重大事故隐患判定标准培训场次
  majorAccidentTrainedPeople: '', // 重大事故隐患判定标准培训人数
  highRiskInsuranceRequired: '', // 危险行业应保企业数
  highRiskInsured: '', // 危险行业在保企业数
  constructionInsuranceRequired: '', // 建筑施工企业应保企业数
  constructionInsured: '', // 建筑施工企业在保企业数
  transportInsuranceRequired: '', // 交通运输企业应保企业数
  transportInsured: '', // 交通运输企业在保企业数
  reportsHandled: '', // 举报处理件数
  rewardAmount: '', // 举报奖励金额
  dangersResolvedByReports: '', // 举报查实消除隐患数
  illegalActionsStopped: '' // 举报查实制止违法行为数
})

const handleCancel = () => {
  replace(query.backPath)
}

const handleSubmit = async () => {
  const submitData: any = []
  for (const key in formData.value) {
    if (Object.hasOwnProperty.call(formData.value, key)) {
      const element = formData.value[key]
      if (element) {
        submitData.push({
          reportId: query.id,
          attribute: key,
          attributeValue: element
        })
      }
    }
  }
  const res = await WorkReportApi.createWorkReport(submitData)
  message.success('保存成功')
  handleCancel()
}

const getDetailInfo = async () => {
  const res = await WorkReportApi.getWorkReportPage({
    pageSize: -1,
    yearWorkId: query.id
  })
  res.list.forEach((item) => {
    if (formData.value.hasOwnProperty(item.attribute)) {
      formData.value[item.attribute] = item.attributeValue
    }
  })
}
const flag = ref()
onMounted(() => {
  flag.value = query.type
  getDetailInfo()
})
</script>

<style lang="scss" scoped>
:deep(.el-input-number) {
  width: 120px;
}
.container {
  background-color: white;
  padding: 40px;
}

.content-title {
  font-size: 22px;
  margin: 15px 0;
}
</style>
