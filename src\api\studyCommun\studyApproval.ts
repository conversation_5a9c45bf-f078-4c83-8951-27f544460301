/*
 * @Description: 交流学习审批相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-30 13:26:14
 * @LastEditTime: 2024-07-30 14:07:26
 */
import request from '@/config/axios'

// 交流学习审批记录 VO
export interface LearningApprovalVO {
  id: number // 公告ID
  learningId: number // 交流学习ID
  title: string // 文件名
  uploadUnit: string // 上传单位
  uploadTime: Date // 上传时间
  reviewResult: number // 审核状态 （0通过 1拒绝）
  reviewOpinions: string // 审核意见
}

// 交流学习审批记录 API
export const LearningApprovalApi = {
  // 查询交流学习审批记录分页
  getLearningApprovalPage: async (params: any) => {
    return await request.get({ url: `/system/learning-approval/page`, params })
  },

  // 查询交流学习审批记录详情
  getLearningApproval: async (id: number) => {
    return await request.get({ url: `/system/learning-approval/get?id=` + id })
  },

  // 新增交流学习审批记录
  createLearningApproval: async (data: LearningApprovalVO) => {
    return await request.post({ url: `/system/learning-approval/create`, data })
  },

  // 修改交流学习审批记录
  updateLearningApproval: async (data: LearningApprovalVO) => {
    return await request.put({ url: `/system/learning-approval/update`, data })
  },

  // 删除交流学习审批记录
  deleteLearningApproval: async (id: number) => {
    return await request.delete({ url: `/system/learning-approval/delete?id=` + id })
  },

  // 导出交流学习审批记录 Excel
  exportLearningApproval: async (params) => {
    return await request.download({ url: `/system/learning-approval/export-excel`, params })
  },
}
