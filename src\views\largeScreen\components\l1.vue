<!--
 * @Description: 考核排名-各区
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-09-05 10:03:54
 * @LastEditTime: 2024-09-05 16:31:35
-->

<template>
  <div class="con">
    <div class="title">
      考核排名-各区
      <ul class="date">
        <li @click="handleRange(1)" :class="acitiveNum == 1 ? 'active' : ''"> 今日 </li>
        <li @click="handleRange(2)" :class="acitiveNum == 2 ? 'active' : ''"> 本周 </li>
        <li @click="handleRange(3)" :class="acitiveNum == 3 ? 'active' : ''"> 本月 </li>
        <li @click="handleRange(4)" :class="acitiveNum == 4 ? 'active' : ''"> 全年 </li>
      </ul>
    </div>
    <div class="content" ref="contentRef">
      <ul class="head">
        <li>排名</li>
        <li>区名称</li>
        <li>总分</li>
        <li>事故分数</li>
        <li>过程分数</li>
      </ul>
      <ul class="body">
        <li>
          <span style="position: relative; height: 100%">
            <img
              src="@/assets/imgs/largeScreen/rank1.png"
              alt=""
              style="
                width: 16px;
                height: 16px;
                position: absolute;
                top: 9px;
                left: 50%;
                transform: translateX(-50%);
              "
            />
          </span>
          <span>黄浦区</span>
          <span>98</span>
          <span>49</span>
          <span>49</span>
        </li>
        <li>
          <span style="position: relative; height: 100%">
            <img
              src="@/assets/imgs/largeScreen/rank2.png"
              alt=""
              style="
                width: 16px;
                height: 16px;
                position: absolute;
                top: 9px;
                left: 50%;
                transform: translateX(-50%);
              "
            />
          </span>
          <span>徐汇区</span>
          <span>97</span>
          <span>47</span>
          <span>50</span>
        </li>
        <li>
          <span style="position: relative; height: 100%">
            <img
              src="@/assets/imgs/largeScreen/rank3.png"
              alt=""
              style="
                width: 16px;
                height: 16px;
                position: absolute;
                top: 9px;
                left: 50%;
                transform: translateX(-50%);
              "
            />
          </span>
          <span>长宁区</span>
          <span>95</span>
          <span>45</span>
          <span>50</span>
        </li>
        <li>
          <span> 4 </span>
          <span>静安区</span>
          <span>94</span>
          <span>44</span>
          <span>50</span>
        </li>
        <li>
          <span> 5 </span>
          <span>普陀区</span>
          <span>92</span>
          <span>46</span>
          <span>46</span>
        </li>
        <li>
          <span> 6 </span>
          <span>闵行区</span>
          <span>91</span>
          <span>41</span>
          <span>50</span>
        </li>
        <li>
          <span> 7 </span>
          <span>虹口区</span>
          <span>90</span>
          <span>42</span>
          <span>48</span>
        </li>
        <li>
          <span> 8 </span>
          <span>宝山区</span>
          <span>89</span>
          <span>48</span>
          <span>41</span>
        </li>
        <li>
          <span> 9 </span>
          <span>嘉定区</span>
          <span>88</span>
          <span>34</span>
          <span>54</span>
        </li>
        <li>
          <span> 10 </span>
          <span>浦东新区</span>
          <span>87</span>
          <span>43</span>
          <span>44</span>
        </li>
        <li>
          <span> 11 </span>
          <span>松江区</span>
          <span>86</span>
          <span>43</span>
          <span>43</span>
        </li>
        <li>
          <span> 12 </span>
          <span>青浦区</span>
          <span>84</span>
          <span>43</span>
          <span>41</span>
        </li>
        <li>
          <span> 13 </span>
          <span>奉贤区</span>
          <span>83</span>
          <span>33</span>
          <span>50</span>
        </li>
        <li>
          <span> 14 </span>
          <span>崇明区</span>
          <span>82</span>
          <span>41</span>
          <span>41</span>
        </li>
        <li>
          <span> 15 </span>
          <span>杨浦区</span>
          <span>82</span>
          <span>50</span>
          <span>32</span>
        </li>
        <li>
          <span> 16</span>
          <span>金山区</span>
          <span>80</span>
          <span>40</span>
          <span>40</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script setup lang="ts">
const option = ref({})
const contentRef = ref<any>(null)
const acitiveNum = ref(4)
const handleRange = (num) => {
  acitiveNum.value = num
}
const randomScore = () => {
  return Math.floor(Math.random() * 100)
}
onMounted(() => {
  let h = contentRef.value.offsetHeight

  let domList = contentRef.value.getElementsByTagName('li')
  for (let item of domList) {
    item.style.height = (h - 30) / 10 + 'px'
    item.style.lineHeight = (h - 30) / 10 + 'px'
  }
  option.value = {
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['45%', '65%'],
        avoidLabelOverlap: false,

        labelLine: {
          show: true
        },
        itemStyle: {
          normal: {
            borderColor: '#fff'
          }
        },

        label: {
          position: 'outer',
          alignTo: 'edge',
          margin: 0,
          formatter(param) {
            return param.name + ' (' + param.percent * 2 + '%)'
          }
        },
        data: [
          { value: 1048, name: 'Search' },
          { value: 735, name: 'Direct' },
          { value: 580, name: 'Email' },
          { value: 484, name: 'Union' },
          { value: 300, name: 'Video' }
        ]
      }
    ]
  }
})
</script>

<style scoped lang="scss">
.con {
  height: 100%;
  color: #fff;
  background: #071b3d;
  //background: linear-gradient(#3264a4, #051430 30%, #051430 70%);

  padding: 10px;
  border-radius: 6px;
  border: 1px solid #4189f0;
  .active {
    color: #fff !important;
  }
  .title {
    font-size: 15px;
    font-weight: bold;
    position: relative;
    .date {
      position: absolute;
      display: flex;
      right: 10px;
      top: 4px;
      font-size: 12px;
      color: #767e8e;

      > li {
        flex: 1;
        margin: 0 4px;
        font-weight: bold;
        cursor: pointer;
      }
    }
  }
  .content {
    width: 100%;
    height: calc(100% - 20px);
    padding-top: 4px;
    overflow: hidden;
    font-size: 12px;
    text-align: left;
    .head {
      display: flex;

      background: #153373;
      > li {
        overflow: hidden;
        line-height: 38px !important;
        height: 38px !important;
      }
    }
    .head {
      > li:nth-child(1) {
        width: 14%;
        text-align: center;
      }
      > li:nth-child(2) {
        width: 26%;
      }
      > li:nth-child(3) {
        width: 20%;
      }
      > li:nth-child(4) {
        width: 20%;
      }
      > li:nth-child(5) {
        width: 20%;
      }
    }
    .body {
      height: 100%;
      overflow: hidden;
      margin-top: 2px;
      > li {
        line-height: 32px !important;
        height: 32px !important;
        > span {
          display: inline-block;
          overflow: hidden;
        }
        > span:nth-child(1) {
          width: 14%;
          text-align: center;
        }
        > span:nth-child(2) {
          width: 26%;
        }
        > span:nth-child(3) {
          width: 20%;
          padding-left: 6px;
        }
        > span:nth-child(4) {
          width: 20%;
          padding-left: 12px;
        }
        > span:nth-child(5) {
          width: 10%;
        }
      }
      > li:nth-child(even) {
        background: #153373;
      }

      > li:nth-child(1),
      > li:nth-child(3) {
        > span:nth-child(1) {
          color: #ed9d32;
        }
      }
    }
  }
}
</style>
