<template>
  <div class="container leading-40px">
    <div class="text-34px mb-30px text-center">成员单位基本工作数据报告</div>

    <!-- 部门安全生产会议情况 -->
    <div class="content">
      <div class="content-title">一、部门安全生产会议情况</div>
      <div>
        1. 党委（党组）研究安全生产
        <el-input-number :disabled="flag === 'detail'" v-model="formData.partyMeetingCount" />
        次，听取安全生产工作汇报
        <el-input-number :disabled="flag === 'detail'" v-model="formData.workReportCount" />
        次。
      </div>
      <div>
        2. 主要负责人召开安全工作会议、专题会议
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.primarySafetyMeetingCount"
        />
        次。
      </div>
      <div>
        3. 分管负责人召开安全工作会议、专题会议
        <el-input-number
          :disabled="flag === 'detail'"
          v-model="formData.deputySafetyMeetingCount"
        />
        次。
      </div>
    </div>

    <!-- 行业领域生产安全事故情况 -->
    <div class="content">
      <div class="content-title">二、行业领域生产安全事故情况</div>
      <div>
        1. 涉及本行业领域工矿商贸事故
        <el-input-number :disabled="flag === 'detail'" v-model="formData.industrialAccidentCount" />
        起，死亡
        <el-input-number :disabled="flag === 'detail'" v-model="formData.industrialDeathCount" />
        人； 生产经营性道路交通事故
        <el-input-number :disabled="flag === 'detail'" v-model="formData.trafficAccidentCount" />
        起，死亡
        <el-input-number :disabled="flag === 'detail'" v-model="formData.trafficDeathCount" />
        人； 生产经营性火灾事故
        <el-input-number :disabled="flag === 'detail'" v-model="formData.fireAccidentCount" />
        起，死亡
        <el-input-number :disabled="flag === 'detail'" v-model="formData.fireDeathCount" />
        人； 农业机械事故死亡
        <el-input-number :disabled="flag === 'detail'" v-model="formData.agriculturalDeathCount" />
        人。
      </div>
      <div>
        2. 涉及本行业领域较大及以上事故
        <el-input-number :disabled="flag === 'detail'" v-model="formData.majorAccidentCount" />
        起，死亡
        <el-input-number :disabled="flag === 'detail'" v-model="formData.majorDeathCount" />
        人。
      </div>
    </div>

    <!-- 行政执法或安全检查情况 -->
    <div class="content">
      <div class="content-title">三、行政执法或安全检查情况</div>
      <div>
        行业领域，检查
        <el-input-number :disabled="flag === 'detail'" v-model="formData.industryInspections" />
        户单位，覆盖率达到
        <el-input-number :disabled="flag === 'detail'" v-model="formData.industryCoverage" />%
        。查出隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.industryHiddenDangers" />
        条，整改隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.industryHiddenRectified" />
        条（其中重大事故隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.majorHiddenDangers" />
        条，整改隐患
        <el-input-number :disabled="flag === 'detail'" v-model="formData.majorHiddenRectified" />
        条，一案双罚
        <el-input-number :disabled="flag === 'detail'" v-model="formData.dualPenaltyCases" />
        条，行政处罚
        <el-input-number :disabled="flag === 'detail'" v-model="formData.administrativeFines" />
        万元）。立案查处
        <el-input-number :disabled="flag === 'detail'" v-model="formData.casesFiled" />
        条，行政处罚
        <el-input-number :disabled="flag === 'detail'" v-model="formData.fineAmount" />
        万元，吊销证照（单位、个人）
        <el-input-number :disabled="flag === 'detail'" v-model="formData.revokedLicenses" />
        起，责令停产停业
        <el-input-number :disabled="flag === 'detail'" v-model="formData.suspendedBusinesses" />
        户。
      </div>
    </div>

    <!-- 社会共治情况 -->
    <div class="content">
      <div class="content-title">四、社会共治情况</div>

      <!-- 安全生产宣传教育情况 -->
      <div>
        1. 安全生产宣传教育情况：
        <div>
          举办党委（组）中心组学习
          <el-input-number :disabled="flag === 'detail'" v-model="formData.centerStudySessions" />
          期；
        </div>
        <div>
          组织行业系统企业主要负责人安全培训班
          <el-input-number
            :disabled="flag === 'detail'"
            v-model="formData.businessLeaderTrainingCount"
          />
          期，培训
          <el-input-number
            :disabled="flag === 'detail'"
            v-model="formData.businessLeaderTrainedCount"
          />
          人；
        </div>
        <div>
          推动行业系统内高危行业生产经营单位从业人员安全技能提升，年内从业人员培训
          <el-input-number :disabled="flag === 'detail'" v-model="formData.highRiskTrainingCount" />
          人。
        </div>
        <div>
          学好用好重大事故隐患判定标准，开展解读宣贯
          <el-input-number
            :disabled="flag === 'detail'"
            v-model="formData.accidentStandardTrainingSessions"
          />
          次，培训
          <el-input-number
            :disabled="flag === 'detail'"
            v-model="formData.accidentStandardTrainedPeople"
          />
          人。
        </div>
      </div>

      <!-- 安全生产责任险情况 -->
      <div>
        2. 安全生产责任险情况：
        <div>
          行业系统内高危企业安责险应保企业
          <el-input-number
            :disabled="flag === 'detail'"
            v-model="formData.highRiskInsuranceRequired"
          />
          家，在保企业
          <el-input-number :disabled="flag === 'detail'" v-model="formData.highRiskInsured" />
          家。
        </div>
      </div>

      <!-- 安全生产举报奖励情况 -->
      <div>
        3. 安全生产举报奖励情况：
        <div>
          行业系统内接办举报
          <el-input-number :disabled="flag === 'detail'" v-model="formData.reportsHandled" />
          万件，经核查属实后兑现奖励
          <el-input-number :disabled="flag === 'detail'" v-model="formData.rewardAmount" />
          万元。
        </div>
        <div>
          通过举报查实消除事故隐患
          <el-input-number
            :disabled="flag === 'detail'"
            v-model="formData.dangersResolvedByReports"
          />
          万件次，制止违法行为
          <el-input-number :disabled="flag === 'detail'" v-model="formData.illegalActionsStopped" />
          万件次。
        </div>
      </div>
    </div>

    <div class="f-c mt-50px">
      <el-button @click="handleCancel">取消</el-button>
      <el-button v-if="flag === 'edit'" type="primary" @click="handleSubmit">保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { WorkReportApi } from '@/api/examineTask/yearWork/index'

const { replace, push } = useRouter()
const { query }: any = useRoute()
const message = useMessage()

const formData: any = ref({
  // 第一部分 部门安全生产会议情况字段
  partyMeetingCount: null, // 党委会议次数
  workReportCount: null, // 听取安全生产工作汇报次数
  primarySafetyMeetingCount: null, // 主要负责人会议次数
  deputySafetyMeetingCount: null, // 分管负责人会议次数

  // 第二部分 行业领域生产安全事故情况字段
  industrialAccidentCount: null, // 工矿商贸事故起数
  industrialDeathCount: null, // 工矿商贸事故死亡人数
  trafficAccidentCount: null, // 道路交通事故起数
  trafficDeathCount: null, // 道路交通事故死亡人数
  fireAccidentCount: null, // 火灾事故起数
  fireDeathCount: null, // 火灾事故死亡人数
  agriculturalDeathCount: null, // 农业机械事故死亡人数
  majorAccidentCount: null, // 较大事故起数
  majorDeathCount: null, // 较大事故死亡人数

  // 第三部分 行政执法或安全检查情况字段
  industryInspections: null, // 行业领域检查单位数
  industryCoverage: null, // 行业领域覆盖率
  industryHiddenDangers: null, // 行业领域隐患数
  industryHiddenRectified: null, // 行业领域整改隐患数
  majorHiddenDangers: null, // 行业领域重大事故隐患数
  majorHiddenRectified: null, // 行业领域整改重大隐患数
  dualPenaltyCases: null, // 一案双罚条数
  administrativeFines: null, // 行政处罚金额
  casesFiled: null, // 立案查处条数
  fineAmount: null, // 行政处罚金额
  revokedLicenses: null, // 吊销证照数
  suspendedBusinesses: null, // 责令停产停业数

  // 第四部分 社会共治情况字段
  centerStudySessions: null, // 党委中心组学习期数
  businessLeaderTrainingCount: null, // 企业负责人培训期数
  businessLeaderTrainedCount: null, // 企业负责人培训人数
  highRiskTrainingCount: null, // 高危行业从业人员培训人数
  accidentStandardTrainingSessions: null, // 重大事故隐患判定标准宣贯次数
  accidentStandardTrainedPeople: null, // 重大事故隐患判定标准培训人数
  highRiskInsuranceRequired: null, // 高危企业安责险应保企业数
  highRiskInsured: null, // 高危企业安责险在保企业数
  reportsHandled: null, // 举报处理件数
  rewardAmount: null, // 举报奖励金额
  dangersResolvedByReports: null, // 举报查实消除隐患数
  illegalActionsStopped: null // 举报制止违法行为数
})

const handleCancel = () => {
  replace(query.backPath)
}

const handleSubmit = async () => {
  const submitData: any = []
  for (const key in formData.value) {
    if (Object.hasOwnProperty.call(formData.value, key)) {
      const element = formData.value[key]
      if (element) {
        submitData.push({
          yearWorkId: query.id,
          attribute: key,
          attributeValue: element
        })
      }
    }
  }
  await WorkReportApi.createWorkReport(submitData)
  message.success('保存成功')
  handleCancel()
}

const getDetailInfo = async () => {
  const res = await WorkReportApi.getWorkReportPage({
    pageSize: -1,
    yearWorkId: query.id
  })
  res.list.forEach((item) => {
    if (formData.value.hasOwnProperty(item.attribute)) {
      formData.value[item.attribute] = item.attributeValue
    }
  })
}
const flag = ref()
onMounted(() => {
  flag.value = query.type
  getDetailInfo()
})
</script>

<style lang="scss" scoped>
:deep(.el-input-number) {
  width: 120px;
}
.container {
  background-color: white;
  padding: 40px;
}

.content-title {
  font-size: 22px;
  margin: 15px 0;
}
</style>
