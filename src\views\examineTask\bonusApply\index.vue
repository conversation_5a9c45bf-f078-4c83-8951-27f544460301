<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="108px">
      <el-form-item label="被考核单位" prop="unitName">
        <el-input
          v-model="queryParams.unitName"
          placeholder="请输入被考核单位"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" type="primary"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-row class="mb-2">
      <el-button type="primary" plain @click="handleView('create')" v-if="!isExaminingUnit">
        <Icon icon="ep:plus" class="mr-5px" /> 填报
      </el-button>
      <el-button type="success" plain @click="handleExport" :loading="exportLoading">
        <Icon icon="ep:download" class="mr-5px" /> 导出
      </el-button>
    </el-row>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="被考核单位" align="center" prop="unitName" />
      <el-table-column label="年份" align="center" prop="year" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="主要工作情况" align="center" prop="mainWork" />
      <el-table-column label="附件" align="center" prop="attachment">
        <template #default="scope">
          <FileListPreview class="justify-center" :fileUrl="scope.row.attachment" />
        </template>
      </el-table-column>
      <el-table-column label="联系人" align="center" prop="contactPerson" />
      <el-table-column label="联系方式" align="center" prop="contactPhone" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.REPORT_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <template v-if="isExaminingUnit">
            <!-- 考核单位只能看详情 -->
            <el-button link type="primary" @click="handleView('detail', scope.row.id)">
              详情
            </el-button>
          </template>
          <template v-else>
            <!-- 非考核单位可以编辑和提交 -->
            <el-button
              v-if="scope.row.status !== 1"
              link
              type="primary"
              @click="handleView('update', scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="success"
              @click="handleSubmit(scope.row.id)"
              v-if="scope.row.status !== 1"
            >
              提交
            </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ApplyApi, ApplyVO } from '@/api/examineTask/bonusApply'
import { useCache } from '@/hooks/web/useCache'

/** 考核奖金申报 列表 */
defineOptions({ name: 'Apply' })

const { wsCache } = useCache()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const route = useRoute()

const loading = ref(true) // 列表的加载中
const list = ref<ApplyVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  unitId: undefined,
  unitName: undefined,
  projectName: undefined,
  mainWork: undefined,
  attachment: undefined,
  contactPerson: undefined,
  contactPhone: undefined,
  createTime: [],
  status: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 计算属性：是否为考核单位 */
const isExaminingUnit = computed(() => {
  return route.path === '/examineProcess/bonusApply'
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const year = wsCache.get('year')
    const data = await ApplyApi.getApplyPage({
      ...queryParams,
      year,
      status: isExaminingUnit.value ? 1 : queryParams.status
    })
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 跳转到表单页面 */
const handleView = (type: string, id?: number) => {
  router.push({
    path: '/bonusApplyForm',
    query: {
      type,
      ...(id && { id })
    }
  })
}

/** 提交操作 */
const handleSubmit = async (id: number) => {
  try {
    // 提交的二次确认
    await message.confirm(`确认要提交该申请表吗？`)

    const updateData: any = { id, status: 1 }

    // 发起提交
    await ApplyApi.updateApply(updateData)
    message.success(`申请表提交成功`)
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ApplyApi.exportApply(queryParams)
    download.excel(data, '考核奖金申报.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
