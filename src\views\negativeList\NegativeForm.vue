<!--
 * @Description: 负面清单表单
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-09-06 09:51:55
 * @LastEditTime: 2024-10-31 15:52:45
-->

<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="被考核单位" prop="evaluatedUnit">
        <el-tree-select
          ref="treeRef"
          class="!w-full"
          v-model="formData.evaluatedUnit"
          :data="deptList"
          multiple
          :default-expanded-keys="[101]"
          :props="defaultProps"
          node-key="id"
          placeholder="请选择被考核单位"
          show-checkbox
        />
      </el-form-item>
      <el-form-item label="负面清单内容" prop="content">
        <el-input
          :autosize="{ minRows: 2, maxRows: 4 }"
          type="textarea"
          v-model="formData.content"
          placeholder="请输入负面清单内容"
        />
      </el-form-item>
      <el-form-item label="扣分" prop="deductScore">
        <el-input-number
          class="!w-100%"
          :min="0"
          controls-position="right"
          v-model="formData.deductScore"
          placeholder="请输入扣分"
        />
      </el-form-item>
      <el-form-item label="附件" prop="attachments">
        <UploadFile v-model="formData.attachments" />
      </el-form-item>
      <el-form-item label="说明" prop="remark">
        <el-input
          :autosize="{ minRows: 2, maxRows: 4 }"
          type="textarea"
          v-model="formData.remark"
          placeholder="请输入说明"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { defaultProps, handleTree } from '@/utils/tree'
import { NegativeApi, NegativeVO } from '@/api/negativeList'
import * as DeptApi from '@/api/system/dept'
import { cloneDeep } from 'lodash-es'

/** 负面清单 表单 */
defineOptions({ name: 'NegativeForm' })

const props = defineProps({
  assessmentYear: {
    type: Number
  }
})
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const treeRef = ref()
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<any>({
  id: undefined,
  evaluatedUnit: undefined,
  evaluatedUnitName: undefined,
  content: undefined,
  deductScore: undefined,
  attachments: undefined,
  remark: undefined
})
const formRules = reactive({
  evaluatedUnit: [{ required: true, message: '被考核单位不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '负面清单内容不能为空', trigger: 'blur' }],
  deductScore: [{ required: true, message: '扣分不能为空', trigger: 'blur' }]
})
const deptList = ref<any>([])
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  const deptRes = await DeptApi.getChildDeptList({ id: 101, hasParent: true })
  deptList.value = handleTree(deptRes, 'id')
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await NegativeApi.getNegative(id)
      formData.value.evaluatedUnit = formData.value.evaluatedUnit.split(',').map(Number)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const submitData = cloneDeep(formData.value)
    submitData.evaluatedUnit = submitData.evaluatedUnit.join(',')
    submitData.evaluatedUnitName = treeRef.value
      .getCheckedNodes(true)
      .map((item) => item.name)
      .join(',')
    if (formType.value === 'create') {
      submitData.assessmentYear = props.assessmentYear
      await NegativeApi.createNegative(submitData)
      message.success(t('common.createSuccess'))
    } else {
      await NegativeApi.updateNegative(submitData)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    evaluatedUnit: undefined,
    evaluatedUnitName: undefined,
    content: undefined,
    deductScore: undefined,
    attachments: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>
