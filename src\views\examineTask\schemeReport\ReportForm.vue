<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="方案名称" prop="planName">
        <el-input
          v-model="formData.planName"
          placeholder="请输入方案名称"
          :disabled="isDetail || operationType === 'final'"
        />
      </el-form-item>

      <!-- 详情模式显示所有信息 -->
      <template v-if="isDetail">
        <!-- 报备信息 -->
        <div class="mb-4">
          <el-form-item label="报备描述" prop="remark">
            <el-input
              disabled
              type="textarea"
              :autosize="{
                maxRows: 10,
                minRows: 3
              }"
              v-model="formData.remark"
              placeholder="暂无描述"
            />
          </el-form-item>
          <el-form-item label="报备方案">
            <FileListPreview v-if="formData.reportPlan" :fileUrl="formData.reportPlan" />
            <div v-else class="text-gray-400">暂无文件</div>
          </el-form-item>
          <el-form-item label="报备时间">
            {{ formatDate(formData.reportTime) }}
          </el-form-item>
          <el-form-item label="报备状态">
            <dict-tag :type="DICT_TYPE.REPORT_STATUS" :value="formData.reportStatus" />
          </el-form-item>
        </div>

        <!-- 正式信息 -->
        <div>
          <el-form-item label="正式描述" prop="remark1">
            <el-input
              type="textarea"
              :autosize="{
                maxRows: 10,
                minRows: 3
              }"
              v-model="formData.remark1"
              placeholder="暂无描述"
              disabled
            />
          </el-form-item>
          <el-form-item label="正式方案">
            <FileListPreview v-if="formData.finalPlan" :fileUrl="formData.finalPlan" />
            <div v-else class="text-gray-400">暂无文件</div>
          </el-form-item>
          <el-form-item label="正式填报时间">
            {{ formatDate(formData.finalTime) }}
          </el-form-item>
          <el-form-item label="正式填报状态">
            <dict-tag :type="DICT_TYPE.REPORT_STATUS" :value="formData.finalStatus" />
          </el-form-item>
        </div>
      </template>

      <!-- 报备操作相关字段 -->
      <template v-else-if="!operationType || operationType === 'report'">
        <el-form-item label="描述" prop="remark">
          <el-input
            type="textarea"
            :autosize="{
              maxRows: 10,
              minRows: 5
            }"
            v-model="formData.remark"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="报备方案" prop="reportPlan">
          <UploadFile v-model="formData.reportPlan" :fileType="['pdf', 'docx']" />
        </el-form-item>
      </template>

      <!-- 正式操作相关字段 -->
      <template v-else-if="operationType === 'final'">
        <el-form-item label="描述" prop="remark1">
          <el-input
            type="textarea"
            :autosize="{
              maxRows: 10,
              minRows: 5
            }"
            v-model="formData.remark1"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="正式方案" prop="finalPlan">
          <UploadFile v-model="formData.finalPlan" :fileType="['pdf', 'docx']" />
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <el-button v-if="!isDetail" @click="submitForm" type="primary" :disabled="formLoading"
        >确 定</el-button
      >
      <el-button @click="dialogVisible = false">{{ isDetail ? '关 闭' : '取 消' }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ReportApi, ReportVO } from '@/api/examineTask/schemeReport'
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { cloneDeep } from 'lodash-es'

/** 考核方案报备 表单 */
defineOptions({ name: 'ReportForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const props = defineProps({
  year: {
    type: String,
    default: ''
  }
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const operationType = ref<'report' | 'final' | ''>('') // 操作类型：report - 报备操作；final - 正式操作
const formData: any = ref({
  id: undefined,
  unitId: undefined,
  year: props.year,
  planName: undefined,
  reportPlan: undefined,
  reportTime: undefined,
  reportStatus: undefined,
  finalPlan: undefined,
  finalTime: undefined,
  finalStatus: undefined,
  remark: undefined,
  remark1: undefined
})

// 是否为详情模式
const isDetail = computed(() => formType.value === 'detail')

// 动态表单验证规则
const formRules = computed(() => {
  // 详情模式不需要验证
  if (isDetail.value) {
    return {}
  }

  const baseRules = {
    planName: [{ required: true, message: '方案名称不能为空', trigger: 'blur' }],
    reportPlan: [{ required: true, message: '报备方案不能为空', trigger: 'blur' }],
    finalPlan: [{ required: true, message: '正式方案不能为空', trigger: 'blur' }]
  }

  // 根据操作类型添加相应的验证规则
  if (operationType.value === 'report') {
    return {
      ...baseRules,
      reportPlan: [{ required: true, message: '报备方案不能为空', trigger: 'blur' }]
    }
  } else if (operationType.value === 'final') {
    return {
      ...baseRules,
      finalPlan: [{ required: true, message: '正式方案不能为空', trigger: 'blur' }]
    }
  }

  return baseRules
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number, opType?: 'report' | 'final') => {
  dialogVisible.value = true
  formType.value = type
  operationType.value = opType || ''

  // 根据操作类型设置弹窗标题
  if (type === 'detail') {
    dialogTitle.value = '方案详情'
  } else if (opType === 'report') {
    dialogTitle.value = type === 'create' ? '新增报备方案' : '编辑报备方案'
  } else if (opType === 'final') {
    dialogTitle.value = '编辑正式方案'
  } else {
    dialogTitle.value = t('action.' + type)
  }

  resetForm()
  // 修改或详情时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ReportApi.getReport(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 详情模式下不允许提交
  if (isDetail.value) {
    return
  }

  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = cloneDeep(formData.value)
    if (formType.value === 'create') {
      await ReportApi.createReport(data)
      message.success(t('common.createSuccess'))
    } else {
      await ReportApi.updateReport(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    unitId: undefined,
    year: props.year,
    planName: undefined,
    reportPlan: undefined,
    reportTime: undefined,
    reportStatus: undefined,
    finalPlan: undefined,
    finalTime: undefined,
    finalStatus: undefined,
    remark: undefined,
    remark1: undefined
  }
  formRef.value?.resetFields()
}
</script>
