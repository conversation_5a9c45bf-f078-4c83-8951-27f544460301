<!--
 * @Description: 政策解读
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-29 16:11:40
 * @LastEditTime: 2024-07-30 11:32:31
-->

<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="88px"
    >
      <el-form-item label="政策标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入政策标题"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="上传时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PUBLISH_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>

      <el-row :gutter="10" class="mb-8">
        <el-col :span="1.5">
          <el-button type="primary" plain @click="openForm('create')">
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="序号" align="center" type="index" width="60" />
      <el-table-column label="政策标题" align="center" prop="title" min-width="240">
        <template #default="scope">
          <div class="listLink" @click="loadDocument(scope.row)">
            {{ scope.row.title }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="内容" align="center" prop="content" min-width="200">
        <template #default="scope">
          <div v-html="scope.row.content"> </div>
        </template>
      </el-table-column>
      <el-table-column label="附件" align="center" prop="attachments" min-width="80">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.attachments" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PUBLISH_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        align="center"
        prop="createTime"
        width="190"
        :formatter="dateFormatter"
      />
      <el-table-column label="操作" width="190" align="center">
        <template #default="scope">
          <el-button
            v-if="scope.row.status === 0"
            link
            type="warning"
            @click="handlePublish(scope.row.id)"
          >
            发布
          </el-button>
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <InterpretationForm ref="formRef" @success="getList" />
  <DocumentForm ref="htmlDialogRef" />
  <PreviewDialog ref="previewDialogRef" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { InterpretationApi } from '@/api/policyInterpret'
import InterpretationForm from './InterpretationForm.vue'

defineOptions({ name: 'PolicyInterpret' })
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { query } = useRoute()
const type = query.type
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams: any = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  userId: undefined,
  contentType: undefined,
  receiveObjs: undefined,
  content: undefined,
  status: undefined,
  attachments: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const htmlDialogRef = ref()
const previewDialogRef = ref()
const loadDocument = (row) => {
  if (row.contentType === 1) {
    htmlDialogRef.value.fileLoad(row.content)
  } else if (row.contentType === 2) {
    previewDialogRef.value.fileLoad(row.attachments)
  }
}
/** 查询公告列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.type = type
    const data = await InterpretationApi.getInterpretationPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1

  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await InterpretationApi.deleteInterpretation(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 发布按钮操作 */
const handlePublish = async (id: number) => {
  try {
    // 发布的二次确认
    await message.confirm('是否发布所选中数据？')
    // 发起发布
    await InterpretationApi.updateInterpretation({ id, status: 1 })
    message.success(t('发布成功'))
    getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-link) {
  width: 100%;
  text-align: center;
}
</style>
