<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="督查督办名称" prop="supervisionName">
        <el-input v-model="formData.supervisionName" placeholder="请输入督办名称" />
      </el-form-item>
      <el-form-item label="督查督办内容" prop="content">
        <el-input
          type="textarea"
          :autosize="{
            minRows: 2,
            maxRows: 4
          }"
          placeholder="请输入督办内容"
          v-model="formData.content"
          height="150px"
        />
      </el-form-item>
      <el-form-item label="被考核单位" prop="evaluatedUnit">
        <el-select ref="evaluatedUnitRef" class="w-100%" v-model="formData.evaluatedUnit">
          <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="截止日期" prop="dueDate">
        <el-date-picker
          class="!w-full"
          v-model="formData.dueDate"
          type="date"
          :disabled-date="disabledDate"
          value-format="x"
          placeholder="选择截止日期"
        />
      </el-form-item>
      <!-- <el-form-item label="关联扣分规则" prop="relatedPenaltyRule">
        <el-select
          class="w-100%"
          v-model="formData.relatedPenaltyRule"
          clearable
          placeholder="请选择关联扣分规则"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.DEDUCTION_RULES)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { SupervisionTasksApi, SupervisionTasksVO } from '@/api/supervision'
import * as DeptApi from '@/api/system/dept'
import { cloneDeep } from 'lodash-es'

/** 督查督办 表单 */
defineOptions({ name: 'SupervisionTasksForm' })
const props = defineProps({
  assessmentYear: {
    type: Number
  }
})
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const deptList = ref<any>([])
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const evaluatedUnitRef = ref()
const formData = ref({
  id: undefined,
  supervisionName: undefined,
  content: undefined,
  evaluatedUnit: undefined,
  dueDate: undefined,
  relatedPenaltyRule: '1',
  status: undefined,
  completionStatus: undefined
})
const formRules = reactive({
  supervisionName: [{ required: true, message: '督办名称不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '督办内容不能为空', trigger: 'blur' }],
  evaluatedUnit: [{ required: true, message: '被考核单位不能为空', trigger: 'blur' }],
  dueDate: [{ required: true, message: '截止日期不能为空', trigger: 'blur' }],
  relatedPenaltyRule: [{ required: true, message: '关联扣分规则不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  deptList.value = await DeptApi.getChildDeptList({ id: 102, hasParent: false })
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SupervisionTasksApi.getSupervisionTasks(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const submitData = cloneDeep(formData.value) as any
    submitData.assessmentYear = props.assessmentYear
    if (formType.value === 'create') {
      await SupervisionTasksApi.createSupervisionTasks(submitData)
      message.success(t('common.createSuccess'))
    } else {
      await SupervisionTasksApi.updateSupervisionTasks(submitData)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const disabledDate = (time) => {
  const now = new Date().getTime()
  return now > time
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    supervisionName: undefined,
    content: undefined,
    evaluatedUnit: undefined,
    dueDate: undefined,
    relatedPenaltyRule: '1',
    status: undefined,
    completionStatus: undefined
  }
  formRef.value?.resetFields()
}
</script>
