<!--
 * @Description: 突出问题隐患区域场所综合治理情况
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-18 08:58:14
 * @LastEditTime: 2024-10-28 11:34:01
-->

<template>
  <ContentWrap>
    <div class="flex items-center mb-15px">
      <el-button icon="Back" type="primary" @click="handleBack">返 回</el-button>
    </div>
    <div class="f-s">
      <div
        v-for="item in tabList"
        @click="handleTabChange(item)"
        class="w-180px h-35px bg-[#f4f4f4] f-c mr-15px text-14px cursor-pointer"
        :class="{ '!bg-[#c2e1fe]': item.id === currentTab.id }"
      >
        {{ item.label }}
      </div>
    </div>
    <el-row class="mb2">
      <el-button
        class="ml-auto"
        type="success"
        plain
        @click="handleExport"
        :loading="exportLoading"
      >
        <Icon class="mr-5px" icon="ep:upload" /> 导 出
      </el-button>
    </el-row>
    <el-table row-class-name="h-68px" loading="loading" :data="list" v-loading="loading">
      <el-table-column label="序号" align="center" type="index" width="60" />
      <el-table-column label="区域" align="center" prop="unitName" width="250" />
      <el-table-column
        label="主要成效"
        align="left"
        header-align="center"
        prop="accomplishmentList"
      >
        <template #default="{ row }">
          <div class="pb-30px">
            <div
              class="flex mb-5px items-center"
              v-for="(item, index) in row.editData.accomplishmentList"
            >
              <span>{{ index + 1 }}.</span>
              <el-input
                class="mr-10px"
                type="textarea"
                v-if="row.accomplishmentListIsEditing"
                v-model="row.editData.accomplishmentList[index]"
              />
              <span class="mr-20px" v-else>{{ item }}</span>
              <el-button
                v-if="row.accomplishmentListIsEditing"
                class="ml-auto"
                type="danger"
                circle
                @click="handleDelete(row, 'accomplishmentList', index)"
              >
                <Icon icon="ep:delete" />
              </el-button>
            </div>
            <div class="position-absolute right-12px bottom-0" v-if="flag === 'edit'">
              <template
                v-if="row.editData.accomplishmentList && row.editData.accomplishmentList.length > 0"
              >
                <el-button
                  class="ml-auto"
                  v-if="row.accomplishmentListIsEditing"
                  type="success"
                  circle
                  @click="handleConfirm(row, 'accomplishmentList')"
                >
                  <Icon icon="ep:check" />
                </el-button>
                <el-button
                  class="ml-auto"
                  v-else
                  type="primary"
                  circle
                  @click="handleEdit(row, 'accomplishmentList')"
                >
                  <Icon icon="ep:edit" />
                </el-button>
              </template>
              <el-button @click="handleAddRow(row, 'accomplishmentList')" type="primary" circle>
                <Icon icon="ep:plus" />
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="突出问题"
        align="left"
        header-align="center"
        prop="outstandingIssueList"
      >
        <template #default="{ row }">
          <div class="pb-30px">
            <div
              class="flex mb-5px items-center"
              v-for="(item, index) in row.editData.outstandingIssueList"
            >
              <span>{{ index + 1 }}.</span>
              <el-input
                class="mr-10px"
                type="textarea"
                v-if="row.outstandingIssueListIsEditing"
                v-model="row.editData.outstandingIssueList[index]"
              />
              <span class="mr-20px" v-else>{{ item }}</span>
              <el-button
                v-if="row.outstandingIssueListIsEditing"
                class="ml-auto"
                type="danger"
                circle
                @click="handleDelete(row, 'outstandingIssueList', index)"
              >
                <Icon icon="ep:delete" />
              </el-button>
            </div>
            <div class="position-absolute right-12px bottom-0" v-if="flag === 'edit'">
              <template
                v-if="
                  row.editData.outstandingIssueList && row.editData.outstandingIssueList.length > 0
                "
              >
                <el-button
                  class="ml-auto"
                  v-if="row.outstandingIssueListIsEditing"
                  type="success"
                  circle
                  @click="handleConfirm(row, 'outstandingIssueList')"
                >
                  <Icon icon="ep:check" />
                </el-button>
                <el-button
                  class="ml-auto"
                  v-else
                  type="primary"
                  circle
                  @click="handleEdit(row, 'outstandingIssueList')"
                >
                  <Icon icon="ep:edit" />
                </el-button>
              </template>
              <el-button @click="handleAddRow(row, 'outstandingIssueList')" type="primary" circle>
                <Icon icon="ep:plus" />
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { WorkEvaluationApi } from '@/api/rootCause'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'ExamineSituation' })
const currentTab = ref({
  id: 0,
  label: '各区域'
})
const tabList = [
  {
    id: 0,
    label: '各区域'
  },
  {
    id: 1,
    label: '成员单位'
  }
]
const handleTabChange = (item) => {
  currentTab.value = item
  getList()
}

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const { push, currentRoute } = useRouter()
const { delView } = useTagsViewStore() // 视图操作
const loading = ref(true) // 列表的加载中
const list = ref<any>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams: any = ref({
  quarter: route.query.quarter,
  createTime: []
})
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const queryData = {
      ...queryParams.value,
      evaluationType: currentTab.value.id
    }
    const data = await WorkEvaluationApi.getWorkEvaluationPage(queryData)
    list.value = data.list?.map((row) => ({
      ...row,
      editData: {
        accomplishmentList: row.accomplishmentList || [],
        outstandingIssueList: row.outstandingIssueList || []
      },
      accomplishmentListIsEditing: false,
      outstandingIssueListIsEditing: false
    }))
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 新增一行
const handleAddRow = (row, field) => {
  row.editData[field].push('')
  handleEdit(row, field)
}
// 修改状态
const handleEdit = (row, field) => {
  row[`${field}IsEditing`] = true
}
// 确定按钮
const handleConfirm = async (row, field) => {
  // 提交请求
  try {
    const submitData = {
      [field]: row.editData[field],
      id: row.id,
      unitId: row.unitId,
      unitName: row.unitName,
      quarter: route.query.quarter,
      evaluationType: currentTab.value.id
    }
    await WorkEvaluationApi.saveOrUpdateWorkEvaluation(submitData)
  } finally {
    message.success('操作成功')
    getList()
  }
}

// 删除某行
const handleDelete = async (row, field, index) => {
  await message.confirm('是否确认删除该项？')
  row.editData[field].splice(index, 1)
  message.success('删除成功')
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await WorkEvaluationApi.exportWorkEvaluation({
      ...queryParams.value,
      evaluationType: currentTab.value.id
    })
    download.excel(data, '各区各部门工作考评情况.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const handleBack = () => {
  delView(unref(currentRoute))
  // @ts-ignore
  push(route.query.backPath)
}
const flag = ref()
const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(() => {
  flag.value = route.query.flag
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>
