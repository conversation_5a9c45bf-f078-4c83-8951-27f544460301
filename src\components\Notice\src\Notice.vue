<!--
 * @Description: 
 * @Author: sunyunwu
 * @LastEditors: sunyunwu
 * @Date: 2024-07-15 13:38:49
 * @LastEditTime: 2024-07-16 16:21:41
-->
<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="1200" top="10px">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="0px">
      <el-form-item label="" prop="content">
        <Editor v-model="formData.content" height="500px" id="pdfDom" />
      </el-form-item>
    </el-form>
    <img :src="imgData" />
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <!-- <el-button type="warning" plain @click="handleView" :loading="formLoading"> 预览 </el-button>
      <el-button type="success" plain @click="handleExport" :loading="formLoading">
        导出
      </el-button> -->
      <el-button :disabled="formLoading" type="primary" @click="submitForm">通知</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import { TaskApi, TaskVO } from '@/api/evaluation/task'

import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import { ElMessageBox } from 'element-plus'
defineOptions({ name: 'Notice' })

const { t } = useI18n()
const message = useMessage()
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formData = ref({
  content: ''
})
const formRules = reactive({
  content: [{ required: true, message: '内容不能为空', trigger: 'blur' }]
})
const formRef = ref()

const taskData: any = ref({})

const open = async (title, content) => {
  dialogTitle.value = title
  formData.value.content = content
  dialogVisible.value = true
}
defineExpose({ open })

const emit = defineEmits(['process'])

const submitForm = async () => {
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  console.log(formData.value.content)
  emit('process',formData.value.content)
}
const handleView = () => {
  window.print()
}
const imgData = ref()
const handleExport = () => {
  const dom = document.getElementById('pdfDom')?.getElementsByClassName('w-e-scroll')[0]

  /* html2canvas(dom, { allowTaint: true, useCORS: true, scale: 1 }).then((canvas) => {
    const contentWidth = canvas.width
    const contentHeight = canvas.height
    const pageHeight = (contentWidth / 592.28) * 841.89
    let leftHeight = contentHeight
    let position = 0
    const imgwidth = 595.28
    const imgHeight = (592.28 / contentWidth) * contentHeight
    const pageData=canvas.toDataURL("img/jpeg",1.0);
    const PDF = new jsPDF('', 'pt', 'a4')
    if (leftHeight < pageHeight) {
      PDF.addImage(pageData, 'JPEG', 0, 0, imgwidth, imgHeight)
    } else {
      while (leftHeight > 0) {
        PDF.addImage(pageData, 'JPEG', 0, position, imgwidth, imgHeight)
        leftHeight -= pageHeight
        position -= 841.89
        if (leftHeight > 0) {
          PDF.addPage()
        }
      }
    }
    console.log(pageData)
    PDF.save(`${dialogTitle.value}.pdf`)
  }) */
  /* console.log(exportContent.scrollHeight)
  // scale 2 提升图片质量
  html2canvas(exportContent, {
    useCORS: true,
    scale:1,

    height: exportContent.scrollHeight,
    windowHeight: exportContent.scrollHeight
  }).then((data) => {
    let imgUrlData = data.toDataURL('image/png', 1)
    imgData.value = imgUrlData
    const pdfWidth = 210
    const pdf = new jsPDF('', '', '')

    pdf.addImage(
      imgUrlData,
      'png',
      10,
      10,
      pdfWidth - 20,
      ((pdfWidth - 20) / data.width) * data.height
    )
    pdf.save(`${dialogTitle.value}.pdf`)
  }) */
}
</script>
