/*
 * @Description: 
 * @Author: sun<PERSON><PERSON>
 * @LastEditors: sunyunwu
 * @Date: 2024-06-04 17:07:26
 * @LastEditTime: 2024-06-14 10:13:41
 */
import variables from '@/styles/global.module.scss'

export const useDesign = () => {
  const scssVariables = variables

  /**
   * @param scope 类名
   * @returns 返回空间名-类名
   */
  const getPrefixCls = (scope: string) => {
    return `${scssVariables.namespace}-${scope}`
  }

  return {
    variables: scssVariables,
    getPrefixCls
  }
}
