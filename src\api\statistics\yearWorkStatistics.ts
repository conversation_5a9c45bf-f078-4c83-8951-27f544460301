import request from '@/config/axios'

// t1  :   已完成数
// t2  :   已完成百分比
// t3  :   未完成数
// t4  :   未完成百分比
export async function getMeetingData(params?: any) {
  return await request.get({ url: `/system/statistics/get-meetings-data`, params })
}

export async function getSafetyWorkData(quarter) {
  return await request.get({ url: `/system/statistics/get-safety-work-data?year=` + quarter })
}
export async function getCheckYearData(quarter) {
  return await request.get({ url: `/system/statistics/get-check-data?quarter=` + quarter })
}
export async function getAccidentData(quarter) {
  return await request.get({ url: `/system/statistics/get-accident-data?quarter=` + quarter })
}
