<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="95%" class="temDialog">
    <el-row v-if="actionType == 'set'" style="margin-bottom: 10px">
      <el-button type="warning" plain @click="handleImport">
        <Icon icon="ep:upload" /> 导入
      </el-button>
      <el-button type="primary" plain @click="openForm('create')">
        <Icon icon="ep:plus" class="mr-5px" /> 新增
      </el-button>
    </el-row>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      :span-method="objectSpanMethod"
      height="600"
    >
      <el-table-column type="index" align="center" label="序号" />
      <el-table-column label="类型" align="center" prop="primaryElement" />
      <el-table-column label="指标名称" align="center" prop="secondaryElement" />
      <el-table-column label="评分标准" align="center" prop="thirdElement" width="200"/>
      <el-table-column label="分值" align="center" prop="standardScore" width="80" />
      <el-table-column label="治本攻坚" align="center" prop="campaignMark" width="150">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ROOT_CAUSE_TACKLING" :value="scope.row.campaignMark" />
        </template>
      </el-table-column>
      <el-table-column label="分值" align="center" prop="standardScore" width="80" />
      <el-table-column label="评分规则" align="center" prop="examineContent" min-width="120" />
      <el-table-column label="适用范围" align="center" prop="applicationScope" min-width="150">
        <template #default="scope">
          <span v-if="scope.row.applicationScope">
            {{ formatApplicationScope(scope.row.applicationScope) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="计算类型" align="center" prop="calculateType" width="100">
        <template #default="scope">
          {{ getCalculateTypeLabel(scope.row.calculateType) }}
        </template>
      </el-table-column>
      <el-table-column label="运算符" align="center" prop="operator" width="80">
        <template #default="scope">
          {{ getOperatorLabel(scope.row.operator) }}
        </template>
      </el-table-column>
      <el-table-column label="阈值" align="center" prop="threshold" width="80" />
      <el-table-column label="单项扣分值" align="center" prop="singleDeductScore" width="100" />
      <el-table-column label="扣分上限值" align="center" prop="maxDeductScore" width="100" />

      <el-table-column label="操作" align="center" v-if="actionType == 'set'" width="150">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 表单弹窗：添加/修改 -->
    <TemplateEvaluationForm ref="formRef" @success="getList" />

    <!-- 用户导入对话框 -->
    <ImportForm
      ref="importFormRef"
      @success="getList"
      @importTemplate="importTemplate"
      :upUrl="TemplateEvaluationApi.upUrl"
    />
  </Dialog>
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { TemplateEvaluationApi } from '@/api/template/checkTemplate'
import TemplateEvaluationForm from './TemplateEvaluationForm.vue'
import { getCalculateTypeLabel, getOperatorLabel } from '@/utils/constants/templateConfig'
import { getSpanArrays } from '@/utils/tableSpanMethod'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'

/** 考核指标 列表 */
defineOptions({ name: 'ViewSetDialog' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const loading = ref(true) // 列表的加载中
const list = ref<any>([]) // 列表的数据
const actionType = ref('')
const importTemplate = async () => {
  const res = await TemplateEvaluationApi.getImportTemplate({})
  download.excel(res, '考核指标导入模板.xls')
}
const queryParams: any = reactive({
  pageNo: 1,
  pageSize: 100,
  primaryElement: undefined,
  secondaryElement: undefined,
  thirdElement: undefined,
  examineContent: undefined,
  standardScore: undefined,
  examineWay: undefined,
  sort: undefined,
  templateId: undefined,
  createTime: [],
  calculateType: undefined,
  operator: undefined,
  threshold: undefined,
  singleDeductScore: undefined,
  maxDeductScore: undefined
})
const exportLoading = ref(false) // 导出的加载中
/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open({ id: queryParams.templateId })
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TemplateEvaluationApi.getTemplateEvaluationPage(queryParams)
    const ndata = data.list.reverse()
    ndata.forEach((item, index) => {
      // 计算同类目总分
      ndata[index]['score'] = ndata[index].standardScore
      if (index > 0 && ndata[index].primaryElement == ndata[index - 1].primaryElement) {
        ndata[index]['score'] = ndata[index].standardScore + ndata[index - 1].score
      }
    })
    list.value = ndata.reverse()
    getSpanArr()
  } finally {
    loading.value = false
  }
}
// 存储各列的合并信息
const spanArrays: any = ref([])

// 定义每列的合并规则配置（注意：索引0是序号列，不参与合并）
const columnMergeRules = [
  // 序号列不合并，索引为0
  // 第一列：类型 (primaryElement)，索引为1
  ['primaryElement'],
  // 第二列：指标名称 (secondaryElement)，索引为2
  ['primaryElement', 'secondaryElement'],
  // 第三列：评分标准 (thirdElement)，索引为3
  ['primaryElement', 'secondaryElement', 'thirdElement'],
  // 第四列：分值 (standardScore)，索引为4
  ['primaryElement', 'secondaryElement', 'standardScore'],
  // 第五列：治本攻坚 (campaignMark)，索引为5
  ['primaryElement', 'secondaryElement', 'thirdElement', 'campaignMark'],
  // 第六列：评分规则 (examineContent)，索引为6
  ['primaryElement', 'secondaryElement', 'thirdElement', 'examineContent']
  // 第七列：适用范围 (applicationScope)，索引为7（不需要合并）
]

const getSpanArr = () => {
  // 根据配置动态生成各列的合并信息
  spanArrays.value = getSpanArrays(list.value, columnMergeRules)
}

// 生成表格合并方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 只处理需要合并的列（索引1到6，跳过序号列0和适用范围列）
  if (columnIndex >= 1 && columnIndex <= 6) {
    // 由于序号列不参与合并，需要调整索引
    const adjustedIndex = columnIndex - 1
    const spanArr = spanArrays.value[adjustedIndex]
    if (spanArr) {
      const _row = spanArr[rowIndex]
      const _col = _row > 0 ? 1 : 0
      return {
        rowspan: _row,
        colspan: _col
      }
    }
  }
  // 其他列不合并
  return {
    rowspan: 1,
    colspan: 1
  }
}
// 格式化适用范围显示
const formatApplicationScope = (applicationScope: string) => {
  if (!applicationScope) return ''

  const values = applicationScope.split(',')
  const labels: string[] = []

  values.forEach((value) => {
    // 特殊处理district_templates类型
    if (value === 'a') {
      labels.push('中心城区')
    } else if (value === 'b') {
      labels.push('郊区')
    } else if (value === 'district_templates') {
      labels.push('各区')
    } else {
      // 查找对应的标签
      const dict = getStrDictOptions(DICT_TYPE.DISTRICT_TEMPLATES).find((d) => d.value === value)
      if (dict) {
        labels.push(dict.label)
      } else {
        // 尝试在TEMPLATE_TYPE中查找
        const templateDict = getStrDictOptions(DICT_TYPE.TEMPLATE_TYPE).find(
          (d) => d.value === value
        )
        if (templateDict) {
          labels.push(templateDict.label)
        }
      }
    }
  })

  return labels.join(', ')
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id, queryParams.templateId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TemplateEvaluationApi.deleteTemplateEvaluation(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TemplateEvaluationApi.exportTemplateEvaluation(queryParams)
    download.excel(data, '考核指标.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  actionType.value = type
  dialogTitle.value = t('action.' + type)
  queryParams.templateId = id
  getList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
/** 初始化 **/
onMounted(() => {
  // getList()
})
</script>
<style lang="scss">
.temDialog {
  .el-dialog__body {
    padding: 20px !important;
    padding-bottom: 40px !important;
  }
}
</style>
