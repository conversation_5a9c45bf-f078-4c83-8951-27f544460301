<!--
 * @Description: 设置关键词弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-18 13:06:43
 * @LastEditTime: 2024-10-18 16:16:56
-->

<template>
  <Dialog title="设置关键词" v-model="dialogVisible" center top="5vh" width="800">
    <el-form ref="formRef" label-width="100px" v-loading="formLoading" label-position="top">
      <template v-for="(item, index) in fieldList" :key="index">
        <el-form-item :label="item.remark">
          <div class="position-absolute right-0 top--40px">
            <el-button type="primary" @click="addRow(item)">
              <Icon icon="ep:plus" class="mr-5px" />新 增
            </el-button>
          </div>
          <div class="flex flex-col flex-1 gap1">
            <div v-for="(val, indey) in item.keywords" class="flex">
              <el-input v-model="item.keywords[indey]" :placeholder="`请输入${item.remark}`">
                <template #prepend>{{ indey + 1 }}.</template>
              </el-input>

              <el-button @click="removeRow(item.keywords, indey)" class="ml2" circle>
                <Icon icon="ep:delete" />
              </el-button>
            </div>
          </div>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getDictDataPage, batchUpdateDictData } from '@/api/system/dict/dict.data'
import { cloneDeep } from 'lodash-es'

/** 考核模板 表单 */
defineOptions({ name: 'KeyWordForm' })
const props = defineProps({
  dictType: {
    type: String
  }
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const fieldList: any = ref([])
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  resetForm()
  const res = await getDictDataPage({
    value: props.dictType,
    dictType: 'key_words_map',
    pageNum: 1,
    pageSize: -1
  })
  fieldList.value = res.list
  fieldList.value.forEach((item) => {
    if (!item.keywords) item.keywords = []
  })
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    await batchUpdateDictData(fieldList.value)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 增加一行
const addRow = (item) => {
  item.keywords.push('')
}
// 删除一行
const removeRow = async (item, index) => {
  await message.confirm('确定要删除该项吗？')
  item.splice(index, 1)
  message.success('删除成功')
}

/** 重置表单 */
const resetForm = () => {
  fieldList.value = []
  formRef.value?.resetFields()
}
</script>
