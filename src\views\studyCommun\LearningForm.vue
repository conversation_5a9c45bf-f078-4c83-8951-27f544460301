<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="1200" center>
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="pr-50px pl-30px"
    >
      <el-form-item label="文件名称" prop="title">
        <el-input v-model="formData.title" placeholder="请输入文件名称" maxlength="30" />
      </el-form-item>
      <el-form-item label="类型" prop="exchangeType">
        <el-select
          class="w-100%"
          v-model="formData.exchangeType"
          clearable
          placeholder="请选择类型"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.EXCHANGE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="内容形式" prop="contentType">
        <el-select
          class="w-100%"
          v-model="formData.contentType"
          clearable
          placeholder="请选择内容形式"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.CONTENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="内容" prop="content" v-if="formData.contentType === '1'">
        <Editor v-model="formData.content" height="150px" />
      </el-form-item>
      <el-form-item label="附件" prop="attachments" v-if="formData.contentType === '2'">
        <UploadFile v-model="formData.attachments" :file-size="100" :limit="1" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="success" @click="handlePreivew">预览</el-button>
    </template>
  </Dialog>
  <DocumentForm ref="htmlDialogRef" />
  <PreviewDialog ref="previewRef" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { LearningApi } from '@/api/studyCommun'
import { cloneDeep } from 'lodash-es'

defineOptions({ name: 'LearningForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<any>({
  id: undefined,
  title: undefined,
  userId: undefined,
  contentType: undefined,
  exchangeType: undefined,
  uploadUnit: undefined,
  uploadTime: undefined,
  publishTime: undefined,
  content: undefined,
  publishStatus: undefined,
  attachments: undefined,
  reviewResult: undefined,
  reviewOpinions: undefined
})
const formRules = reactive({
  title: [{ required: true, message: '文件名称不能为空', trigger: 'blur' }],
  exchangeType: [{ required: true, message: '类型不能为空', trigger: 'change' }],
  contentType: [{ required: true, message: '内容形式不能为空', trigger: 'change' }],
  attachments: [{ required: true, message: '附件不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '内容不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await LearningApi.getLearning(id)
      formData.value.exchangeType = formData.value.exchangeType + ''
      formData.value.contentType = formData.value.contentType + ''
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const submitData = cloneDeep(formData.value)
    if (submitData.contentType === '1') {
      delete submitData.attachments
    } else if (submitData.contentType === '2') {
      delete submitData.content
    }
    if (formType.value === 'create') {
      delete submitData.id
      await LearningApi.createLearning(submitData)
      message.success(t('common.createSuccess'))
    } else {
      await LearningApi.updateLearning(submitData)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    title: undefined,
    userId: undefined,
    contentType: undefined,
    exchangeType: undefined,
    uploadUnit: undefined,
    uploadTime: undefined,
    publishTime: undefined,
    content: undefined,
    publishStatus: undefined,
    attachments: undefined,
    reviewResult: undefined,
    reviewOpinions: undefined
  }
  formRef.value?.resetFields()
}

const previewRef = ref()
const htmlDialogRef = ref()
const handlePreivew = () => {
  if (!formData.value.contentType) return message.warning('暂无内容查看')
  if (formData.value.contentType === '1') {
    if (!formData.value.content) return message.warning('暂无内容查看')
    htmlDialogRef.value.fileLoad(formData.value.content)
  } else if (formData.value.contentType === '2') {
    if (!formData.value.attachments) return message.warning('暂无内容查看')
    previewRef.value.fileLoad(formData.value.attachments)
  }
}
</script>
