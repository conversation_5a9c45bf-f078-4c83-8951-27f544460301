import request from '@/config/axios'

// 考核方案报备 VO
export interface ReportVO {
  id: number // 主键ID
  unitId: number // 被考核单位ID
  year: number // 年份
  planName: string // 方案名称
  reportPlan: string // 报备方案
  reportTime: number // 报备填报时间
  reportStatus: number // 报备状态
  finalPlan: string // 正式方案
  finalTime: number // 正式填报时间
  finalStatus: number // 正式填报状态
}

// 考核方案报备 API
export const ReportApi = {
  // 查询考核方案报备分页
  getReportPage: async (params: any) => {
    return await request.get({ url: `/system/report/page`, params })
  },

  // 查询考核方案报备详情
  getReport: async (id: number) => {
    return await request.get({ url: `/system/report/get?id=` + id })
  },

  // 新增考核方案报备
  createReport: async (data: ReportVO) => {
    return await request.post({ url: `/system/report/create`, data })
  },

  // 修改考核方案报备
  updateReport: async (data: ReportVO) => {
    return await request.put({ url: `/system/report/update`, data })
  },

  // 删除考核方案报备
  deleteReport: async (id: number) => {
    return await request.delete({ url: `/system/report/delete?id=` + id })
  },

  // 导出考核方案报备 Excel
  exportReport: async (params) => {
    return await request.download({ url: `/system/report/export-excel`, params })
  },
}
