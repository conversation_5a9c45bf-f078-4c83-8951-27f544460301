import request from '@/config/axios'

// 燃气安全专项治理燃气管网改造进度 VO
export interface GasPipelineProgressVO {
  id: number // 唯一标识ID
  unitId: number // 考核单位
  unitName: string // 单位名称
  roadPipesImplemented: number // 道路管实施量
  roadPipesRatio: number // 道路管实施量占计划量比例
  streetPipesImplemented: number // 街坊管实施量
  streetPipesRatio: number // 街坊管实施量占计划量比例
  aboveGroundTransformed: number // 地上管改造户数
  status: number // 状态   0-待提交  1-已提交
  quarter: string // 季度
}

// 燃气安全专项治理燃气管网改造进度 API
export const GasPipelineProgressApi = {
  // 查询燃气安全专项治理燃气管网改造进度分页
  getGasPipelineProgressPage: async (params: any) => {
    return await request.get({ url: `/examine/gas-pipeline-progress/page`, params })
  },

  // 查询燃气安全专项治理燃气管网改造进度详情
  getGasPipelineProgress: async (id: number) => {
    return await request.get({ url: `/examine/gas-pipeline-progress/get?id=` + id })
  },

  // 新增燃气安全专项治理燃气管网改造进度
  createGasPipelineProgress: async (data: GasPipelineProgressVO) => {
    return await request.post({ url: `/examine/gas-pipeline-progress/create`, data })
  },

  // 修改燃气安全专项治理燃气管网改造进度
  updateGasPipelineProgress: async (data: GasPipelineProgressVO) => {
    return await request.put({ url: `/examine/gas-pipeline-progress/update`, data })
  },
  
  // 新增修改燃气安全专项治理燃气管网改造进度
  saveOrUpdateGasPipelineProgress: async (data: GasPipelineProgressVO) => {
    return await request.post({ url: `/examine/gas-pipeline-progress/saveOrUpdate`, data })
  },

  // 删除燃气安全专项治理燃气管网改造进度
  deleteGasPipelineProgress: async (id: number) => {
    return await request.delete({ url: `/examine/gas-pipeline-progress/delete?id=` + id })
  },

  // 导出燃气安全专项治理燃气管网改造进度 Excel
  exportGasPipelineProgress: async (params) => {
    return await request.download({ url: `/examine/gas-pipeline-progress/export-excel`, params })
  },

  // 获得导入模板
  getImportTemplate: async (params) => {
    return await request.download({ url: `/examine/gas-pipeline-progress/get-import-template`, params })
  },

  // 导入接口地址
  upUrl: '/examine/gas-pipeline-progress/import'
}
