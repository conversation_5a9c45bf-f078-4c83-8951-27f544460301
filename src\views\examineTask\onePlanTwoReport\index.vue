<!--
 * @Description: 一计划双报告
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-27 11:06:29
 * @LastEditTime: 2025-07-22 09:09:08
-->
<template>
  <ContentWrap>
    <template v-if="!resultFlag">
      <div class="text-20px font-bold mb-20px">一计划双报告</div>
      <el-button
        v-if="route.path === '/examineTask/onePlanTwoReport'"
        type="primary"
        class="mb-20px"
        plain
        @click="openForm"
      >
        <Icon icon="ep:plus" class="mr-5px" /> 填 报
      </el-button>
    </template>
    <el-table v-loading="loading" :data="tableData" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column type="index" label="序号" width="60"></el-table-column>
      <el-table-column label="报告名称" align="center" prop="workName" />
      <el-table-column label="被考核单位" align="center" prop="unitName" />
      <el-table-column label="类型" align="center" prop="workType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PLAN_REPORT_PAIR" :value="scope.row.workType" />
        </template>
      </el-table-column>
      <el-table-column label="附件" align="center" prop="annex" width="200">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.annex" />
        </template>
      </el-table-column>
      <el-table-column
        label="提交时间"
        align="center"
        prop="submitTime"
        width="200"
        :formatter="dateFormatter"
      />
      <el-table-column label="状态" align="center" prop="status" width="120">
        <template #default="scope">
          <dict-tag
            :type="DICT_TYPE.REPORT_STATUS"
            :value="resultFlag ? scope.row.assessmentStatus : scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column v-if="resultFlag" label="分值" align="center" prop="assessmentScore" />
      <el-table-column label="操作" align="center" width="200">
        <template #default="scope">
          <template v-if="!resultFlag">
            <template
              v-if="scope.row.status === 0 && route.path === '/examineTask/onePlanTwoReport'"
            >
              <el-button link type="warning" @click="handleSubmit(scope.row.id)"> 提交 </el-button>
              <el-button
                link
                type="primary"
                v-if="scope.row.workType == 2"
                @click="handleAddEdit('edit', scope.row)"
              >
                编辑
              </el-button>
              <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
            </template>
            <el-button
              v-if="scope.row.status === 1 && scope.row.workType == 2"
              link
              type="primary"
              @click="handleAddEdit('detail', scope.row)"
            >
              详情
            </el-button>
          </template>
          <template v-else>
            <el-button link type="primary" @click="handleAddEdit('detail', scope.row)">
              详情
            </el-button>
            <el-button
              v-if="!scope.row.assessmentScore"
              link
              type="primary"
              @click="handleExamine(scope.row)"
            >
              考核
            </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <ExamineDialog ref="examineDialogRef" @fetch-data="getList" :currentTabId="currentTabId" />
  </ContentWrap>

  <!-- 添加或修改用户对话框 -->
  <ReportForm :year="wsCache.get('year')" ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { SchemeApi } from '@/api/template/examinePlan'
import { WorkApi } from '@/api/examineTask/yearWork'
import ReportForm from './ReportForm.vue'
import { useUserStore } from '@/store/modules/user'
import { useCache } from '@/hooks/web/useCache'

defineOptions({ name: 'YearWork' })

const props = defineProps({
  currentTabId: {
    type: String,
    default: ''
  }
})
const { wsCache } = useCache()
const userStore = useUserStore()
const roles = userStore.getRoles
const route = useRoute()
const resultFlag = inject('resultFlag', false)
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter()
const loading = ref(true) // 列表的加载中
const tableData = ref<any>([]) // 列表的数据
const formRef = ref()
const total = ref(0) // 列表的总页数
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const sessionObj: any = JSON.parse(sessionStorage.getItem('examineResult') as any)
    const data = await WorkApi.getWorkPage({
      pageSize: queryParams.pageSize,
      pageNo: queryParams.pageNo,
      year: resultFlag ? sessionObj.examineYear : wsCache.get('year'),
      unitId: resultFlag ? sessionObj.unitId : undefined,
      status: resultFlag ? 1 : queryParams.status
    })
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const openForm = () => {
  formRef.value.open()
}

const handleAddEdit = (type, row) => {
  const query = {
    id: row.id,
    type,
    backPath: route.path
  }
  if (roles.includes('assessed_unit')) {
    // 成员单位
    push({
      path: '/memberUnitReportCreate',
      query
    })
  } else if (roles.includes('district')) {
    // 区县
    push({
      path: '/districtReportCreate',
      query
    })
  } else if (roles.includes('assessment_unit')) {
    // 考核单位
    switch (row.roleCode) {
      case 'assessed_unit':
        push({
          path: '/memberUnitReportCreate',
          query
        })
        break
      case 'district':
        push({
          path: '/districtReportCreate',
          query
        })
        break
      default:
        break
    }
  }
}

const handleSubmit = async (id) => {
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  await WorkApi.updateWork({ id, status: 1, submitFlag: true })
  message.success('操作成功')
  // 刷新列表
  await getList()
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await WorkApi.deleteWork(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

const examineDialogRef = ref()
const handleExamine = (row) => {
  examineDialogRef.value.openDialog(row)
}

const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(async () => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-link) {
  width: 100%;
  text-align: center;
}
</style>
