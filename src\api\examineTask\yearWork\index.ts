import request from '@/config/axios'

// 年度工作 VO
export interface WorkVO {
  id: number // 主键(自增策略)
  workName: string // 年度工作名称
  workType: number // 类型
  submitTime: Date // 填写时间
  status: number // 状态  0-未提交 1-已提交
}

// 年度工作填报 VO
export interface WorkReportVO {
  id: number // 明细ID
  unitId: number // 被考核单位
  yearWorkId: number // 年度工作ID
  attribute: string // 属性名
  attributeValue: string // 属性值
  sort: number // 顺序
}

// 自评考核 VO
export interface AssessmentVO {
  id: number // 主键(自增策略)
  templateQuotaId: number // 考核模板指标ID
  unitId: number // 被考核单位
  score: number // 评分
  reason: string // 扣分原因
  annex: string // 附件
  remark: string // 内容描述
}

// 年度工作 API
export const WorkApi = {
  // 查询年度工作分页
  getWorkPage: async (params: any) => {
    return await request.get({ url: `/system/yearly/work/page`, params })
  },

  // 查询年度工作详情
  getWork: async (id: number) => {
    return await request.get({ url: `/system/yearly/work/get?id=` + id })
  },

  // 新增年度工作
  createWork: async (data: WorkVO) => {
    return await request.post({ url: `/system/yearly/work/create`, data })
  },

  // 修改年度工作
  updateWork: async (data: any) => {
    return await request.put({ url: `/system/yearly/work/update`, data })
  },

  // 删除年度工作
  deleteWork: async (id: number) => {
    return await request.delete({ url: `/system/yearly/work/delete?id=` + id })
  },

  // 导出年度工作 Excel
  exportWork: async (params) => {
    return await request.download({ url: `/system/yearly/work/export-excel`, params })
  }
}

// 年度工作填报 API
export const WorkReportApi = {
  // 查询年度工作填报分页
  getWorkReportPage: async (params: any) => {
    return await request.get({ url: `/system/yearly/work-report/page`, params })
  },

  // 查询年度工作填报详情
  getWorkReport: async (id: number) => {
    return await request.get({ url: `/system/yearly/work-report/get?id=` + id })
  },

  // 新增年度工作填报
  createWorkReport: async (data: WorkReportVO) => {
    return await request.post({ url: `/system/yearly/work-report/create`, data })
  },

  // 修改年度工作填报
  updateWorkReport: async (data: WorkReportVO) => {
    return await request.put({ url: `/system/yearly/work-report/update`, data })
  },

  // 删除年度工作填报
  deleteWorkReport: async (id: number) => {
    return await request.delete({ url: `/system/yearly/work-report/delete?id=` + id })
  },

  // 导出年度工作填报 Excel
  exportWorkReport: async (params) => {
    return await request.download({ url: `/system/yearly/work-report/export-excel`, params })
  }
}

// 自评考核 API
export const AssessmentApi = {
  // 查询自评考核分页
  getAssessmentPage: async (params: any) => {
    return await request.get({ url: `/system/self/assessment/page`, params })
  },

  // 查询自评考核详情
  getAssessment: async (id: number) => {
    return await request.get({ url: `/system/self/assessment/get?id=` + id })
  },

  // 新增自评考核
  createAssessment: async (data: AssessmentVO) => {
    return await request.post({ url: `/system/self/assessment/create`, data })
  },

  // 修改自评考核
  updateAssessment: async (data: AssessmentVO) => {
    return await request.put({ url: `/system/self/assessment/update`, data })
  },

  // 删除自评考核
  deleteAssessment: async (id: number) => {
    return await request.delete({ url: `/system/self/assessment/delete?id=` + id })
  },

  // 导出自评考核 Excel
  exportAssessment: async (params) => {
    return await request.download({ url: `/system/self/assessment/export-excel`, params })
  }
}
