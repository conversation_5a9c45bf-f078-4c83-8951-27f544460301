<!--
 * @Description: 审核弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-30 13:10:34
 * @LastEditTime: 2024-07-30 14:44:05
-->
<template>
  <Dialog v-model="dialogVisible" title="审核" width="1000" center>
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="pr-50px pl-30px"
    >
      <el-form-item label="集团单位" prop="uploadUnit">
        {{ formData.uploadUnit }}
      </el-form-item>
      <el-form-item label="文件名称" prop="title">
        {{ formData.title }}
      </el-form-item>
      <el-form-item label="审核结果" prop="reviewResult">
        <el-radio-group v-model="formData.reviewResult">
          <el-radio
            v-for="dict in getStrDictOptions(DICT_TYPE.VERIFYING_RESULT)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见" prop="reviewOpinions">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          v-model="formData.reviewOpinions"
          placeholder="请输入审核意见"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { LearningApprovalApi } from '@/api/studyCommun/studyApproval'
import { LearningApi } from '@/api/studyCommun'
import { cloneDeep } from 'lodash-es'

defineOptions({ name: 'AuditDialog' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  learningId: undefined,
  title: undefined,
  uploadUnit: undefined,
  uploadTime: undefined,
  reviewResult: undefined,
  reviewOpinions: undefined
})
const primaryInfo = ref<any>({})
const formRules = reactive({
  reviewResult: [{ required: true, message: '审核结果不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (row) => {
  dialogVisible.value = true
  resetForm()
  formLoading.value = true
  try {
    primaryInfo.value = row
    formData.value.learningId = primaryInfo.value.id
    formData.value.uploadUnit = primaryInfo.value.uploadUnit
    formData.value.title = primaryInfo.value.title
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const submitData = cloneDeep(formData.value)
    await LearningApprovalApi.createLearningApproval(submitData)
    await LearningApi.updateLearning({
      id: primaryInfo.value.id,
      reviewResult: submitData.reviewResult,
      publishStatus: submitData.reviewResult === '1' ? 1 : undefined  // 审批通过才修改发布状态
    })
    message.success('操作成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    learningId: undefined,
    title: undefined,
    uploadUnit: undefined,
    uploadTime: undefined,
    reviewResult: undefined,
    reviewOpinions: undefined
  }
  formRef.value?.resetFields()
}
</script>
