/*
 * @Description: 考核完成率统计模块API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-14 14:19:56
 * @LastEditTime: 2024-08-14 16:44:59
 */

import request from '@/config/axios'

// 获取考核完成情况数据
// t1: 已完成数
// t2: 已完成百分比
// t3: 未完成数
// t4: 未完成百分比
export async function getExamineResultData(params?: any) {
  return await request.get({ url: `/system/statistics/get-examine-result`, params })
}

// 获得过程完成指标数据
export async function getProcessMetricsData(params?: any) {
  return await request.get({ url: `/system/statistics/get-process-metrics`, params })
}

// 获得各区域集团分布数量   取t3
export async function getRegionDistribution(params?: any) {
  return await request.get({ url: `/system/statistics/get-region-distribution`, params })
}
