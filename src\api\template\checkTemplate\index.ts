/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-04 17:07:26
 * @LastEditTime: 2025-07-23 16:53:15
 */
import request from '@/config/axios'

// 考核模板 VO
export interface TemplateEvaluationVO {
  id: number // 主键(自增策略)
  primaryElement: string // 类型
  secondaryElement: string // 指标名称
  thirdElement: string // 评分标准
  evaluationCategory: string // 考评类目
  evaluationItem: string // 考评项目
  evaluationContent: string // 考评内容
  evaluationMethod: string // 考评办法
  standardScore: number // 分值
  templateId: number // 模板主表ID
  calculateType: string // 计算类型
  operator: string // 运算符
  threshold: number // 阈值
  singleDeductScore: number // 单项扣分值
  maxDeductScore: number // 扣分上限值
  examineContent: string // 评分规则
  examineWay: string // 工作方案
  sort: number // 显示排序
  totalScore: number // 考核分值
  campaignMark: string // 治本攻坚考察项标识
  applicationScope: string // 适用范围（逗号分隔的字符串）
  refIds: string // 关联ID（逗号分隔的字符串）
}

// 考核模板 API
export const TemplateEvaluationApi = {
  // 查询考核模板分页
  getTemplateEvaluationPage: async (params: any) => {
    return await request.get({ url: `/system/template-quota/page`, params })
  },

  // 查询考核模板详情
  getTemplateEvaluation: async (id: number) => {
    return await request.get({ url: `/system/template-quota/get?id=` + id })
  },

  // 新增考核模板
  createTemplateEvaluation: async (data: TemplateEvaluationVO) => {
    return await request.post({ url: `/system/template-quota/create`, data })
  },

  // 修改考核模板
  updateTemplateEvaluation: async (data: TemplateEvaluationVO) => {
    return await request.put({ url: `/system/template-quota/update`, data })
  },

  // 删除考核模板
  deleteTemplateEvaluation: async (id: number) => {
    return await request.delete({ url: `/system/template-quota/delete?id=` + id })
  },

  // 导出考核模板 Excel
  exportTemplateEvaluation: async (params) => {
    return await request.download({ url: `/system/template-quota/export-excel`, params })
  },

  // 获得导入考核模板
  getImportTemplate: async (params) => {
    return await request.download({ url: `/system/template-quota/get-import-template`, params })
  },
  // 导入考核模板数据
  templateEvaluation: async (data) => {
    return await request.post({ url: `/system/template-quota/import`, data })
  },

  upUrl: '/system/template-quota/import'
}
