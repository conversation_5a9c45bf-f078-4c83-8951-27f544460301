<template>
  <el-row :gutter="20">
    <!-- 左侧部门树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap class="h-1/1">
        <DeptTree @node-click="handleDeptNodeClick" />
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">
      <!-- 搜索 -->
      <ContentWrap>
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="88px"
        >
          <el-form-item label="所属区域" prop="region">
            <el-select
              v-model="queryParams.region"
              placeholder="请选择区域"
              clearable
              class="!w-240px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.BELONG_REGION)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="所属行业" prop="industry">
            <el-input
              v-model="queryParams.industry"
              placeholder="请选择行业"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="电话" prop="managerPhone">
            <el-input
              v-model="queryParams.managerPhone"
              placeholder="请输入电话"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="负责人" prop="managerUser">
            <el-input
              v-model="queryParams.managerUser"
              placeholder="请选择负责人"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery"><Icon icon="ep:search" />搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
          </el-form-item>
          <div>
            <el-form-item>
              <el-button
                type="primary"
                plain
                @click="openForm('create')"
                v-hasPermi="['system:user:create']"
              >
                <Icon icon="ep:plus" /> 新增
              </el-button>
              <el-button
                type="success"
                plain
                @click="handleExport"
                :loading="exportLoading"
                v-hasPermi="['system:user:export']"
              >
                <Icon icon="ep:download" />导出
              </el-button>
            </el-form-item></div
          >
        </el-form>
      </ContentWrap>
      <ContentWrap>
        <el-table
          
          v-loading="loading"
          :data="list"
        >
          <el-table-column label="序号" align="center" width="60px">
            <template #default="scope">
              <div>
                {{ scope.$index + 1 }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="所属区域"
            align="center"
            prop="region"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <div>
                {{ getDictLabel(DICT_TYPE.BELONG_REGION, scope.row.region) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="地址"
            align="center"
            prop="companyAddressDetail"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="国民经济行业类型"
            align="center"
            prop="industry"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="法人" align="center" prop="legalRepresentativeName" />
          <el-table-column label="电话" align="center" prop="legalRepresentativePhone" />
          <el-table-column label="账号" align="center" prop="account" />
          <el-table-column label="状态" align="center" prop="accountStatus">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.BUSINESS_STATE" :value="scope.row.accountStatus" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200">
            <template #default="scope">
              <div class="flex items-center justify-center">
                <el-button type="primary" link @click="handleDetail('detail', scope.row.id)">
                  详情
                </el-button>
                <el-button type="primary" link @click="handleDetail('edit', scope.row.id)">
                  修改
                </el-button>
                <el-button link type="primary" @click="handleResetPwd(scope.row)"
                  >重置密码
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
  </el-row>

  <!-- 添加或修改用户对话框 -->
  <UserForm ref="formRef" @success="getList" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions, getDictLabel } from '@/utils/dict'
import download from '@/utils/download'
import UserForm from './UserForm.vue'
import DeptTree from './DeptTree.vue'
import { CompanyRegisterApi } from '@/api/examineObj/business'
import * as UserApi from '@/api/system/user'

const { push } = useRouter()

defineOptions({ name: 'BasicOverview' })

const resultFlag = inject('resultFlag', false)
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  managerUser: undefined,
  region: undefined,
  industry: undefined,
  managerPhone: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CompanyRegisterApi.getCompanyRegisterPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

const handleDetail = (type, id) => {
  push({
    path: '/examineObj/businessDetail',
    query: {
      type,
      id
    }
  })
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

const handleResetPwd = async (row) => {
  try {
    // 重置的二次确认
    const result = await message.prompt(
      '请输入"' + row.account + '"的新密码',
      t('common.reminder'),
      {
        inputPattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/,
        inputErrorMessage: '密码为长度6-16位大小写字母、数字组合'
      }
    )
    const password = result.value
    // 发起重置
    await UserApi.resetUserPwd(row.userId, password)
    message.success('修改成功，新密码是：' + password)
  } catch {}
}

/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  queryParams.deptId = row.id
  await getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CompanyRegisterApi.exportCompanyRegister(queryParams)
    download.excel(data, '集团数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 */
onMounted(() => {
  getList()
})
</script>
