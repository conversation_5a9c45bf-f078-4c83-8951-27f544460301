/*
 * @Description:
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-28 15:32:15
 * @LastEditTime: 2024-10-28 15:42:39
 */
import request from '@/config/axios'

// 考核总体情况 VO
export interface OverviewVO {
  id: number // 主键
  summaryType: string // 类型
  citySummary: string // 全市总体情况的描述
  deptSummary: string // 各区各部门情况的描述
  quarter: string // 考核季度
  assessmentUnit: number // 考核单位
  examineUnitIds: string // 被考核单位
}

// 考核总体情况 API
export const OverviewApi = {
  // 查询考核总体情况分页
  getOverviewPage: async (params: any) => {
    return await request.get({ url: `/assessment/overview/page`, params })
  },

  // 查询考核总体情况详情
  getOverview: async (id: number) => {
    return await request.get({ url: `/assessment/overview/get?id=` + id })
  },

  // 新增考核总体情况
  createOverview: async (data: OverviewVO) => {
    return await request.post({ url: `/assessment/overview/create`, data })
  },

  // 修改考核总体情况
  updateOverview: async (data: OverviewVO) => {
    return await request.put({ url: `/assessment/overview/update`, data })
  },

  // 新增/修改考核总体情况
  saveOrUpdateOverview: async (data: any) => {
    return await request.post({ url: `/assessment/overview/saveOrUpdate`, data })
  },

  // 删除考核总体情况
  deleteOverview: async (id: number) => {
    return await request.delete({ url: `/assessment/overview/delete?id=` + id })
  },

  // 导出考核总体情况 Excel
  exportOverview: async (params) => {
    return await request.download({ url: `/assessment/overview/export-excel`, params })
  }
}
