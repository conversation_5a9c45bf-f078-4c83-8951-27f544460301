/*
 * @Description: 考核结果相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-06 13:11:24
 * @LastEditTime: 2024-08-07 10:14:54
 */
import request from '@/config/axios'

// 考核结果 VO
export interface ResultsVO {
  id: number // 主键(自增策略)
  unitId: number // 集团ID
  unitName: string // 集团名称
  examineYear: number // 考核年度
  basicScoreToplimit: number // 基础概况分值上限
  basicScore: number // 基础概况分值
  yearlyScoreToplimit: number // 年度工作分值上限
  yearlyScore: number // 年度工作分值
  specialScoreToplimit: number // 专项工作分值上限
  specialScore: number // 专项工作分值
  accidentScoreToplimit: number // 事故填报分值上限
  accidentScore: number // 事故填报分值
  status: number // 状态  0-未提交 1-已提交
}

// 考核结果 API
export const ResultsApi = {
  // 查询考核结果分页
  getResultsPage: async (params: any) => {
    return await request.get({ url: `/system/assessment/results/page`, params })
  },

  // 查询考核结果详情
  getResults: async (id: number) => {
    return await request.get({ url: `/system/assessment/results/get?id=` + id })
  },

  // 新增考核结果
  createResults: async (data: any) => {
    return await request.post({ url: `/system/assessment/results/create`, data })
  },

  // 修改考核结果
  updateResults: async (data: any) => {
    return await request.put({ url: `/system/assessment/results/update`, data })
  },

  // 删除考核结果
  deleteResults: async (id: number) => {
    return await request.delete({ url: `/system/assessment/results/delete?id=` + id })
  },

  // 导出考核结果 Excel
  exportResults: async (params) => {
    return await request.download({ url: `/system/assessment/results/export-excel`, params })
  },

  // 获取上限分值
  getTopLimitScore: async (params: any) => {
    return await request.get({ url: `/system/method/page`, params })
  }
}
