<template>
  <ContentWrap>
    <div class="flex items-center mb-15px">
      <el-button icon="Back" type="primary" @click="handleBack">返 回</el-button>
      <!-- <div class="ml-20px text-16px font-bold pb-5px text-#1e91fc bb-1-#1e91fc w-120px f-c">
        {{ previewPageData?.examineYear || route.query.examineYear }}年度
      </div> -->
    </div>
    <div class="f-s mb-30px">
      <div
        v-for="item in tabList"
        @click="handleTabChange(item)"
        class="w-180px h-35px bg-[#f4f4f4] f-c mr-15px text-14px cursor-pointer"
        :class="{ '!bg-[#c2e1fe]': item.id === currentTab.id }"
      >
        {{ item.label }}
      </div>
    </div>
    <!-- 动态使用模块页面组件 -->
    <component
      :is="currentTab.component"
      :currentTabId="currentTab.id"
      :key="currentTab.id"
      :flag="flag"
    ></component>
  </ContentWrap>
</template>

<script setup lang="ts">
import AccidentReport from './accidentReport/index.vue'
import ProminentProblems from './prominentProblems/index.vue'
import ElectricVehicle from './electricVehicle/index.vue'
import GasSafety from './gasSafety/index.vue'
import FireSafety from './fireSafety/index.vue'
import { cloneDeep } from 'lodash-es'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'RootCauseDetail' })

const previewPageData = ref<any>({})
const route = useRoute()
const { push, currentRoute } = useRouter()
const { delView } = useTagsViewStore() // 视图操作
const currentTab = shallowRef<any>({
  id: '1',
  label: '生产安全事故情况',
  component: AccidentReport
})
const tabList = [
  {
    id: '1',
    label: '生产安全事故情况',
    component: AccidentReport
  },
  {
    id: '2',
    label: '隐患区域场所综合治理',
    component: ProminentProblems
  },
  {
    id: '3',
    label: '电动车火灾',
    component: ElectricVehicle
  },
  {
    id: '4',
    label: '燃气安全专项治理',
    component: GasSafety
  },
  {
    id: '5',
    label: '消防安全专项治理',
    component: FireSafety
  }
]

const handleTabChange = (item) => {
  currentTab.value = item
}

const handleBack = () => {
  delView(unref(currentRoute))
  push('/rootCause')
}

const initPage = async () => {}

const flag: any = ref('edit')
const isMounted = ref(false)
onMounted(async () => {
  flag.value = route.query.flag
  if (!isMounted.value) {
    initPage().then(() => {
      isMounted.value = true
    })
  }
})

onActivated(async () => {
  if (isMounted.value) {
    initPage()
  }
})
</script>
