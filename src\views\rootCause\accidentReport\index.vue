<!--
 * @Description: 事故填报列表页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-23 10:54:18
 * @LastEditTime: 2024-10-31 14:50:00
-->
<template>
  <ContentWrap>
    <el-row class="mb4" v-if="roles.includes('assessment_unit')">
      <template v-if="flag !== 'detail'">
        <el-button type="warning" plain @click="handleImport">
          <Icon icon="ep:download" />
          导入
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:upload" />导出
        </el-button>
        <el-button type="primary" plain @click="handleSetKeyword"> 设置关键字 </el-button>
      </template>
      <el-button type="warning" plain @click="handleOverall"> 总体情况 </el-button>
    </el-row>
    <div class="f-s mb-20px">
      <div
        v-for="item in tabList"
        @click="handleChangeDate(item)"
        class="w-100px h-30px bg-[#d7d7d7] rounded f-c mr-15px text-14px cursor-pointer"
        :class="{ '!bg-[#1c5377]': item === activeTab, '!color-[#fff]': item === activeTab }"
      >
        {{ item }}
      </div>
    </div>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      show-summary
      :summary-method="getSummaries"
    >
      <el-table-column label="序号" align="center" type="index" width="60" />
      <el-table-column label="区域" align="center" prop="unitName" width="120" />
      <el-table-column label="城区类型" align="center" prop="cityAreaType" />
      <el-table-column label="累计">
        <el-table-column label="起数" align="center" prop="cumulativeIncidents" width="80" />
        <el-table-column label="人数" align="center" prop="totalOccurrences" width="80" />
      </el-table-column>
      <el-table-column label="工矿商贸事故">
        <el-table-column label="起数" align="center" prop="industrialAccidents" width="120">
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.industrialAccidents"
              :min="0"
            />
            <span v-else>{{ row.industrialAccidents }}</span>
          </template>
        </el-table-column>
        <el-table-column label="人数" align="center" prop="industrialVictims" width="120">
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.industrialVictims"
              :min="0"
            />
            <span v-else>{{ row.industrialVictims }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="道路交通事故">
        <el-table-column label="起数" align="center" prop="roadAccidents" width="120">
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.roadAccidents"
              :min="0"
            />
            <span v-else>{{ row.roadAccidents }}</span>
          </template>
        </el-table-column>
        <el-table-column label="人数" align="center" prop="roadVictims" width="120">
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.roadVictims"
              :min="0"
            />
            <span v-else>{{ row.roadVictims }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="水上交通事故">
        <el-table-column label="起数" align="center" prop="waterAccidents" width="120">
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.waterAccidents"
              :min="0"
            />
            <span v-else>{{ row.waterAccidents }}</span>
          </template>
        </el-table-column>
        <el-table-column label="人数" align="center" prop="waterVictims" width="120">
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.waterVictims"
              :min="0"
            />
            <span v-else>{{ row.waterVictims }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="铁路交通事故">
        <el-table-column label="起数" align="center" prop="railAccidents" width="120">
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.railAccidents"
              :min="0"
            />
            <span v-else>{{ row.railAccidents }}</span>
          </template>
        </el-table-column>
        <el-table-column label="人数" align="center" prop="railVictims" width="120">
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.railVictims"
              :min="0"
            />
            <span v-else>{{ row.railVictims }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column
        label="填写时间"
        align="center"
        prop="updateTime"
        :formatter="dateFormatter"
        width="190px"
      />
      <el-table-column
        v-if="flag !== 'detail'"
        label="操作"
        align="center"
        width="120"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button v-if="!row.isEditing" link type="primary" @click="handlEdit(row)">
            编辑
          </el-button>
          <template v-else>
            <el-button link type="warning" @click="handlCancel(row)"> 取消 </el-button>
            <el-button link type="success" @click="handlConfirm(row)"> 确定 </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- 导入弹窗 -->
  <ImportForm
    ref="importFormRef"
    @success="getList"
    @importTemplate="importTemplate"
    :upUrl="AccidentSummaryApi.upUrl"
  />
  <!-- 设置关键词弹窗 -->
  <KeyWordForm ref="keywordRef" dict-type="examine_accident_summary" />
  <!-- 总体情况弹窗 -->
  <OverallDialog ref="overallDialogRef" :quarter="convertToQuarter(activeTab)" summaryType="0" />
</template>

<script setup lang="ts">
import KeyWordForm from '../KeyWordForm.vue'
import dayjs from 'dayjs'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { AccidentSummaryApi } from '@/api/examineTask/accidentReport'
import { convertToQuarter } from '@/utils'
import { useUserStore } from '@/store/modules/user'

defineProps({
  flag: {
    type: String
  }
})
defineOptions({ name: 'AccidentReport' })
const roles = useUserStore().getRoles
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list = ref<any>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const tabList = ref<any>([])
const activeTab = ref<any>()
const queryParams: any = reactive({
  pageNo: 1,
  pageSize: -1,
  unitId: undefined,
  unitName: undefined,
  cityAreaType: undefined,
  industrialAccidents: undefined,
  industrialVictims: undefined,
  roadAccidents: undefined,
  roadVictims: undefined,
  waterAccidents: undefined,
  waterVictims: undefined,
  railAccidents: undefined,
  railVictims: undefined,
  completionDate: [],
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const overallDialogRef = ref()
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const queryData = {
      ...queryParams,
      yearMonthStr: activeTab.value
    }
    const data = await AccidentSummaryApi.getAccidentSummaryPage(queryData)
    list.value = data.list.map((item) => ({ ...item, isEditing: false }))
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 初始化月份标签
const initializeMonths = () => {
  const currentMonth = dayjs()
  tabList.value = Array.from({ length: 6 }, (_, i) =>
    currentMonth.subtract(i, 'month').format('YYYY-MM')
  ).reverse() // 保证数组中月份按时间顺序排列
  activeTab.value = tabList.value[tabList.value.length - 1] // 设置初始活动标签为当前月份
}

/** 搜索按钮操作 */
const handleQuery = async () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AccidentSummaryApi.exportAccidentSummary(queryParams)
    download.excel(data, '事故填报.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const handleChangeDate = (item) => {
  activeTab.value = item
  getList()
}
const importTemplate = async () => {
  const res = await AccidentSummaryApi.getImportTemplate({ quarter: route.query.quarter })
  download.excel(res, '事故填报导入模板.xls')
}
/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open({ quarter: route.query.quarter })
}

// 计算合计
const getSummaries: any = (param) => {
  const { columns, data } = param
  const sums: (string | VNode)[] = []
  columns.forEach((column, index) => {
    if (index === 13) return
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    const values = data.map((item) => Number(item[column.property]))
    if (!values.every((value) => Number.isNaN(value))) {
      sums[index] = `${values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)}`
    } else {
      sums[index] = ''
    }
  })

  return sums
}

const keywordRef = ref()
const handleSetKeyword = () => {
  keywordRef.value.open()
}
const handlEdit = (row) => {
  row.editData = {
    industrialAccidents: row.industrialAccidents,
    industrialVictims: row.industrialVictims,
    roadAccidents: row.roadAccidents,
    roadVictims: row.roadVictims,
    waterAccidents: row.waterAccidents,
    waterVictims: row.waterVictims,
    railAccidents: row.railAccidents,
    railVictims: row.railVictims
  }
  row.isEditing = true
}
const handlCancel = (row) => {
  row.editData = {}
  row.isEditing = false
}
const handlConfirm = async (row) => {
  // 提交请求
  try {
    const submitData = {
      ...row.editData,
      id: row.id,
      yearMonthStr: activeTab.value,
      unitId: row.unitId,
      unitName: row.unitName
    }
    await AccidentSummaryApi.saveOrUpdateAccidentSummary(submitData)
    message.success('操作成功')
    await getList()
  } finally {
    row.isEditing = false
  }
}

const handleOverall = () => {
  overallDialogRef.value.openDialog()
}

const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(() => {
  initializeMonths()
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-link) {
  width: 100%;
  text-align: center;
}
:deep(.el-input-number) {
  max-width: 100%;
}
</style>
