<!--
 * @Description: HomeIndex
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-04-22 13:53:50
 * @LastEditTime: 2024-06-04 16:33:27
-->
<template>
  <section class="index-container">
    <component :is="currentView" :row="row" @updateCurrentView="updateCurrentView"></component>
  </section>
</template>

<script lang="ts" setup>
import HomePage from './homePage.vue'

defineOptions({ name: 'Home' })
const row = ref({})
const currentView = shallowRef<any>(HomePage)

const updateCurrentView = (view, item) => {
  row.value = item
  if (view === 'HomePage') {
    currentView.value = HomePage
  }
}
</script>
