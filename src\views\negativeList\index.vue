<!--
 * @Description: 负面清单列表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-09-06 09:51:55
 * @LastEditTime: 2025-07-22 10:46:15
-->

<template>
  <ContentWrap>
    <template v-if="!resultFlag">
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="108px"
      >
        <el-form-item label="被考核单位" prop="evaluatedUnitName">
          <el-input
            v-model="queryParams.evaluatedUnitName"
            placeholder="请输入被考核单位"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery"
            ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
          >
          <el-button @click="resetQ<PERSON>y"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        </el-form-item>
        <el-row class="mb-25px">
          <el-button
            type="primary"
            plain
            @click="openForm('create')"
            v-hasPermi="['examine:negative:create']"
          >
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
          <el-button
            type="success"
            plain
            @click="handleExport"
            :loading="exportLoading"
            v-hasPermi="['examine:negative:export']"
          >
            <Icon icon="ep:download" class="mr-5px" /> 导出
          </el-button>
        </el-row>
      </el-form>
    </template>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="被考核单位" align="center" prop="evaluatedUnitName" />
      <el-table-column label="负面清单内容" align="center" prop="content" />
      <el-table-column label="扣分" align="center" prop="deductScore" width="100" />
      <el-table-column label="附件" align="center" prop="attachments" width="170">
        <template #default="scope">
          <FileListPreview :fileUrl="scope.row.attachments" />
        </template>
      </el-table-column>
      <el-table-column label="说明" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="185"
      />
      <el-table-column label="状态" align="center" prop="status" width="120px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.REPORT_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column width="200" label="操作" align="center" v-if="!resultFlag">
        <template #default="scope">
          <template v-if="scope.row.status === 0">
            <el-button
              link
              type="warning"
              @click="handleSubmit(scope.row.id)"
              v-hasPermi="['examine:negative:update']"
            >
              提交
            </el-button>
            <el-button
              link
              type="primary"
              @click="openForm('update', scope.row.id)"
              v-hasPermi="['examine:negative:update']"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row.id)"
              v-hasPermi="['examine:negative:delete']"
            >
              删除
            </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <NegativeForm ref="formRef" @success="getList" :assessmentYear="wsCache.get('year')" />
</template>

<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { NegativeApi, NegativeVO } from '@/api/negativeList'
import NegativeForm from './NegativeForm.vue'
import { useCache } from '@/hooks/web/useCache'

/** 负面清单 列表 */
defineOptions({ name: 'Negative' })

const { wsCache } = useCache()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const resultFlag = inject('resultFlag', false)
const loading = ref(true) // 列表的加载中
const list = ref<NegativeVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  evaluatedUnit: undefined,
  evaluatedUnitName: undefined,
  content: undefined,
  deductScore: undefined,
  attachments: undefined,
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const sessionObj: any = JSON.parse(sessionStorage.getItem('examineResult') as any)
    const data = await NegativeApi.getNegativePage({
      ...queryParams,
      assessmentYear: resultFlag ? sessionObj.examineYear : wsCache.get('year')
    })
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await NegativeApi.deleteNegative(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await NegativeApi.exportNegative(queryParams)
    download.excel(data, '负面清单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const handleSubmit = async (id) => {
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  await NegativeApi.updateNegative({ id, status: 1 })
  message.success('操作成功')
  // 刷新列表
  await getList()
}

/** 初始化 **/
onMounted(async () => {
  getList()
})
</script>
