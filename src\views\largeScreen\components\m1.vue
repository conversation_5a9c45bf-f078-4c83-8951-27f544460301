<!--
 * @Description: 城市概况
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-07-26 14:48:51
 * @LastEditTime: 2024-09-05 16:24:52
-->
<template>
  <div class="con">
    <div class="title">
      
      <span class="time">截止至{{ dateStr }}0时数据统计</span>
    </div>
    <ul class="card-con">
      <li>
        <p><span style="color: #468bee">980</span>起</p>
        <p>事故数量</p>
      </li>
      <li>
        <p><span style="color: #f782d9">840</span>人</p>
        <p>事故人数</p>
      </li>
      <li>
        <p> <span style="color: #eb9b32; cursor: pointer" @click="viewData">640</span>个 </p>
        <p>问题上报个数</p>
      </li>
      <li>
        <p><span style="color: #49a172">640</span>个</p>
        <p>已整改</p>
      </li>

      <li>
        <p><span style="color: #d5dd3e">65</span>%</p>
        <p>整改完成率</p>
      </li>
    </ul>
  </div>
</template>
<script setup lang="ts">
const router = useRouter()
const dateStr = ref('')
const viewData = () => {
  router.push({
    path: '/fullscreen/overviewAccident'
  })
}
onMounted(() => {
  const today = new Date()

  const year = today.getFullYear()

  const month = String(today.getMonth() + 1).padStart(2, '0')

  const day = String(today.getDate()).padStart(2, '0')

  dateStr.value = `${year}年${month}月${day}日`

  const viewData = () => {}
})
</script>

<style scoped lang="scss">
.con {
  height: 100%;
  color: #fff;
  /* background: #071b3d; */
  background: linear-gradient(#3264a4, #051430 30%, #051430 70%);
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #4189f0;
  .title {
    height: 40px;
    font-size: 15px;
    font-weight: bold;
    text-align: center;
    position: relative;
    color: #fafd86;
    .time {
      position: absolute;
      right: 10px;
      top: 4px;
      font-size: 12px;
      color: #fff;
    }
  }
  .card-con {
    width: 100%;
    height: calc(100% - 60px);
    display: flex;
    padding: 10px 20px;
    > li {
      flex: 1;
      text-align: center;
      > p:first-child {
        line-height: 24px;
        margin: 8px 0;
        > span {
          font-size: 30px;
          font-weight: bold;
          margin: 0 4px;
        }
      }
      > p:nth-child(2) {
        line-height: 30px;
        font-size: 14px;
      }
    }
    > li:not(:last-child) {
      > p:first-child {
        border-right: 1px solid #fff;
      }
    }
  }
}
</style>
