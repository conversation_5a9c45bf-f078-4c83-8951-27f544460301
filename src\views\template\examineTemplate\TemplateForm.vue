<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="110px"
      v-loading="formLoading"
    >
      <el-form-item label="指标名称" prop="templateName">
        <el-input v-model="formData.templateName" placeholder="请输入指标名称" />
      </el-form-item>
      <el-form-item label="评估标准" prop="classify">
        <el-select v-model="formData.classify" placeholder="请选择分类" style="width: 100%">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TEMPLATE_CLASS_CHECK)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="评估周期" prop="dateRange">
        <el-date-picker
          v-model="formData.dateRange"
          value-format="x"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="采集部门" prop="responsibleDept">
        <el-tree-select
          class="!w-100%"
          v-model="formData.responsibleDept"
          :data="deptList"
          default-expand-all
          :props="defaultProps"
          check-strictly
          node-key="id"
          placeholder="请选择部门"
          @current-change="handleDeptChange"
        />
      </el-form-item>
      <el-form-item label="被考核单位" prop="templateType">
        <el-select
          v-model="formData.templateType"
          placeholder="请选择被考核单位"
          style="width: 100%"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.TEMPLATE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="formData.description" placeholder="请输入描述" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { defaultProps } from '@/utils/tree'
import { getTemplate, createTemplate, updateTemplate } from '@/api/system/template'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import * as DeptApi from '@/api/system/dept'
import { cloneDeep } from 'lodash-es'
/** 指标管理 表单 */
defineOptions({ name: 'TemplateForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { deptList } = defineProps({
  deptList: {
    type: Array as PropType<DeptApi.DeptVO[]>,
    default: () => []
  }
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData: any = ref({
  pageNo: 1,
  pageSize: 10,
  templateName: undefined,
  classify: undefined,
  templateType: undefined,
  description: undefined,
  dateRange: [],
  responsibleDept: undefined,
  responsibleDeptName: undefined,
  collectionStart: undefined,
  collectionEnd: undefined
})
const formRules = reactive({
  templateName: [{ required: true, message: '指标名称不能为空', trigger: 'blur' }],
  classify: [{ required: true, message: '评估标准不能为空', trigger: 'change' }],
  templateType: [{ required: true, message: '被考核单位不能为空', trigger: 'change' }],
  level: [{ required: true, message: '层级不能为空', trigger: 'change' }],
  dateRange: [{ required: true, message: '评估周期不能为空', trigger: 'change' }],
  responsibleDept: [{ required: true, message: '采集部门不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await getTemplate(id)
      formData.value.responsibleDept = +formData.value.responsibleDept
      formData.value.dateRange = [formData.value.collectionStart, formData.value.collectionEnd]
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 部门树变化
const handleDeptChange = (data) => {
  formData.value.responsibleDeptName = data.name
}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = cloneDeep(formData.value)
    data.collectionStart = data.dateRange[0]
    data.collectionEnd = data.dateRange[1]
    delete data.dateRange
    if (formType.value === 'create') {
      await createTemplate(data)
      message.success(t('common.createSuccess'))
    } else {
      await updateTemplate(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    pageNo: 1,
    pageSize: 10,
    templateName: undefined,
    classify: undefined,
    templateType: undefined,
    description: undefined,
    dateRange: [],
    responsibleDept: undefined,
    responsibleDeptName: undefined,
    collectionStart: undefined,
    collectionEnd: undefined
  }
  formRef.value?.resetFields()
}
</script>
