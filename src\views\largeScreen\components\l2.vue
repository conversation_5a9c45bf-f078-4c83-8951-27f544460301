<!--
 * @Description: 考核排名-各特定功能区
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-09-05 10:03:54
 * @LastEditTime: 2024-09-05 16:22:27
-->

<template>
  <div class="con">
    <div class="title">考核排名-各特定功能区</div>
    <div class="content" ref="contentRef">
      <ul class="head">
        <li>排名</li>
        <li>单位</li>
        <li>总数</li>
        <li>重大隐患</li>
        <li>一般隐患</li>
      </ul>
      <ul class="body">
        <li>
          <span> 1 </span>
          <span>临港新片区管委会</span>
          <span>8</span>
          <span>3</span>
          <span>0</span>
        </li>
        <li>
          <span> 2 </span>
          <span>上海化工区管委会</span>
          <span>7</span>
          <span>5</span>
          <span>4</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script setup lang="ts">
const contentRef = ref<any>(null)
onMounted(() => {
  let h = contentRef.value.offsetHeight
  let domList = contentRef.value.getElementsByTagName('li')
  for (let item of domList) {
    item.style.height = (h - 30) / 10 + 'px'
    item.style.lineHeight = (h - 30) / 10 + 'px'
  }
})
</script>

<style scoped lang="scss">
.con {
  height: 100%;
  color: #fff;
  background: #071b3d;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #4189f0;
  .active {
    color: #fff !important;
  }
  .title {
    font-size: 15px;
    font-weight: bold;
    position: relative;
    margin: 4px 0 2px 0;
    .date {
      position: absolute;
      display: flex;
      right: 10px;
      top: 4px;
      font-size: 12px;
      color: #767e8e;

      > li {
        flex: 1;
        margin: 0 4px;
        font-weight: bold;
        cursor: pointer;
      }
    }
  }
  .content {
    width: 100%;
    height: calc(100% - 30px);
    padding-top: 4px;
    overflow: hidden;
    font-size: 12px;
    text-align: left;
    .head {
      display: flex;

      background: #153373;
      > li {
        overflow: hidden;
        line-height: 38px !important;
        height: 38px !important;
      }
    }
    .head {
      li {
        display: flex;
        justify-content: center;
        flex: 1;
        text-align: center;
      }
      > li:nth-child(2) {
        width: 36%;
        flex: unset;
        width: 36% !important;
      }
    }
    .body {
      overflow: hidden;
      margin-top: 2px;
      > li {
        display: flex;
        line-height: 32px !important;
        height: 32px !important;
        > span {
          display: inline-block;
          overflow: hidden;
          text-align: center;
          flex: 1;
        }
        > span:nth-child(2) {
          text-align: center;
          flex: unset;
          width: 36% !important;
        }
      }
      > li:nth-child(even) {
        background: #153373;
      }
    }
  }
}
</style>
