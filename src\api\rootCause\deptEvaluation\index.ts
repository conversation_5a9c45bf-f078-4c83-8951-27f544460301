/*
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-18 16:50:50
 * @LastEditTime: 2024-10-18 16:50:55
 */
import request from '@/config/axios'

// 各区各部门工作考评情况 VO
export interface WorkEvaluationVO {
  id: number // 唯一标识ID
  evaluationType: number // 状态   0-各区域  1-成员单位
  unitId: number // 考核单位
  unitName: string // 单位名称
  mainAccomplishments: string // 主要成效
  outstandingIssues: string // 突出问题
  quarter: string // 季度
  status: number // 状态   0-待提交  1-已提交
}

// 各区各部门工作考评情况 API
export const WorkEvaluationApi = {
  // 查询各区各部门工作考评情况分页
  getWorkEvaluationPage: async (params: any) => {
    return await request.get({ url: `/examine/work-evaluation/page`, params })
  },

  // 查询各区各部门工作考评情况详情
  getWorkEvaluation: async (id: number) => {
    return await request.get({ url: `/examine/work-evaluation/get?id=` + id })
  },

  // 新增各区各部门工作考评情况
  createWorkEvaluation: async (data: WorkEvaluationVO) => {
    return await request.post({ url: `/examine/work-evaluation/create`, data })
  },

  // 修改各区各部门工作考评情况
  updateWorkEvaluation: async (data: WorkEvaluationVO) => {
    return await request.put({ url: `/examine/work-evaluation/update`, data })
  },

  // 删除各区各部门工作考评情况
  deleteWorkEvaluation: async (id: number) => {
    return await request.delete({ url: `/examine/work-evaluation/delete?id=` + id })
  },

  // 导出各区各部门工作考评情况 Excel
  exportWorkEvaluation: async (params) => {
    return await request.download({ url: `/examine/work-evaluation/export-excel`, params })
  },
}
