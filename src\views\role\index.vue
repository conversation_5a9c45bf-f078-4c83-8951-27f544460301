<!--
 * @Description: 角色选择页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-08 17:19:43
 * @LastEditTime: 2024-09-11 11:07:46
-->

<template>
  <div class="bg">
    <p class="title"> {{ title }} </p>

    <ul class="loginType">
      <li v-for="item in pathList" @click="toPath(item)">
        <div class="imgcon">
          <img :src="getAssetUrl(item.url)" />
        </div>
        <p>
          <XButton
            :title="item.name"
            class="w-90% !h-50px !text-20px"
            style="background: var(--el-color-primary); color: #fff"
          />
        </p>
      </li>
    </ul>
  </div>
</template>
<script lang="ts" setup>
const { push } = useRouter()
const title = import.meta.env.VITE_APP_TITLE
const pathList = ref([
  { url: 'company', name: '被考核单位', type: '5' },
  { url: 'department', name: '考核单位', type: '3' }
])
const getAssetUrl = (name) => {
  return new URL(`../../assets/role/${name}.png`, import.meta.url).href
}
const toPath = ({ type, name }) => {
  push(`/login?type=${type}&name=${name}`)
}
</script>
<style lang="scss" scoped>
.loginType {
  display: flex;
  justify-content: space-between;
  width: 65%;
  left: 50%;
  transform: translateX(-50%);
  position: relative;
  top: 20%;
  font-size: 0;
  padding: 20px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  > li {
    background-color: rgba(255, 255, 255, 0.85);
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    width: 49%;
    box-shadow: 0 0 10px 0 rgba(0, 0, 100, 0.25);
    .imgcon {
      width: 100%;
      height: 55vh;

      position: relative;
      > img {
        width: 25%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -30%);
      }
    }
    > p {
      line-height: 50px;
      font-size: 52px;
      color: var(--el-color-primary);
      text-align: center;
      margin-bottom: 10%;
    }
  }
  > li:hover {
    /* font-weight: bold; */
    box-shadow: 0 0 30px 0 rgba(0, 0, 100, 0.25);
  }
}
.bg {
  width: 100vw;
  height: 100vh;
  background: url('@/assets/imgs/login-bg.jpg') no-repeat;
  background-size: cover; /* 水平垂直居中 */
  background-position: center center;
}
footer {
  background: rgba(70, 76, 91, 1);
  height: 120px;
  position: relative;
  top: 0px;
  left: 0;
  width: 100%;
  color: #fff;
  .footerCon {
    width: 1420px;
    margin: 0 auto;
    position: relative;
    .tip {
      font-weight: bold;
      position: absolute;
      right: 0;
      font-size: 16px;
      top: 40px;
    }
  }
}
.content {
  left: 50%;
  top: -140px;
  width: 1280px;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.6);
  border-radius: 10px;
  padding: 20px;
  position: relative;
  .descCon {
    position: relative;
    left: 50%;
    transform: translateX(-280px);
  }
  .desc {
    font-size: 24px;
    line-height: 36px;
  }
  .info {
    font-size: 18px;
    line-height: 32px;
  }
}
.title {
  width: 600px;
  font-size: 48px;
  text-align: center;
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  line-height: 56px;
  font-weight: bold;
}
.type {
  font-size: 26px;
}
</style>
