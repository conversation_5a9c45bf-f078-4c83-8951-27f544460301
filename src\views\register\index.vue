<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-04 17:07:27
 * @LastEditTime: 2024-08-28 14:41:15
-->
<template>
  <div class="bg">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      class="login-form"
      label-width="160px"
    >
      <div class="p-20px">
        <div class="flex items-center pl-100px">
          <!-- <img src="@/assets/imgs/u3.png" alt="" /> -->
          <div class="font-bold text-36px ml-10px">城市运行安全考核系统</div>
        </div>
        <div class="font-bold text-20px ml-750px mt--10px">----企业端</div>
        <div class="title">注册</div>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="单位名称" prop="companyName">
              <el-input v-model="formData.companyName" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人代表" prop="legalRepresentativeName">
              <el-input v-model="formData.legalRepresentativeName" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="统一社会信用代码" prop="creditCode">
              <el-input v-model="formData.creditCode" placeholder="必须为18位" maxlength="18" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="注册手机号" prop="contactPhone">
              <el-input v-model="formData.contactPhone" placeholder="请输入" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织机构代码" prop="organizationalCode">
              <el-input v-model="formData.organizationalCode" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="注册邮箱地址" prop="email">
              <el-input v-model="formData.email" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位区域" prop="areaId">
              <el-cascader
                v-model="formData.areaId"
                :props="defaultProps"
                :options="areaList"
                clearable
                style="width: 100%"
                placeholder="请选择地区"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="详细地址" prop="companyAddressDetail">
              <el-input v-model="formData.companyAddressDetail" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
          <el-form-item label="单位地址" prop="producingAddress">
            <el-row :gutter="0" style="width: 100%">
              <el-col :span="8">
                <el-form-item prop="producingAddress">
                  <el-cascader
                    v-model="formData.producingAddress"
                    :props="defaultProps"
                    :options="areaList"
                    style="width: 100%"
                    placeholder="请选择地区"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8" :offset="1">
                <el-form-item prop="producingAddressDetail">
                  <el-input
                    style="width: 100%"
                    v-model="formData.producingAddressDetail"
                    placeholder="详细地址"
                /></el-form-item>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col> -->
        </el-row>

        <div class="divider"></div>

        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="登录用户名" prop="username">
              <el-input
                v-model="formData.username"
                placeholder="请输入长度6-16位字符"
                :prefix-icon="iconAvatar"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="登录密码" prop="password">
              <el-input
                v-model="formData.password"
                placeholder="密码为长度6-16位大小写字母、数字组合"
                type="password"
                :prefix-icon="iconLock"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="确认密码" prop="repeatPassword">
              <el-input
                v-model="formData.repeatPassword"
                placeholder="请输入"
                type="password"
                :prefix-icon="iconLock"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row class="justify-center my-20px">
          <el-button @click="handleCancel"> 取 消 </el-button>
          <el-button class="!text-white !bg-[#1abc9c]" @click="getCode" :disabled="formLoading">
            注 册
          </el-button>
        </el-row>
      </div>
    </el-form>
    <Verify
      ref="verify"
      :captchaType="captchaType"
      :imgSize="{ width: '400px', height: '200px' }"
      mode="pop"
      @success="submitForm"
    />
  </div>

  <el-dialog v-model="resVisible" title="提示" width="560">
    <div style="margin-bottom: 30px; font-size: 18px; font-weight: bold; text-align: center"
      >该企业已注册</div
    >
    <el-form ref="resRef" label-width="240px">
      <el-form-item label="单位名称" prop="">
        <span>{{ resForm.companyName }}</span>
      </el-form-item>
      <el-form-item label="统一社会信用代码" prop="">
        <span>{{ resForm.creditCode }}</span>
      </el-form-item>
      <el-form-item label="法人代表" prop="">
        <span>{{ resForm.legalRepresentativeName }}</span>
      </el-form-item>
    </el-form>
    <div
      style="
        margin-bottom: 20px;
        color: #d9001b;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
      "
    >
      如有疑问请联系标准化办公室:33390486
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { useIcon } from '@/hooks/web/useIcon'
import { reactive } from 'vue'
import * as AreaApi from '@/api/system/area'
import * as UserApi from '@/api/system/user'
import { cloneDeep } from 'lodash-es'

const iconLock = useIcon({ icon: 'ep:lock' })
const iconAvatar = useIcon({ icon: 'ep:avatar' })
const formData: any = ref({})
import { isPhone, isEmail, isCreditCode, isUsername, isPwd } from '@/utils/validate'
import { registerInfo } from '@/api/system/user'
const defaultProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}
const formLoading = ref(false)
const areaList = ref([])

const { push } = useRouter()
const resVisible = ref(false)
const resForm = ref()
const checkRes = async (data) => {
  resForm.value = data
  resVisible.value = true
}

const validatePhone = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入手机号'))
  }
  if (!isPhone(value)) {
    callback(new Error('手机号格式不正确'))
  } else {
    callback()
  }
}
const validateEmail = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入邮箱'))
  }
  if (value && !isEmail(value)) {
    callback(new Error('邮箱格式不正确'))
  } else {
    callback()
  }
}

const validatePwd = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入确认密码'))
  }
  if (value && value != formData.value.password) {
    callback(new Error('密码不一致'))
  } else {
    callback()
  }
}
const validateCreditCode = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入社会信用代码'))
  }
  if (value && !isCreditCode(value)) {
    callback(new Error('必须为18位字母、数字组合'))
  } else {
    callback()
  }
}
const validateUsername = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入用户名'))
  }
  if (value && !isUsername(value)) {
    callback(new Error('必须为长度6-16位字母、数字组合'))
  } else {
    callback()
  }
}
const validatePassword = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入密码'))
  }
  if (value && !isPwd(value)) {
    callback(new Error('必须为长度6-16位字母、数字组合'))
  } else {
    callback()
  }
}
const formRules = reactive({
  companyName: [{ required: true, message: '企业名称不能为空', trigger: 'blur' }],
  creditCode: [
    {
      required: true,
      validator: validateCreditCode,
      trigger: 'blur'
    }
  ],
  legalRepresentativeName: [{ required: true, message: '法定代表人不能为空', trigger: 'blur' }],
  contactPhone: [{ required: true, validator: validatePhone, trigger: 'blur' }],
  email: [{ required: true, validator: validateEmail, trigger: 'blur' }],
  username: [{ required: true, validator: validateUsername, trigger: 'blur' }],
  password: [{ required: true, validator: validatePassword, trigger: 'blur' }],
  repeatPassword: [{ required: true, validator: validatePwd, trigger: 'blur' }],
  organizationalCode: [{ required: true, message: '组织机构代码不能为空', trigger: 'blur' }],
  areaId: [{ required: true, message: '单位区域不能为空', trigger: 'change' }],
  companyAddressDetail: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }]
})
const handleCancel = () => {
  window.history.back()
}

const captchaType = ref('blockPuzzle')
const formRef = ref()
const message = useMessage()
const verify = ref()
const submitForm = async (checkData) => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = cloneDeep(formData.value)

    data.areaId = data.areaId.join('/')
    data.registerType = '1'
    data.captchaVerification = checkData.captchaVerification
    /* const data = {
      companyName: '测试注册',
      legalRepresentativeName: '张三丰',
      contactPhone: '17301810626',
      creditCode: '111111111111111111',
      organizationalCode: '111',
      email: '<EMAIL>',
      companyAddressDetail: '111',
      areaId: '110000/110100/110101',
      username: 'zhangsanfeng',
      password: 'zhangsanfeng123',
      repeatPassword: 'zhangsanfeng123',
      registerType: '1',
      captchaVerification: checkData.captchaVerification
    } */
    let resData = await UserApi.getByCreditCode({ creditCode: formData.value.creditCode })
    if (resData.length > 0) {
      checkRes(resData[0])
      return
    }
    await registerInfo(data)
    message.success('注册成功')
    push('/role')
  } finally {
    formLoading.value = false
  }
}
// 获取验证码
const getCode = async () => {
  verify.value.show()
}
onMounted(async () => {
  let data = await AreaApi.getAreaTree()
  areaList.value = data[8].children
  //verify.value.show()
})
</script>

<style lang="scss" scoped>
.bg {
  width: 100vw;
  height: 100vh;
  background: rgb(26, 188, 156);
}
.divider {
  border-bottom: 2px dashed rgb(26, 188, 156);
  display: block;
  width: 100%;
  margin: 20px 0 30px 0;
}
.login-form {
  background: #fff;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 10px 20px 10px 20px;
  width: 1100px;
  border-radius: 10px;
  overflow: hidden;
}

.title {
  position: relative;
  width: 130px;
  line-height: 46px;
  font-weight: bold;
  font-size: 20px;
  margin: 0px auto 20px;
  text-align: center;
  color: rgb(26, 188, 156);
  border-bottom: 2px solid #00bfbf;
}
.title::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 0;
  width: 100%;
  height: 1px;
  border-bottom: 1px solid #00bfbf;
}
</style>
