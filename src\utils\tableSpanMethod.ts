/*
 * @Description:
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-07-24 14:02:04
 * @LastEditTime: 2025-07-25 16:11:09
 */
// 通用的表格合并逻辑函数
// 通用的合并逻辑函数
export const calculateSpanArray = (
  data: any[],
  compareFn: (current: any, previous: any) => boolean
) => {
  const spanArr: any = []
  let position = 0

  data.forEach((item, index) => {
    if (index === 0) {
      spanArr.push(1)
      position = 0
    } else {
      if (compareFn(data[index], data[index - 1])) {
        spanArr[position] += 1
        spanArr.push(0)
      } else {
        spanArr.push(1)
        position = index
      }
    }
  })

  return spanArr
}

// 根据属性数组生成比较函数
export const generateCompareFn = (props: string[]) => {
  return (current: any, previous: any) => {
    return props.every((prop) => current[prop] === previous[prop])
  }
}

// 获取表格合并数组
export const getSpanArrays = (data: any[], columnMergeRules: string[][]) => {
  const spanArrays: any[] = []

  // 根据配置动态生成各列的合并信息
  columnMergeRules.forEach((props, index) => {
    const spanArr = calculateSpanArray(data, generateCompareFn(props))
    spanArrays.push(spanArr)
  })

  return spanArrays
}

// 表格合并方法
export const objectSpanMethod = (spanArrays: any[]) => {
  return ({ row, column, rowIndex, columnIndex }) => {
    // 只处理有合并规则的列
    if (columnIndex >= 1 && columnIndex <= 6) {
      // 由于序号列不参与合并，需要调整索引
      const adjustedIndex = columnIndex - 1
      const spanArr = spanArrays[adjustedIndex]
      if (spanArr) {
        const _row = spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    }
    // 其他列不合并
    return {
      rowspan: 1,
      colspan: 1
    }
  }
}
