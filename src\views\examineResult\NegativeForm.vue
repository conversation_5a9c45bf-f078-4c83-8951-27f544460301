<template>
  <Dialog title="负面清单" v-model="dialogVisible" center top="5vh" width="800">
    <div class="px-30px pb-10px" v-if="tableData?.length">
      <div class="text-16px flex flex-col gap3" v-for="(item, index) in tableData">
        <div>负面清单内容：{{ item.content }}</div>
        <div class="flex">附件内容： <FileListPreviewDownload :fileUrl="item.attachments" /></div>
        <div>提出时间：{{ formatDate(item?.createTime, 'YYYY-MM-DD') }}</div>
        <div>扣分： {{ item.deductScore }}分</div>
        <el-divider v-if="index !== tableData.length - 1"></el-divider>
      </div>
    </div>
    <el-empty v-else></el-empty>
  </Dialog>
</template>
<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import { NegativeApi } from '@/api/negativeList'

/** 考核模板 表单 */
defineOptions({ name: 'NegativeForm' })

const props = defineProps({
  year: {
    type: Number
  }
})

const tableData: any = ref([])
const dialogVisible = ref(false) // 弹窗的是否展示

/** 打开弹窗 */
const open = async (row) => {
  try {
    let queryData = {
      pageSize: -1,
      pageNo: 1,
      assessmentYear: props.year,
      evaluatedUnit: row.unitId,
      status: 1
    }
    const data = await NegativeApi.getNegativePage(queryData)
    tableData.value = data.list
  } finally {
    dialogVisible.value = true
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
