<!--
 * @Description: 考核方式新增
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-29 16:43:12
 * @LastEditTime: 2024-10-30 17:11:43
-->

<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="160px"
    v-loading="formLoading"
  >
    <div class="text-22px pl-35px pb-25px font-bold"
      >{{ flag === 'add' ? '新增' : '修改' }}考核方式</div
    >
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="考核方式名称" prop="examineMethod">
          <el-input v-model="formData.examineMethod" placeholder="请输入考核方式名称" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="考核频率" prop="examineFrequency">
          <el-select
            v-model="formData.examineFrequency"
            placeholder="请选择考核频率"
            class="w-100%"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.ASSESSMENT_FREQUENCY)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <!-- <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="考核单位" prop="examineUnitId">
          <el-tree-select
            class="w-100%"
            v-model="formData.examineUnitId"
            :data="deptList"
            default-expand-all
            :props="defaultProps"
            check-strictly
            node-key="id"
            placeholder="请选择考核单位"
            @current-change="handleDeptChange"
          />
        </el-form-item>
      </el-col>
    </el-row> -->
    <!-- <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="考核人员" prop="examiners">
          <el-select
            class="w-100%"
            v-model="formData.examiners"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="6"
          >
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row> -->
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="上限分" prop="upperLimitScore">
          <el-input
            v-model="formData.upperLimitScore"
            placeholder="请输入上限分"
            @input="(v) => (formData.upperLimitScore = v.replace(/[^\d.]/g, ''))"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="基础分" prop="baseScore">
          <el-input
            v-model="formData.baseScore"
            placeholder="请输入基础分"
            @input="(v) => (formData.baseScore = v.replace(/[^\d.]/g, ''))"
            @blur="baseScoreBlur"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="事故分值占比" prop="accidentPercentage">
          <el-input
            v-model="formData.accidentPercentage"
            placeholder="请输入事故分值占比"
            @input="(v) => (formData.accidentPercentage = v.replace(/[^\d.]/g, ''))"
            @blur="handleWeightBlur('accidentPercentage')"
          >
            <template #suffix>%</template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="4" v-if="formData.baseScore && formData.accidentPercentage">
        <div class="ml-30px f-s items-center h-34px">
          {{ (+formData.baseScore * (+formData.accidentPercentage / 100)).toFixed(2) }}分
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="考核过程分值占比" prop="yearlyPercentage">
          <el-input
            v-model="formData.yearlyPercentage"
            placeholder="请输入考核过程分值占比"
            @input="(v) => (formData.yearlyPercentage = v.replace(/[^\d.]/g, ''))"
            @blur="handleWeightBlur('yearlyPercentage')"
          >
            <template #suffix>%</template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="4" v-if="formData.baseScore && formData.yearlyPercentage">
        <div class="ml-30px f-s items-center h-34px">
          {{ (+formData.baseScore * (+formData.yearlyPercentage / 100)).toFixed(2) }}分
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="治本攻坚分值占比" prop="specialPercentage">
          <el-input
            v-model="formData.specialPercentage"
            placeholder="请输入治本攻坚分值占比"
            @input="(v) => (formData.specialPercentage = v.replace(/[^\d.]/g, ''))"
            @blur="handleWeightBlur('specialPercentage')"
          >
            <template #suffix>%</template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="4" v-if="formData.baseScore && formData.specialPercentage">
        <div class="ml-30px f-s items-center h-34px">
          {{ (+formData.baseScore * (+formData.specialPercentage / 100)).toFixed(2) }}分
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="督查督办分值占比" prop="basicPercentage">
          <el-input
            v-model="formData.basicPercentage"
            placeholder="请输入督查督办分值占比"
            @input="(v) => (formData.basicPercentage = v.replace(/[^\d.]/g, ''))"
            @blur="handleWeightBlur('basicPercentage')"
          >
            <template #suffix>%</template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="4" v-if="formData.baseScore && formData.basicPercentage">
        <div class="ml-30px f-s items-center h-34px">
          {{ (+formData.baseScore * (+formData.basicPercentage / 100)).toFixed(2) }}分
        </div>
      </el-col>
    </el-row> -->
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="汇总方式" prop="summaryMethod1">
          <el-select v-model="formData.summaryMethod1" placeholder="请选择汇总方式" class="w-100%">
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.SUMMARY_METHOD)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item label="考核形式" prop="assessmentFormat">
          <el-select
            v-model="formData.assessmentFormat"
            placeholder="请选择考核形式"
            class="w-100%"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.ASSESSMENT_FORM)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-form-item>
        <el-button type="info" @click="handleClose" :disabled="formLoading">取 消</el-button>
        <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      </el-form-item>
    </el-row>
  </el-form>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user'
import { defaultProps, handleTree } from '@/utils/tree'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { MethodApi } from '@/api/template/examineMethod'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { cloneDeep } from 'lodash-es'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'

defineOptions({ name: 'ExamineMethodCreate' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const route = useRoute()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  examineMethod: undefined,
  examineFrequency: undefined,
  examineUnitId: undefined,
  examiners: undefined,
  basicPercentage: undefined,
  yearlyPercentage: undefined,
  specialPercentage: undefined,
  accidentPercentage: undefined,
  baseScore: undefined,
  upperLimitScore: undefined,
  summaryMethod1: undefined,
  summaryMethod2: undefined,
  assessmentFormat: undefined,
  examineQuotaId: undefined
})

const deptId = useUserStore().getUser.deptId
const userList = ref<any>([])
const deptList = ref<any>([])
const flag = ref('add')
const formRules = reactive({
  examineMethod: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  examineFrequency: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  examineUnitId: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  examiners: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  basicPercentage: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  yearlyPercentage: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  specialPercentage: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  accidentPercentage: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  baseScore: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  upperLimitScore: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  summaryMethod1: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  assessmentFormat: [{ required: true, message: '必填项不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // if (
  //   +formData.value.basicPercentage +
  //     +formData.value.yearlyPercentage +
  //     +formData.value.specialPercentage +
  //     +formData.value.accidentPercentage !==
  //   100
  // ) {
  //   return message.warning('四个分值占比总和必须等于100%')
  // }
  // 提交请求
  formLoading.value = true
  try {
    const submitData = cloneDeep(formData.value)
    // 处理数据
    // submitData.examiners = submitData.examiners.join(',')
    submitData.examineUnitId = deptId
    if (flag.value === 'add') {
      delete submitData.id
      await MethodApi.createMethod(submitData)
      message.success(t('common.createSuccess'))
    } else {
      await MethodApi.updateMethod(submitData)
      message.success(t('common.updateSuccess'))
    }
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  push('/template/examineMethod')
}

// 部门树变化
const handleDeptChange = async (data, node) => {
  formData.value.examiners = ''
  fetchUserList(data.id)
}

const fetchUserList = async (deptId) => {
  const res = await UserApi.getUserPage({ pageNo: 1, pageSize: 100, deptId })
  userList.value = res.list
}

// 基础分限制低于上限分
const baseScoreBlur = () => {
  if (!formData.value.upperLimitScore) {
    message.warning('请先填写上限分！')
    formData.value.baseScore = undefined
    return
  }
  if (formData.value.baseScore > formData.value.upperLimitScore) {
    message.warning('基础分必须低于上限分！')
    formData.value.baseScore = undefined
    return
  }
}

// 限制四个分值占比总和必须等于100%
const handleWeightBlur = (flag) => {
  if (
    !formData.value.baseScore ||
    !formData.value.basicPercentage ||
    !formData.value.yearlyPercentage ||
    !formData.value.specialPercentage ||
    !formData.value.accidentPercentage
  )
    return

  if (
    +formData.value.basicPercentage +
      +formData.value.yearlyPercentage +
      +formData.value.specialPercentage +
      +formData.value.accidentPercentage !==
    100
  ) {
    message.warning('四个分值占比总和必须等于100%')
    formData.value[flag] = undefined
    return
  }
}

onMounted(async () => {
  deptList.value = handleTree(await DeptApi.getSimpleDeptList({ parentId: 102 }))
  if (route.query.id) {
    flag.value = 'edit'
    formData.value = await MethodApi.getMethod(route.query.id as any)
    formData.value.examiners = formData.value.examiners.split(',').map(Number)
    formData.value.examineFrequency = formData.value.examineFrequency + ''
    formData.value.summaryMethod1 = formData.value.summaryMethod1 + ''
    formData.value.assessmentFormat = formData.value.assessmentFormat + ''
    fetchUserList(formData.value.examineUnitId)
  }
})
</script>
