<!--
 * @Description: DocumentForm
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-30 11:23:19
 * @LastEditTime: 2024-07-30 11:29:22
-->
<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="1000">
    <div class="p-20px">
      <div v-html="content"></div>
    </div>
  </Dialog>
</template>

<script lang="ts" setup>
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const content = ref()
/** 打开弹窗 */
const fileLoad = async (data) => {
  dialogVisible.value = true
  dialogTitle.value = '预览'
  content.value = data
}
defineExpose({ fileLoad }) // 提供 open 方法，用于打开弹窗
</script>
