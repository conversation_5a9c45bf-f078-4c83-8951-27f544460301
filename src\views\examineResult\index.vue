<!--
 * @Description: 考核结果
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-05 14:44:01
 * @LastEditTime: 2025-07-22 09:06:44
-->
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="mb-5px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="98px"
    >
      <el-form-item label="被考核单位" prop="unitName">
        <el-input
          v-model="queryParams.unitName"
          placeholder="请输入被考核单位"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s mb-20px position-relative flex-wrap pr-70px">
      <div class="position-absolute right-0 bottom-0">
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:upload" />导出
        </el-button>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column type="index" label="排名" width="60"></el-table-column>
      <el-table-column label="被考核单位" align="center" prop="unitName" min-width="200" />
      <el-table-column label="综合分值" align="center" prop="totalScore">
        <template #default="scope">
          {{ handleText('totalScore', scope.row) }}
          <!-- <el-link type="primary" :underline="false" @click="handleDetail(scope.row, '0')">
            {{ handleText('totalScore', scope.row) }}
          </el-link> -->
        </template>
      </el-table-column>
      <el-table-column label="年度考核" align="center" prop="yearlyScore">
        <template #default="scope">
          {{ handleText('yearlyScore', scope.row) }}
          <!-- <el-link type="primary" :underline="false" @click="handleDetail(scope.row, '1')">
            {{ handleText('yearlyScore', scope.row) }}
          </el-link> -->
        </template>
      </el-table-column>
      <!-- <el-table-column label="事故填报" align="center" prop="accidentScore" width="160">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="handleDetail(scope.row, '0')">
            {{ handleText('accidentScore', scope.row) }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="治本攻坚" align="center" prop="specialScore" width="160">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="handleDetail(scope.row, '2')">
            {{ handleText('specialScore', scope.row) }}
          </el-link>
        </template>
      </el-table-column> -->
      <el-table-column label="督查督办" align="center" prop="supervisionScore">
        <template #default="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="handleOpenForm(scope.row, 'supervisionScore')"
          >
            {{ handleText('supervisionScore', scope.row) }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="负面清单" align="center" prop="negativeScore">
        <template #default="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="handleOpenForm(scope.row, 'negativeScore')"
          >
            {{ handleText('negativeScore', scope.row) }}
          </el-link>
        </template>
      </el-table-column>
      <!-- <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.REPORT_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="考核时间"
        align="center"
        prop="assessmentTime"
        width="170"
        :formatter="dateFormatter"
      />
      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template #default="scope">
          <el-button
            v-if="scope.row.status === 0"
            link
            type="warning"
            @click="handleSubmit(scope.row.id)"
          >
            提交
          </el-button>
          <el-button link type="primary" @click="handleDetail(scope.row, '0')"> 查看 </el-button>
        </template>
      </el-table-column> -->
    </el-table>
  </ContentWrap>
  <!-- 督查督办弹窗 -->
  <SupervisionForm ref="supervisionFormRef" :year="wsCache.get('year')" />
  <!-- 负面清单弹窗 -->
  <NegativeForm ref="negativeFormRef" :year="wsCache.get('year')" />
</template>

<script setup lang="ts">
import SupervisionForm from './SupervisionForm.vue'
import NegativeForm from './NegativeForm.vue'
import { useUserStore } from '@/store/modules/user'
import download from '@/utils/download'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { SchemeApi } from '@/api/template/examinePlan'
import { ResultsApi } from '@/api/examineResult'
import { useCache } from '@/hooks/web/useCache'

defineOptions({ name: 'ExamineResult' })

const supervisionFormRef = ref()
const negativeFormRef = ref()
const userStore = useUserStore()
const { wsCache } = useCache()
const deptId = userStore.getUser.deptId
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter()
const route = useRoute()
const loading = ref(true) // 列表的加载中
const tableData = ref<any>([]) // 列表的数据
const queryParams = ref({
  pageNo: 1,
  pageSize: -1,
  unitId: undefined,
  unitName: undefined,
  examineYear: undefined,
  basicScoreToplimit: undefined,
  basicScore: undefined,
  yearlyScoreToplimit: undefined,
  yearlyScore: undefined,
  specialScoreToplimit: undefined,
  specialScore: undefined,
  accidentScoreToplimit: undefined,
  accidentScore: undefined,
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

/** 搜索按钮操作 */
const handleQuery = async () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ResultsApi.getResultsPage({
      ...queryParams.value,
      examineYear: wsCache.get('year')
    })
    tableData.value = data.list
  } finally {
    loading.value = false
  }
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ResultsApi.exportResults({
      ...queryParams.value,
      examineYear: wsCache.get('year')
    })
    download.excel(data, '考核结果.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const handleDetail = (row, compIndex) => {
  const sessionObj = {
    examineYear: wsCache.get('year'),
    compIndex,
    status: row.status,
    unitId: row.unitId,
    unitName: row.unitName,
    totalScoreToplimit: row.totalScoreToplimit,
    basicScoreToplimit: row.basicScoreToplimit,
    accidentScoreToplimit: row.accidentScoreToplimit,
    yearlyScoreToplimit: row.yearlyScoreToplimit,
    specialScoreToplimit: row.specialScoreToplimit
  }
  sessionStorage.setItem('examineResult', JSON.stringify(sessionObj))
  push({
    path: '/examineResult/resultTab'
  })
}

const handleGroupDetail = (row) => {
  push({
    path: '/examineObj/businessDetail',
    query: {
      type: 'detail',
      deptId: row.unitId,
      backPath: route.path
    }
  })
}

const handleText = (field, row) => {
  // if (row[field] === null || row[field] === undefined) {
  //   return '考核'
  // } else {
  //   return `${row[field]}分 (上限：${row[field + 'Toplimit']}分)`
  // }
  if (!row[field]) {
    return '-'
  } else {
    return row[field]
  }
}

const handleOpenForm = (row, field) => {
  if (!row[field]) {
    return
  }
  switch (field) {
    case 'supervisionScore':
      supervisionFormRef.value.open(row)
      break
    case 'negativeScore':
      negativeFormRef.value.open(row)
      break
    default:
      break
  }
}

const handleSubmit = async (id) => {
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  await ResultsApi.updateResults({ id, status: 1 })
  message.success('操作成功')
  // 刷新列表
  await getList()
}

const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(async () => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-link) {
  width: 100%;
  text-align: center;
}
</style>
