<!--
 * @Description: 考核完成率统计
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-22 17:05:30
 * @LastEditTime: 2024-08-16 14:17:33
-->
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="88px">
      <el-form-item label="年度" prop="year">
        <el-date-picker
          class="!w-100%"
          v-model="queryParams.year"
          type="year"
          placeholder="请选择年度"
          value-format="YYYY"
          @change="yearChange"
          :clearable="false"
        />
      </el-form-item>
    </el-form>

    <el-row :gutter="20" class="my-10px">
      <el-col :span="6">
        <el-card shadow="never">
          <template #header>
            <div class="h-3 flex font-bold justify-between"> 考核完成率 </div>
          </template>
          <Echart class="mx-auto" :height="295" :options="pieData" />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="never">
          <template #header>
            <div class="h-3 flex font-bold justify-between"> 过程完成指标 </div>
          </template>
          <Echart class="mx-auto" :height="295" :options="radarData" />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="never">
          <template #header>
            <div class="h-3 flex font-bold justify-between"> 考核数据 </div>
          </template>
          <TargetCompletion ref="targetCompletionRef" />
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card shadow="never">
          <template #header>
            <div class="h-3 flex font-bold justify-between"> 各区域集团分布数量 </div>
          </template>
          <Echart :options="barData" />
        </el-card>
      </el-col>
    </el-row>
  </ContentWrap>
</template>

<script setup lang="ts">
import TargetCompletion from '@/views/Home/components/targetCompletion.vue'
import {
  getExamineResultData,
  getProcessMetricsData,
  getRegionDistribution
} from '@/api/statistics/examineCompletion'
import { pieOptions, barOptions, radarOption } from './echarts-data'
import { set } from 'lodash-es'

defineOptions({ name: 'ExamineCompletion' })

const targetCompletionRef = ref()
const loading = ref(true) // 列表的加载中
const queryParams = ref<any>({
  year: new Date().getFullYear() + ''
})
const queryFormRef = ref() // 搜索的表单
const pieData = ref<any>(pieOptions)
const radarData = ref<any>(radarOption)
const barData = ref<any>(barOptions)
const yearChange = () => {
  getPieData()
  getRadarData()
  targetCompletionRef.value?.getEchartsData(queryParams.value.year)
}
const getAllData = () => {
  yearChange()
  getBarData()
}
const getPieData = async () => {
  loading.value = true
  try {
    const data = await getExamineResultData(queryParams.value)
    set(pieData.value, 'series[0].data', [
      { value: data.t1, name: '完成率' },
      { value: data.t3, name: '未完成率' }
    ])
  } finally {
    loading.value = false
  }
}
const getRadarData = async () => {
  loading.value = true
  try {
    const data = await getProcessMetricsData(queryParams.value)
    set(radarData.value, 'series[0].data[0].value', [
      data.quarterlyReport,
      data.semiAnnualReport,
      data.basicOverview,
      data.specialWork,
      data.annualReport
    ])
  } finally {
    loading.value = false
  }
}
const getBarData = async () => {
  loading.value = true
  try {
    const data = await getRegionDistribution()
    set(barData.value, 'dataset.source', data)
  } finally {
    loading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  getAllData()
})
</script>
