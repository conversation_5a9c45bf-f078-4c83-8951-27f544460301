<!--
 * @Description: 专项工作
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-31 13:55:52
 * @LastEditTime: 2025-07-25 16:16:21
-->
<template>
  <ContentWrap>
    <div class="flex items-center mb-15px">
      <el-button icon="Back" type="primary" @click="handleBack">返 回</el-button>
      <div class="text-20px font-bold ml-15px">{{ row.workName }}</div>
      <el-button
        v-if="queryParams.unitId"
        class="ml-auto"
        type="success"
        plain
        @click="handleExport"
        :loading="exportLoading"
      >
        <Icon class="mr-5px" icon="ep:upload" /> 导 出
      </el-button>
    </div>
    <el-row :gutter="20">
      <!-- 左侧部门树 -->
      <el-col
        class="pl-10px"
        :span="4"
        :xs="24"
        v-if="route.path === '/examineProcess/yearExamine'"
      >
        <div
          class="py-5px cursor-pointer pl-10px rounded"
          :class="activeDeptId === item.id ? 'bg-blue-100' : ''"
          @click="handleDeptNodeClick(item)"
          v-for="item in row.deptList"
          :key="item.id"
        >
          {{ item.name }}
        </div>
        <!-- <DeptTree @node-click="handleDeptNodeClick" /> -->
      </el-col>
      <el-col :span="route.path === '/examineProcess/yearExamine' ? 20 : 24" :xs="24">
        <el-table
          v-loading="loading"
          :data="tableData"
          :stripe="true"
          :show-overflow-tooltip="true"
          :span-method="objectSpanMethod"
          height="700"
        >
          <el-table-column type="index" label="序号" width="50"></el-table-column>
          <el-table-column label="工作目标任务" align="center" prop="primaryElement">
            <el-table-column label="类型" align="center" prop="primaryElement" width="130" />
            <el-table-column label="指标名称" align="center" prop="secondaryElement" width="130" />
            <el-table-column label="评分标准" align="center" prop="thirdElement" />
            <el-table-column label="分值" align="center" prop="standardScore" width="80" />
            <el-table-column label="评分规则" align="center" prop="examineContent" width="850" />
          </el-table-column>
          <el-table-column label="附件" align="center" prop="annex" width="100">
            <template #default="scope">
              <FileListPreview :fileUrl="scope.row.annex" />
            </template>
          </el-table-column>
          <el-table-column label="自评得分" align="center" prop="score" width="90" />
          <el-table-column label="自评描述" align="center" prop="remark" width="500" />
          <template v-if="route.path === '/examineProcess/yearExamine'">
            <el-table-column label="考核分值" align="center" prop="assessmentScore" width="150" />
            <el-table-column label="考核点评" align="center" prop="assessmentRemark" width="380" />
          </template>
          <!-- <el-table-column
        label="填写时间"
        align="center"
        prop="createTime"
        width="160"
        :formatter="dateFormatter"
      />
      <el-table-column label="填报账号" align="center" prop="creator" width="120" /> -->
          <el-table-column
            fixed="right"
            label="操作"
            align="center"
            width="120"
            v-if="flag === 'edit' && !resultFlag && route.path === '/examineTask/yearExamine'"
          >
            <template #default="scope">
              <el-button link type="primary" @click="handleUpdate(scope.row)"> 编辑 </el-button>
              <el-button v-if="scope.row.id" link type="danger" @click="handleDelete(scope.row.id)">
                删除
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="center"
            width="120"
            v-if="flag === 'edit' && !resultFlag && route.path === '/examineProcess/yearExamine'"
          >
            <template #default="scope">
              <el-button link type="primary" @click="handleExamine(scope.row)"> 考核 </el-button>
            </template>
          </el-table-column>
          <!-- <el-table-column label="操作" align="center" width="200" v-if="resultFlag">
            <template #default="scope">
              <el-button
                v-if="!scope.row.assessmentScore"
                link
                type="primary"
                @click="handleExamine(scope.row)"
              >
                考核
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>
      </el-col>
    </el-row>
  </ContentWrap>

  <YearExamineForm
    ref="formRef"
    @success="getTemplateList"
    :quarterWorkId="row.id"
    :unitId="queryParams.unitId"
  />
  <ExamineDialog
    ref="examineDialogRef"
    @fetch-data="getTemplateList"
    :assessmentYear="row.assessmentYear"
    :currentTabId="queryParams.examineType"
    :unitId="queryParams.unitId"
    :unitName="queryParams.unitName"
  />
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/modules/user'
import { QuarterExamineDetailApi } from '@/api/examineTask/quarterExamine'
import { TemplateEvaluationApi } from '@/api/template/checkTemplate'
import YearExamineForm from './YearExamineForm.vue'
import ExamineDialog from '../examineDialog.vue'
import download from '@/utils/download'
import { getSpanArrays } from '@/utils/tableSpanMethod'

defineOptions({ name: 'YearExamineDetail' })

const props = defineProps({
  row: {
    type: Object,
    default: () => ({})
  }
})
const deptId = useUserStore().getUser.deptId
const resultFlag = inject('resultFlag', false)
const emit = defineEmits(['updateCurrentView'])
const route = useRoute()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter()
const loading = ref(false) // 列表的加载中
const tableData = ref<any>([]) // 列表的数据
const formRef = ref()
const flag = ref('edit')
const queryParams = ref<any>({
  pageSize: -1,
  pageNo: 1,
  unitId: undefined,
  examineType: 2,
  quarterWorkId: props.row.id
})

/** 查询列表 */
const getTemplateList = async () => {
  loading.value = true
  try {
    // const sessionObj = JSON.parse(sessionStorage.getItem('examineResult') as any)
    if (!excludeIds.value.includes(queryParams.value.unitId)) {
      tableData.value = []
      return
    }
    const data = await TemplateEvaluationApi.getTemplateEvaluationPage({
      ...queryParams.value,
      templateId: props.row.templateId,
      year: props.row.assessmentYear
    })
    tableData.value = data.list.map((item) => ({
      ...item,
      // 自评相关信息
      annex: item.selfAssessmentRespVO ? item.selfAssessmentRespVO.annex : undefined,
      id: item.selfAssessmentRespVO ? item.selfAssessmentRespVO.id : undefined,
      score: item.selfAssessmentRespVO ? item.selfAssessmentRespVO.score : undefined,
      remark: item.selfAssessmentRespVO ? item.selfAssessmentRespVO.remark : undefined,
      templateQuotaId: item.id
    }))
    getSpanArr()
  } finally {
    loading.value = false
  }
}

// 存储各列的合并信息
const spanArrays: any = ref([])

// 定义每列的合并规则配置
const columnMergeRules = [
  // 第一列：类型 (primaryElement)
  ['primaryElement'],
  // 第二列：指标名称 (secondaryElement)
  ['primaryElement', 'secondaryElement'],
  // 第三列：评分标准 (thirdElement)
  ['primaryElement', 'secondaryElement', 'thirdElement'],
  // 第四列：分值 (standardScore)
  ['primaryElement', 'secondaryElement', 'standardScore'],
  // 第五列：评分规则 (examineContent)
  ['primaryElement', 'secondaryElement', 'thirdElement', 'examineContent']
]

const getSpanArr = () => {
  // 根据配置动态生成各列的合并信息
  spanArrays.value = getSpanArrays(tableData.value, columnMergeRules)
}

const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 只处理需要合并的列
  if (columnIndex >= 1 && columnIndex <= 5) {
    // 由于序号列不参与合并，需要调整索引
    const adjustedIndex = columnIndex - 1
    const spanArr = spanArrays.value[adjustedIndex]
    if (spanArr) {
      const _row = spanArr[rowIndex]
      const _col = _row > 0 ? 1 : 0
      return {
        rowspan: _row,
        colspan: _col
      }
    }
  }
  // 其他列不合并
  return {
    rowspan: 1,
    colspan: 1
  }
}

const handleUpdate = (row) => {
  formRef.value.open(row)
}
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await QuarterExamineDetailApi.deleteWorkDetail(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getTemplateList()
  } catch {}
}

const activeDeptId = ref()
/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  activeDeptId.value = row.id
  queryParams.value.unitId = row.id
  queryParams.value.unitName = row.name
  await getTemplateList()
}

const handleBack = () => {
  emit('updateCurrentView', 'YearExamineList')
}
const examineDialogRef = ref()
const handleExamine = (row) => {
  examineDialogRef.value.openDialog({ ...row, id: props.row.id, specialDetailId: row.id })
}
/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await QuarterExamineDetailApi.exportWorkDetail({
      ...queryParams.value,
      templateId: props.row.templateId,
      year: props.row.assessmentYear
    })
    download.excel(data, '年度考核指标明细.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const excludeIds = computed(() => {
  return props.row.refIds?.split(',').map(Number)
})
/** 初始化 **/
onMounted(async () => {
  flag.value = props.row.type
  if (route.path === '/examineTask/yearExamine') {
    queryParams.value.unitId = deptId
    getTemplateList()
  } else if (route.path === '/examineProcess/yearExamine') {
    handleDeptNodeClick(props.row?.deptList?.[0])
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-link) {
  width: 100%;
  text-align: center;
}
</style>
