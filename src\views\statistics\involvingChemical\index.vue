<!--
 * @Description: 涉及化学品统计
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-15 11:11:25
 * @LastEditTime: 2024-08-16 11:18:03
-->
<template>
  <ContentWrap>
    <el-table :data="tableData" class="mb-20px">
      <el-table-column>使用集团数量</el-table-column>
      <el-table-column v-for="(value, key) in keyMapping" :label="key">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="openDetail(scope)">
            {{ scope.row[value].t3 }}
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <div v-if="detailTableData.length" class="text-20px font-bold mb-10px">
      {{ currentTabName }}
    </div>
    <div class="flex flex-wrap" v-for="arr in detailTableData">
      <el-table class="!w-500px" :data="arr">
        <el-table-column label="集团名称" prop="deptName" />
        <el-table-column label="名称" prop="relateName" />
        <el-table-column label="使用情况" prop="relateUsedDetail" />
      </el-table>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import { getChemicalsInvolvedData, getRelatePageData } from '@/api/statistics/involvingChemical'

defineOptions({ name: 'InvolvingChemical' })

const loading = ref(true) // 列表的加载中
const tableData = ref<any>([])
const detailTableData = ref<any>([])
const keyMapping = {
  粉尘涉爆: 'dustExplosion',
  涉氨制冷: 'ammoniaRefrigeration',
  高温熔融金属: 'highTempMoltenMetal',
  重大危险源: 'majorHazard'
}
const getList = async () => {
  loading.value = true
  try {
    const data = await getChemicalsInvolvedData()
    // 创建包含业务相关键值对的对象
    const resultObject = data.reduce((acc, item) => {
      const keyName = keyMapping[item.t2]
      acc[keyName] = { ...item }
      return acc
    }, {})
    tableData.value = [resultObject]
  } finally {
    loading.value = false
  }
}
const currentTabName = ref('')
const openDetail = async (scope) => {
  loading.value = true
  const { column, row } = scope
  const key = keyMapping[column.label]
  currentTabName.value = row[key].t2
  try {
    const data = await getRelatePageData({ relateType: row[key].t1 })
    detailTableData.value = data
  } finally {
    loading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  getList()
})
</script>
