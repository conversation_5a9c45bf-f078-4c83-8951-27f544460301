<!--
 * @Description: 成绩统计
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-22 17:05:30
 * @LastEditTime: 2024-09-02 14:15:59
-->
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="mb-5px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="88px"
    >
      <el-form-item label="集团名称" prop="unitName">
        <el-input
          v-model="queryParams.unitName"
          placeholder="请输入集团名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item label="年度" prop="examineYear">
        <el-date-picker
          class="!w-100%"
          v-model="queryParams.examineYear"
          type="year"
          placeholder="请选择年度"
          value-format="YYYY"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-button
      class="float-right mb-10px"
      type="success"
      plain
      @click="handleExport"
      :loading="exportLoading"
    >
      <Icon icon="ep:download" />导出
    </el-button>
    <el-table
      v-loading="loading"
      :data="tableData"
      :stripe="true"
      :show-overflow-tooltip="true"
      height="600"
    >
      <el-table-column type="index" label="序号" width="60"></el-table-column>
      <el-table-column label="集团名称" align="center" prop="unitName" min-width="150">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="handleGroupDetail(scope.row)">
            {{ scope.row.unitName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column :label="`综合分值`" align="center" prop="totalScore" width="140">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="handleDetail(scope.row, '0')">
            {{ handleText('totalScore', scope.row) }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column :label="`基本概况`" align="center" prop="basicScore" width="140">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="handleDetail(scope.row, '0')">
            {{ handleText('basicScore', scope.row) }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column :label="`事故填报`" align="center" prop="accidentScore" width="140">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="handleDetail(scope.row, '1')">
            {{ handleText('accidentScore', scope.row) }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column :label="`年度工作`" align="center" prop="yearlyScore" width="140">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="handleDetail(scope.row, '2')">
            {{ handleText('yearlyScore', scope.row) }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column :label="`专项工作`" align="center" prop="specialScore" width="140">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="handleDetail(scope.row, '3')">
            {{ handleText('specialScore', scope.row) }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="考核时间"
        align="center"
        prop="assessmentTime"
        width="170"
        :formatter="dateFormatter"
      />
    </el-table>

    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/modules/user'
import download from '@/utils/download'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { SchemeApi } from '@/api/template/examinePlan'
import { ResultsApi } from '@/api/examineResult'
import { uniqBy } from 'lodash-es'
import dayjs from 'dayjs'

defineOptions({ name: 'Score' })

const deptId = useUserStore().getUser.deptId
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter()
const route = useRoute()
const loading = ref(true) // 列表的加载中
const tableData = ref<any>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const yearList = ref<any>([])
const activeYear = ref()
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  unitId: undefined,
  unitName: undefined,
  examineYear: dayjs().format('YYYY'),
  basicScoreToplimit: undefined,
  basicScore: undefined,
  yearlyScoreToplimit: undefined,
  yearlyScore: undefined,
  specialScoreToplimit: undefined,
  specialScore: undefined,
  accidentScoreToplimit: undefined,
  accidentScore: undefined,
  status: undefined,
  createTime: [],
  status: 1
})
const queryFormRef = ref() // 搜索的表单

/** 搜索按钮操作 */
const handleQuery = async () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ResultsApi.getResultsPage({
      ...queryParams.value
    })
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ResultsApi.exportResults({
      ...queryParams.value
    })
    download.excel(data, '成绩统计.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const handleChangeYear = (item) => {
  activeYear.value = item
  getList()
}

const handleDetail = (row, compIndex) => {
  // const sessionObj = {
  //   examineYear: queryParams.value.examineYear,
  //   compIndex: compIndex,
  //   status: row.status,
  //   unitId: row.unitId,
  //   unitName: row.unitName,
  //   totalScoreToplimit: row.totalScoreToplimit,
  //   basicScoreToplimit: row.basicScoreToplimit,
  //   accidentScoreToplimit: row.accidentScoreToplimit,
  //   yearlyScoreToplimit: row.yearlyScoreToplimit,
  //   specialScoreToplimit: row.specialScoreToplimit
  // }
  // sessionStorage.setItem('examineResult', JSON.stringify(sessionObj))
  // push({
  //   path: '/examineResult/resultTab'
  // })
}

const handleGroupDetail = (row) => {
  push({
    path: '/examineObj/businessDetail',
    query: {
      type: 'detail',
      deptId: row.unitId,
      backPath: route.path
    }
  })
}

const handleText = (field, row) => {
  if (row[field] === null || row[field] === undefined) {
    return '考核'
  } else {
    return `${row[field]}分 (上限：${row[field + 'Toplimit']}分)`
  }
}

const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(async () => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-link) {
  width: 100%;
  text-align: center;
}
</style>
