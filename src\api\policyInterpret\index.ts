import request from '@/config/axios'

// 政策解读 VO
export interface InterpretationVO {
  id: number // 公告ID
  title: string // 公告标题
  userId: number // 用户ID
  contentType: number // 内容形式
  receiveObjs: string // 接收对象
  content: string // 公告内容
  status: number // 公告状态（0未发布 1已发布）
  attachments: string // 附件
}

// 政策解读 API
export const InterpretationApi = {
  // 查询政策解读分页
  getInterpretationPage: async (params: any) => {
    return await request.get({ url: `/system/policy/page`, params })
  },

  // 查询政策解读详情
  getInterpretation: async (id: number) => {
    return await request.get({ url: `/system/policy/get?id=` + id })
  },

  // 新增政策解读
  createInterpretation: async (data: any) => {
    return await request.post({ url: `/system/policy/create`, data })
  },

  // 修改政策解读
  updateInterpretation: async (data: any) => {
    return await request.put({ url: `/system/policy/update`, data })
  },

  // 删除政策解读
  deleteInterpretation: async (id: number) => {
    return await request.delete({ url: `/system/policy/delete?id=` + id })
  },

  // 导出政策解读 Excel
  exportInterpretation: async (params) => {
    return await request.download({ url: `/system/policy/export-excel`, params })
  }
}
