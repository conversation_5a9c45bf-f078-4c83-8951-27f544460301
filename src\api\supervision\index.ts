import request from '@/config/axios'

// 督查督办 VO
export interface SupervisionTasksVO {
  id: number // 主键
  supervisionName: string // 督查督办名称
  content: string // 督查督办内容
  evaluatedUnit: string // 被考核单位
  dueDate: Date // 截止日期
  relatedPenaltyRule: string // 关联扣分规则
  status: number // 状态
  completionStatus: number // 完成情况
}

// 督查督办 API
export const SupervisionTasksApi = {
  // 查询督查督办分页
  getSupervisionTasksPage: async (params: any) => {
    return await request.get({ url: `/examine/supervision-tasks/page`, params })
  },

  // 查询督查督办详情
  getSupervisionTasks: async (id: number) => {
    return await request.get({ url: `/examine/supervision-tasks/get?id=` + id })
  },

  // 新增督查督办
  createSupervisionTasks: async (data: SupervisionTasksVO) => {
    return await request.post({ url: `/examine/supervision-tasks/create`, data })
  },

  // 修改督查督办
  updateSupervisionTasks: async (data: any) => {
    return await request.put({ url: `/examine/supervision-tasks/update`, data })
  },

  // 删除督查督办
  deleteSupervisionTasks: async (id: number) => {
    return await request.delete({ url: `/examine/supervision-tasks/delete?id=` + id })
  },

  // 导出督查督办 Excel
  exportSupervisionTasks: async (params) => {
    return await request.download({ url: `/examine/supervision-tasks/export-excel`, params })
  },

  // 获得导入督查督办模板
  getImportTemplate: async (params) => {
    return await request.download({ url: `/examine/supervision-tasks/get-import-template`, params })
  },

  upUrl: '/examine/supervision-tasks/import'
}
