<!--
 * @Description: 考核弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-08 09:40:45
 * @LastEditTime: 2024-10-31 10:57:41
-->
<template>
  <el-dialog
    center
    title="考核"
    v-model="dialogVisible"
    width="680px"
    append-to-body
    destroy-on-close
  >
    <el-form
      v-loading="formLoading"
      class="-mb-15px"
      :model="formData"
      ref="formRef"
      :inline="true"
      label-width="88px"
      :rules="formRules"
    >
      <el-form-item label="考核分数" prop="score">
        <el-input-number
          class="!w-240px"
          :max="topLimit"
          v-model="formData.score"
          placeholder="请输入考核分数"
        />
      </el-form-item>
      <el-form-item label="考核点评" prop="opinions">
        <el-input
          class="!w-240px"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 6 }"
          v-model="formData.opinions"
          placeholder="请输入考核点评"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">提 交</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ScoreApi } from '@/api/examineResult/resultScore'
import { cloneDeep } from 'lodash-es'
import { ExamineResultTypeMap } from '@/utils/constants'

defineOptions({ name: 'ExamineDialog' })

const props = defineProps({
  currentTabId: {
    type: String,
    default: ''
  },
  assessmentYear: {
    type: Number
  },
  unitId: {
    type: Number
  },
  unitName: {
    type: String
  }
})
const formRules = reactive({
  score: [{ required: true, message: '考核分数不能为空', trigger: 'blur' }]
})
const emit = defineEmits(['fetch-data'])
const dialogVisible = ref(false)
const message = useMessage() // 消息弹窗
const formLoading = ref(false)
const formData = ref<any>({
  score: undefined,
  opinions: undefined
})
const primaryKeyId = ref('')
const detailKeyId = ref('')
const topLimit = ref()
const formRef = ref() // 搜索的表单
const openDialog = async (row) => {
  topLimit.value = row.standardScore
  primaryKeyId.value = row.id
  if (row.templateQuotaId) {
    detailKeyId.value = row.templateQuotaId
  }
  resetForm()
  formData.value = {
    score: row.assessmentScore,
    opinions: row.assessmentRemark
  }
  dialogVisible.value = true
}

const handleSubmit = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const submitData = {
      ...cloneDeep(formData.value),
      specialWorkId: primaryKeyId.value,
      specialDetailId: detailKeyId.value ? detailKeyId.value : undefined,
      scoreStatus: 1,
      examineYear: props.assessmentYear,
      examineType: props.currentTabId,
      unitId: props.unitId,
      unitName: props.unitName
    }
    await ScoreApi.addEditScore(submitData)
    dialogVisible.value = false
    message.success('提交成功')
    emit('fetch-data')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    score: undefined,
    opinions: undefined
  }
  formRef.value?.resetFields()
}
defineExpose({
  openDialog
})
</script>
