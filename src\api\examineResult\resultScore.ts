import request from '@/config/axios'

// 考核分数 VO
export interface ScoreVO {
  id: number // 主键(自增策略)
  unitId: number // 考核单位
  examineType: number // 考核评分类型  0-基本概况 1-事故填报 2-年度工作 3-专项工作
  examineYear: number // 考核年度
  scoreToplimit: number // 分值上限
  score: number // 考核分数
  opinions: string // 考核意见
  status: number // 状态  0-未提交 1-已提交
  yearWorkId: number // 年度工作ID
  specialWorkId: number // 专项工作ID
}

// 考核分数 API
export const ScoreApi = {
  // 查询考核分数分页
  getScorePage: async (params: any) => {
    return await request.get({ url: `/system/assessment/score/page`, params })
  },

  // 查询考核分数详情
  getScore: async (id: number) => {
    return await request.get({ url: `/system/assessment/score/get?id=` + id })
  },

  // 新增考核分数
  createScore: async (data: ScoreVO) => {
    return await request.post({ url: `/system/assessment/score/create`, data })
  },

  // 修改考核分数
  updateScore: async (data: ScoreVO) => {
    return await request.put({ url: `/system/assessment/score/update`, data })
  },

  // 删除考核分数
  deleteScore: async (id: number) => {
    return await request.delete({ url: `/system/assessment/score/delete?id=` + id })
  },

  // 导出考核分数 Excel
  exportScore: async (params) => {
    return await request.download({ url: `/system/assessment/score/export-excel`, params })
  },

  // 新增/修改考核分数
  addEditScore: async (data: ScoreVO) => {
    return await request.post({ url: `/system/assessment/score/saveOrUpdate`, data })
  },
  
}
