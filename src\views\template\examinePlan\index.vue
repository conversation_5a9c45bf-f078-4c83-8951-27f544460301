<!--
 * @Description: 考核方案
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-29 16:53:51
 * @LastEditTime: 2024-09-11 17:26:17
-->
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="98px"
    >
      <el-form-item label="方案名称" prop="examineSchemeTitle">
        <el-input
          v-model="queryParams.examineSchemeTitle"
          placeholder="请输入方案名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="考核年度" prop="introduceYear">
        <el-input
          v-model="queryParams.introduceYear"
          placeholder="请输入考核年度"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="考核方式" prop="examineMethodId">
        <el-input
          v-model="queryParams.examineMethodId"
          placeholder="请输入考核方式"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>

      <el-row :gutter="10" class="mb-8px">
        <el-form-item class="ml-5px">
          <el-button type="primary" plain @click="handleAddEdit('add')">
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" align="center" type="index" width="60" />
      <el-table-column label="方案名称" align="center" prop="examineSchemeTitle" />
      <el-table-column label="考核方式" align="center" prop="examineMethodName" />
      <el-table-column label="考核年度" align="center" prop="introduceYear" width="100" />
      <el-table-column
        label="被考核单位"
        align="center"
        prop="examineUnitIdsName"
        min-width="250"
      />
      <el-table-column label="考核指标" align="center" prop="examineTemplateName" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="190px"
      />
      <el-table-column label="状态" align="center" prop="schemeStatus" width="100">
        <template #default="scope">
          <el-switch
            :disabled="scope.row.schemeStatus === 1"
            v-model="scope.row.schemeStatus"
            :active-value="1"
            :inactive-value="0"
            @change="handleChangeState(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template #default="scope">
          <template v-if="scope.row.schemeStatus !== 1">
            <el-button link type="warning" @click="handleAddEdit('edit', scope.row.id)">
              编辑
            </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
          <el-button link type="primary" @click="handleAddEdit('detail', scope.row.id)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { SchemeApi } from '@/api/template/examinePlan'

defineOptions({ name: 'ExaminePlan' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter()
const loading = ref(true) // 列表的加载中
const list = ref<any>([]) // 列表的数据
const total = ref(0) // 列表的总页数

const queryParams: any = reactive({
  pageNo: 1,
  pageSize: 10,
  examineSchemeTitle: undefined,
  examineMethodId: undefined,
  schemeStatus: undefined,
  introduceYear: undefined,
  examineUnitIds: undefined,
  examineTemplateId: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await SchemeApi.getSchemePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 新增
const handleAddEdit = (type, id?) => {
  if (type === 'add') {
    push(`/template/examinePlanCreate?optFlag=${type}`)
  } else {
    push(`/template/examinePlanCreate?optFlag=${type}&id=${id}`)
  }
}

/** 搜索按钮操作 */
const handleQuery = async () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SchemeApi.deleteScheme(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await SchemeApi.exportScheme(queryParams)
    download.excel(data, '考核方案.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 更新状态操作 */
const handleChangeState = async (row) => {
  const state = row.schemeStatus
  try {
    // 修改状态的二次确认
    const id = row.id
    const statusState = state === 1 ? '启用' : '挂起'
    const content = '是否确认' + statusState + '方案名称为"' + row.examineSchemeTitle + '"的数据项?'
    await message.confirm(content)
    // 发起修改状态
    await SchemeApi.updateScheme({
      id,
      schemeStatus: state,
      examineUnitIds: row.examineUnitIds,
      introduceYear: row.introduceYear
    })
    // 刷新列表
    await getList()
  } catch {
    // 取消后，进行恢复按钮
    row.schemeStatus = state === 1 ? 0 : 1
  }
}

const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(() => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-link) {
  width: 100%;
  text-align: center;
}
</style>
