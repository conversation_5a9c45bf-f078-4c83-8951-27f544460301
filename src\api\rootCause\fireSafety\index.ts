import request from '@/config/axios'

// 消防安全专项治理任务推进 VO
export interface FireSafetyVO {
  id: number // 唯一标识ID
  unitId: number // 考核单位
  unitName: string // 单位名称
  fireIncidents: string // 发生有影响火灾事故情况
  avgBuildingsRecorded: number // 平均录入建筑数
  avgUnitsInspected: number // 平均录入排查单位数
  avgHazardsRate: number // 平均录入隐患率
  majorHazardUnitsOrAreas: number // 重大火灾隐患单位或区域数
  correctedIrregularGrids: number // 整治违规设置铁栅栏或防盗网或广告牌个数
  rectifiedHighRises: number // 完成整改高层住宅小区数
  rectificationRate: number // 整改率
  status: number // 状态   0-待提交  1-已提交
  quarter: string // 季度
}

// 消防安全专项治理任务推进 API
export const FireSafetyApi = {
  // 查询消防安全专项治理任务推进分页
  getFireSafetyPage: async (params: any) => {
    return await request.get({ url: `/examine/fire-safety/page`, params })
  },

  // 查询消防安全专项治理任务推进详情
  getFireSafety: async (id: number) => {
    return await request.get({ url: `/examine/fire-safety/get?id=` + id })
  },

  // 新增消防安全专项治理任务推进
  createFireSafety: async (data: FireSafetyVO) => {
    return await request.post({ url: `/examine/fire-safety/create`, data })
  },

  // 修改消防安全专项治理任务推进
  updateFireSafety: async (data: FireSafetyVO) => {
    return await request.put({ url: `/examine/fire-safety/update`, data })
  },
  
  // 新增修改燃气安全专项治理燃气管网改造进度
  saveOrUpdateFireSafety: async (data: FireSafetyVO) => {
    return await request.post({ url: `/examine/fire-safety/saveOrUpdate`, data })
  },

  // 删除消防安全专项治理任务推进
  deleteFireSafety: async (id: number) => {
    return await request.delete({ url: `/examine/fire-safety/delete?id=` + id })
  },

  // 导出消防安全专项治理任务推进 Excel
  exportFireSafety: async (params) => {
    return await request.download({ url: `/examine/fire-safety/export-excel`, params })
  },

  // 获得导入模板
  getImportTemplate: async (params) => {
    return await request.download({ url: `/examine/fire-safety/get-import-template`, params })
  },

  // 导入接口地址
  upUrl: '/examine/fire-safety/import'
}
