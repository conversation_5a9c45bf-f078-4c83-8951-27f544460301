{"name": "gov_examine", "version": "1.0.0", "description": "城市运行安全考核系统", "author": "<PERSON>", "private": true, "scripts": {"dev": "vite --mode base", "ts:check": "vue-tsc --noEmit", "build:pro": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode pro", "build:base": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode base", "build:stage": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode stage", "serve:pro": "vite preview --mode pro", "preview": "npm build:base && vite preview", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\""}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@form-create/designer": "^3.1.3", "@form-create/element-ui": "^3.1.24", "@iconify/iconify": "^3.1.1", "@videojs-player/vue": "^1.0.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue-office/pptx": "^1.0.1", "@vueuse/core": "^10.6.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.10", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "^1.6.1", "bpmn-js-token-simulation": "^0.10.0", "camunda-bpmn-moddle": "^7.0.1", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dhtmlx-gantt": "^8.0.7", "diagram-js": "^12.8.0", "driver.js": "^1.3.1", "echarts": "^5.4.3", "echarts-wordcloud": "^2.1.0", "element-plus": "2.4.2", "fast-xml-parser": "^4.3.2", "highlight.js": "^11.9.0", "js-base64": "^3.7.7", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "min-dash": "^4.1.1", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "qrcode": "^1.5.3", "qs": "^6.11.2", "sortablejs": "^1.15.0", "steady-xml": "^0.1.0", "url": "^0.11.3", "video.js": "^7.21.5", "vue": "^3.3.8", "vue-dompurify-html": "^4.1.4", "vue-i18n": "^9.6.5", "vue-router": "^4.2.5", "vue-types": "^5.1.1", "vuedraggable": "^4.1.0", "web-storage-cache": "^1.1.1", "xml-js": "^1.6.11"}, "devDependencies": {"@commitlint/cli": "^18.4.1", "@commitlint/config-conventional": "^18.4.0", "@iconify/json": "^2.2.142", "@intlify/unplugin-vue-i18n": "^1.5.0", "@purge-icons/generated": "^0.9.0", "@types/lodash-es": "^4.17.11", "@types/node": "^20.9.0", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.10", "@types/sortablejs": "^1.15.5", "@unocss/transformer-variant-group": "^0.57.4", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^4.4.1", "@vitejs/plugin-vue-jsx": "^3.0.2", "autoprefixer": "^10.4.16", "bpmn-js": "8.9.0", "bpmn-js-properties-panel": "0.46.0", "consola": "^3.2.3", "html2canvas": "^1.4.1", "huawei-obs-plugin": "^1.0.2", "jspdf": "^2.5.1", "postcss": "^8.4.31", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.9", "prettier": "^3.1.0", "rimraf": "^5.0.5", "rollup": "^4.4.1", "sass": "^1.69.5", "terser": "^5.24.0", "typescript": "5.2.2", "unocss": "^0.57.4", "unplugin-auto-import": "^0.16.7", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.25.2", "vite": "4.5.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-ejs": "^1.6.4", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-top-level-await": "^1.3.1", "vue-tsc": "^1.8.22"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://codeup.aliyun.com/bw/law/law-admin-web.git"}, "engines": {"node": ">= 16.0.0", "npm": ">=8.6.0"}, "volta": {"node": "18.20.3"}}