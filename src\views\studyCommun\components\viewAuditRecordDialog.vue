<!--
 * @Description: 审批记录弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-30 13:10:45
 * @LastEditTime: 2024-07-30 15:14:59
-->
<template>
  <Dialog v-model="dialogVisible" title="审批记录" width="1300" center>
    <el-table :data="recordList" border>
      <el-table-column label="序号" type="index" width="60"></el-table-column>
      <el-table-column prop="title" label="文件名称" />
      <el-table-column prop="uploadUnit" label="上传单位" />
      <el-table-column prop="uploadTime" label="上传时间" :formatter="dateFormatter" />
      <el-table-column prop="creator" label="审批账号" />
      <el-table-column prop="reviewResult" label="审批结果" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.VERIFYING_RESULT" :value="scope.row.reviewResult" />
        </template>
      </el-table-column>
      <el-table-column prop="reviewOpinions" label="审批意见" />
      <el-table-column prop="createTime" label="审批时间" :formatter="dateFormatter" />
    </el-table>
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { dateFormatter } from '@/utils/formatTime'
import { DICT_TYPE } from '@/utils/dict'
import { LearningApprovalApi } from '@/api/studyCommun/studyApproval'

defineOptions({ name: 'ViewAuditRecordDialog' })

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const recordList = ref<any>([])
const primaryInfo = ref<any>({})

/** 打开弹窗 */
const open = async (row) => {
  dialogVisible.value = true
  formLoading.value = true
  try {
    primaryInfo.value = row
    const res = await LearningApprovalApi.getLearningApprovalPage({
      learningId: primaryInfo.value.id,
      pageSize: 100,
      pageNo: 1
    })
    recordList.value = res.list
  } finally {
    formLoading.value = false
  }
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
