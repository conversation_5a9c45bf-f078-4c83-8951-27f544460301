<!--
 * @Description: 自评打分
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-09-03 15:55:22
 * @LastEditTime: 2024-09-03 16:49:21
-->

<template>
  <el-dialog
    center
    title="自评打分"
    v-model="dialogVisible"
    width="480px"
    append-to-body
    destroy-on-close
  >
    <el-form
      v-loading="formLoading"
      class="-mb-15px"
      :model="formData"
      ref="formRef"
      :inline="true"
      label-width="108px"
      :rules="formRules"
    >
      <el-form-item label="自评分数" prop="score">
        <el-input
          @input="(v) => (formData.score = v.replace(/[^\d.]/g, ''))"
          v-model="formData.score"
          placeholder="请输入自评分数"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">提 交</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ScoreApi } from '@/api/examineResult/resultScore'
import { cloneDeep } from 'lodash-es'

defineOptions({ name: 'SelfEvaDialog' })

const props = defineProps({
  unitId: {
    type: Number
  }
})

const formRules = reactive({
  score: [{ required: true, message: '自评分数不能为空', trigger: 'blur' }]
})
const emit = defineEmits(['fetch-data'])
const dialogVisible = ref(false)
const message = useMessage() // 消息弹窗
const formLoading = ref(false)
const formData = ref<any>({
  score: undefined
})
const primaryKeyId = ref('')
const formRef = ref() // 搜索的表单
const openDialog = async (row) => {
  primaryKeyId.value = row.id
  resetForm()
  dialogVisible.value = true
}

const handleSubmit = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const submitData = {
      ...cloneDeep(formData.value),
      specialDetailId: primaryKeyId.value,
      examineType: 3,
      unitId: props.unitId
    }
    await ScoreApi.addEditScore(submitData)
    dialogVisible.value = false
    message.success('提交成功')
    emit('fetch-data')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    score: undefined
  }
  formRef.value?.resetFields()
}
defineExpose({
  openDialog
})
</script>
