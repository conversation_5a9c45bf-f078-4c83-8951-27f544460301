<template>
  <el-dialog
    title="客户选择"
    v-model="dialogVisible"
    width="1000px"
    append-to-body
    destroy-on-close
  >
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="88px"
    >
      <el-form-item label="客户名" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      highlight-current-row
      @current-change="handleCurrentChange"
    >
      <!-- <el-table-column label="客户编号" align="center" prop="id" width="100" /> -->
      <el-table-column label="客户名" align="center" prop="customerName" />
      <el-table-column label="负责人" align="center" prop="responsibleName" />
      <!-- <el-table-column label="客户组" align="center" prop="customerGroup">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CUSTOMER_GROUP" :value="scope.row.customerGroup" />
        </template>
      </el-table-column>
      <el-table-column label="母公司" align="center" prop="parentCompany" /> -->
      <el-table-column label="地址" align="center" prop="address" />
      <el-table-column label="联系电话" align="center" prop="telephone1" />
      <!-- <el-table-column label="部门名称" align="center" prop="deptName" width="150" />
      <el-table-column
        label="合作始于"
        align="center"
        prop="cooperateStart"
        :formatter="dateFormatter2"
        width="190px"
      />
      <el-table-column label="公司网站" align="center" prop="companyWebsite" width="200" />
      <el-table-column label="行业" align="center" prop="industry" width="150">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INDUSTRY" :value="scope.row.industry" />
        </template>
      </el-table-column>
      <el-table-column label="机构性质" align="center" prop="institutionalNature" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INSTITUTIONAL_NATURE" :value="scope.row.institutionalNature" />
        </template>
      </el-table-column>
      <el-table-column label="员工数" align="center" prop="numberEmployees" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.NUMBER_EMPLOYEES" :value="scope.row.numberEmployees" />
        </template>
      </el-table-column>
      <el-table-column label="年收入" align="center" prop="annualIncome" width="150">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ANNUAL_INCOME" :value="scope.row.annualIncome" />
        </template>
      </el-table-column>
      <el-table-column label="分级" align="center" prop="classification">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASSIFICATION" :value="scope.row.classification" />
        </template>
      </el-table-column>
      <el-table-column label="来源" align="center" prop="sourceId">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SOURCE_ID" :value="scope.row.sourceId" />
        </template>
      </el-table-column>
      <el-table-column
        label="纳税人识别号"
        align="center"
        prop="taxpayerIdentificationNumber"
        width="250"
      />
      <el-table-column label="开户行" align="center" prop="openingBank" width="150" />
      <el-table-column label="账户" align="center" prop="account" />
      <el-table-column label="到期（天）" align="center" prop="expireDays" width="100" /> -->
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <template #footer>
      <el-button size="small" @click="dialogVisible = false">取 消</el-button>
      <el-button size="small" type="primary" @click="chooseCustomer">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
defineOptions({ name: 'CustomerDialog' })

let dialogVisible = ref(false)
import { dateFormatter2 } from '@/utils/formatTime'
import { CustomerApi, CustomerVO } from '@/api/sales/customerInfo'
import { defaultProps, handleTree } from '@/utils/tree'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import { DICT_TYPE } from '@/utils/dict'

const emit = defineEmits(['fetch-data'])

const message = useMessage() // 消息弹窗
let currentRow = ref()
const loading = ref(true) // 列表的加载中
const list = ref<CustomerVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  deptId: undefined,
  deptName: undefined,
  responsibleId: undefined,
  responsibleName: undefined,
  customerName: undefined,
  customerFullName: undefined,
  parentCompany: undefined,
  customerGroup: undefined,
  registeredCapital: undefined,
  socialCreditCode: undefined,
  establishTime: [],
  cooperateStart: undefined,
  companyWebsite: undefined,
  industry: undefined,
  institutionalNature: undefined,
  numberEmployees: undefined,
  annualIncome: undefined,
  classification: undefined,
  sourceId: undefined,
  taxpayerIdentificationNumber: undefined,
  openingBank: undefined,
  address: undefined,
  account: undefined,
  expireDays: undefined,
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const deptList = ref<Tree[]>([]) // 树形结构
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CustomerApi.getCustomerPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const openDialog = async () => {
  dialogVisible.value = true
  await getList()
  userList.value = await UserApi.getSimpleUserList()
  // 加载部门树
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
}

const handleCurrentChange = (val) => {
  currentRow.value = val
}

const chooseCustomer = () => {
  if (currentRow.value) {
    dialogVisible.value = false
    emit('fetch-data', currentRow.value)
    message.success('选择成功')
  } else {
    message.error('请选择客户')
  }
}

defineExpose({
  openDialog
})
</script>
