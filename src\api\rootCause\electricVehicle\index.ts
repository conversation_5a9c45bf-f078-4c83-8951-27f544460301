import request from '@/config/axios'

// 电动自行车火灾事故及全链条治理重点任务推进 VO
export interface ElectricBikeFireVO {
  id: number // 唯一标识ID
  unitId: number // 考核单位
  unitName: string // 单位名称
  fireIncidents: number // 电动自行车火灾事故起数
  decreaseRate: number // 降幅
  inspectedBikes: number // 电动自行车排摸数
  registrationRatio: number // 占公安交警部门登记数比率
  quarter: string // 季度
  status: number // 状态   0-待提交  1-已提交
}

// 电动自行车火灾事故及全链条治理重点任务推进 API
export const ElectricBikeFireApi = {
  // 查询电动自行车火灾事故及全链条治理重点任务推进分页
  getElectricBikeFirePage: async (params: any) => {
    return await request.get({ url: `/examine/electric-bike-fire/page`, params })
  },

  // 查询电动自行车火灾事故及全链条治理重点任务推进详情
  getElectricBikeFire: async (id: number) => {
    return await request.get({ url: `/examine/electric-bike-fire/get?id=` + id })
  },

  // 新增电动自行车火灾事故及全链条治理重点任务推进
  createElectricBikeFire: async (data: ElectricBikeFireVO) => {
    return await request.post({ url: `/examine/electric-bike-fire/create`, data })
  },

  // 修改电动自行车火灾事故及全链条治理重点任务推进
  updateElectricBikeFire: async (data: ElectricBikeFireVO) => {
    return await request.put({ url: `/examine/electric-bike-fire/update`, data })
  },
  
  // 新增修改电动自行车火灾事故及全链条治理重点任务推进
  saveOrUpdateElectricBikeFire: async (data: ElectricBikeFireVO) => {
    return await request.post({ url: `/examine/electric-bike-fire/saveOrUpdate`, data })
  },

  // 删除电动自行车火灾事故及全链条治理重点任务推进
  deleteElectricBikeFire: async (id: number) => {
    return await request.delete({ url: `/examine/electric-bike-fire/delete?id=` + id })
  },

  // 导出电动自行车火灾事故及全链条治理重点任务推进 Excel
  exportElectricBikeFire: async (params) => {
    return await request.download({ url: `/examine/electric-bike-fire/export-excel`, params })
  },

  // 获得导入模板
  getImportTemplate: async (params) => {
    return await request.download({ url: `/examine/electric-bike-fire/get-import-template`, params })
  },

  // 导入接口地址
  upUrl: '/examine/electric-bike-fire/import'
}
