<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1000px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="160px"
      v-loading="formLoading"
    >
      <el-form-item label="类型" prop="primaryElement">
        <el-input v-model="formData.primaryElement" placeholder="请输入类型" />
      </el-form-item>
      <el-form-item label="指标名称" prop="secondaryElement">
        <el-input v-model="formData.secondaryElement" placeholder="请输入指标名称" />
      </el-form-item>
      <el-form-item label="评分标准" prop="thirdElement">
        <el-input
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 6 }"
          v-model="formData.thirdElement"
          placeholder="请输入评分标准"
        />
      </el-form-item>
      <el-form-item label="分值" prop="standardScore">
        <el-input-number v-model="formData.standardScore" :min="0" controls-position="right" />
      </el-form-item>
      <el-form-item label="评分规则" prop="examineContent">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          v-model="formData.examineContent"
          placeholder="请输入评分规则"
        />
      </el-form-item>
      <el-form-item label="计算类型" prop="calculateType">
        <el-select v-model="formData.calculateType" placeholder="请选择计算类型" clearable>
          <el-option
            v-for="item in CALCULATE_TYPE_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="运算符" prop="operator">
        <el-select v-model="formData.operator" placeholder="请选择运算符" clearable>
          <el-option
            v-for="item in OPERATOR_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="阈值" prop="threshold">
        <el-input-number v-model="formData.threshold" :min="0" controls-position="right" />
      </el-form-item>
      <el-form-item label="单项扣分值" prop="singleDeductScore">
        <el-input-number v-model="formData.singleDeductScore" :min="0" controls-position="right" />
      </el-form-item>
      <el-form-item label="扣分上限值" prop="maxDeductScore">
        <el-input-number v-model="formData.maxDeductScore" :min="0" controls-position="right" />
      </el-form-item>
      <el-form-item label="显示排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" controls-position="right" />
      </el-form-item>
      <el-form-item label="治本攻坚考察项标识" prop="campaignMark">
        <el-select
          v-model="formData.campaignMark"
          placeholder="请选择治本攻坚考察项标识"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.ROOT_CAUSE_TACKLING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="适用范围" prop="applicationScope">
        <el-tree-select
          v-model="formData.applicationScope"
          :data="applicationScopeTree"
          :props="applicationScopeProps"
          multiple
          node-key="value"
          placeholder="请选择适用范围"
          style="width: 100%"
          :default-expand-all="true"
          :check-strictly="false"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { TemplateEvaluationApi, TemplateEvaluationVO } from '@/api/template/checkTemplate'
import { CALCULATE_TYPE_OPTIONS, OPERATOR_OPTIONS } from '@/utils/constants/templateConfig'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import * as TemplateApi from '@/api/system/template'

/** 类型 表单 */
defineOptions({ name: 'TemplateEvaluationForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const templateType = ref('') // 主表的被考核单位类型
const formData: any = ref({
  id: undefined,
  primaryElement: undefined,
  secondaryElement: undefined,
  thirdElement: undefined,
  examineContent: undefined,
  standardScore: undefined,
  examineWay: undefined,
  sort: undefined,
  templateId: undefined,
  calculateType: undefined,
  operator: undefined,
  threshold: undefined,
  singleDeductScore: undefined,
  maxDeductScore: undefined,
  campaignMark: undefined,
  applicationScope: '',
  refIds: ''
})
// 适用范围树数据
const applicationScopeTree: any = ref([])
// 适用范围树属性配置
const applicationScopeProps = {
  children: 'children',
  label: 'label',
  value: 'value'
}
const formRules = reactive({
  primaryElement: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
  secondaryElement: [{ required: true, message: '指标名称不能为空', trigger: 'blur' }],
  thirdElement: [{ required: true, message: '评分标准不能为空', trigger: 'blur' }],
  examineContent: [{ required: true, message: '评分规则不能为空', trigger: 'blur' }],
  standardScore: [{ required: true, message: '分值不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '显示排序不能为空', trigger: 'blur' }],
  // campaignMark: [{ required: true, message: '治本攻坚考察项标识不能为空', trigger: 'change' }],
  applicationScope: [{ required: true, message: '适用范围不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number, templateId?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  if (type == 'create') {
    formData.value.templateId = templateId
    // 获取主表的templateType
    if (templateId) {
      const templateData = await TemplateApi.getTemplate(templateId)
      templateType.value = templateData.templateType
      // 构建适用范围树数据
      buildApplicationScopeTree()
    }
  }
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const result = await TemplateEvaluationApi.getTemplateEvaluation(id)
      formData.value = result

      // 处理回显数据转换
      if (typeof formData.value.applicationScope === 'string') {
        formData.value.applicationScope = formData.value.applicationScope
          ? formData.value.applicationScope.split(',')
          : []
      }

      // 获取主表的templateType
      if (formData.value.templateId) {
        const templateData = await TemplateApi.getTemplate(formData.value.templateId)
        templateType.value = templateData.templateType
        // 构建适用范围树数据
        buildApplicationScopeTree()
      }
    } finally {
      formLoading.value = false
    }
  }
}

// 构建适用范围树数据
const buildApplicationScopeTree = () => {
  // 特殊处理district_templates类型
  if (templateType.value === 'district_templates') {
    buildDistrictTemplatesTree()
  } else {
    // 一般情况：一级菜单为templateType对应的字典值，二级菜单为templateType字典
    const parentDict = getStrDictOptions(DICT_TYPE.TEMPLATE_TYPE).find(
      (dict) => dict.value === templateType.value
    )
    if (parentDict) {
      applicationScopeTree.value = [
        {
          value: templateType.value,
          label: parentDict.label,
          children: getStrDictOptions(templateType.value)
        }
      ]
    }
  }
}

// 构建district_templates特殊树结构
const buildDistrictTemplatesTree = () => {
  const districtDicts = getStrDictOptions(DICT_TYPE.DISTRICT_TEMPLATES)

  // 构造中心城区和郊区节点
  const centerArea: any = {
    value: 'a',
    label: '中心城区',
    children: []
  }

  const suburbArea: any = {
    value: 'b',
    label: '郊区',
    children: []
  }

  // 根据前缀分配到对应区域
  districtDicts.forEach((dict) => {
    if (dict.value.startsWith('a-')) {
      centerArea.children.push(dict)
    } else if (dict.value.startsWith('b-')) {
      suburbArea.children.push(dict)
    }
  })

  // 构造最终树结构
  applicationScopeTree.value = [
    {
      value: 'district_templates',
      label: '各区',
      children: [centerArea, suburbArea]
    }
  ]
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 处理数据转换
  const data = { ...formData.value } as unknown as TemplateEvaluationVO

  // 如果applicationScope是数组，转换为逗号分隔的字符串
  if (Array.isArray(data.applicationScope)) {
    data.applicationScope = data.applicationScope.join(',')
  }

  // 生成refIds（所有叶子节点的remark值）
  const applicationScopeValue = Array.isArray(data.applicationScope)
    ? data.applicationScope.join(',')
    : data.applicationScope
  data.refIds = generateRefIds(applicationScopeValue).join(',')

  // 提交请求
  formLoading.value = true
  try {
    if (formType.value === 'create') {
      await TemplateEvaluationApi.createTemplateEvaluation(data)
      message.success(t('common.createSuccess'))
    } else {
      await TemplateEvaluationApi.updateTemplateEvaluation(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 生成refIds（所有叶子节点的remark值）
const generateRefIds = (selectedValues: string | string[]) => {
  // 处理空值情况
  if (!selectedValues) return []

  const selectedArray = Array.isArray(selectedValues)
    ? selectedValues
    : selectedValues.split(',').filter((item) => item.trim() !== '')

  const refIds: string[] = []

  // 获取所有可能的字典选项
  const allDicts = getStrDictOptions(DICT_TYPE.DISTRICT_TEMPLATES)

  // 遍历选中的值
  selectedArray.forEach((value) => {
    // 如果是叶子节点（区县），直接添加其remark
    const dict = allDicts.find((d) => d.value === value)
    if (dict && dict.remark) {
      refIds.push(dict.remark)
    }
    // 如果是父节点（中心城区或郊区），添加其所有子节点的remark
    else if (value === 'a' || value === 'b') {
      const prefix = value === 'a' ? 'a-' : 'b-'
      allDicts.forEach((d) => {
        if (d.value.startsWith(prefix) && d.remark) {
          refIds.push(d.remark)
        }
      })
    }
    // 如果是district_templates根节点，添加所有子节点的remark
    else if (value === 'district_templates') {
      allDicts.forEach((d) => {
        if (d.remark) {
          refIds.push(d.remark)
        }
      })
    }
  })

  return refIds
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    primaryElement: undefined,
    secondaryElement: undefined,
    thirdElement: undefined,
    examineContent: undefined,
    standardScore: undefined,
    examineWay: undefined,
    sort: undefined,
    templateId: undefined,
    calculateType: undefined,
    operator: undefined,
    threshold: undefined,
    singleDeductScore: undefined,
    maxDeductScore: undefined,
    campaignMark: undefined,
    applicationScope: '',
    refIds: ''
  }
  formRef.value?.resetFields()
}

defineExpose({ open })
</script>
