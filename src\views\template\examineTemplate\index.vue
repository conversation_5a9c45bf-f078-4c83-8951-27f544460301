<!--
 * @Description: 考核模版管理
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-22 17:05:30
 * @LastEditTime: 2025-07-25 15:19:24
-->
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="108px"
    >
      <el-form-item label="指标名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入指标名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item label="评估标准" prop="classify">
        <el-select
          v-model="queryParams.classify"
          placeholder="请选择类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TEMPLATE_CLASS_CHECK)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="评估周期" prop="queryTime">
        <el-date-picker
          clearable
          class="!w-240px"
          v-model="queryParams.queryTime"
          type="date"
          value-format="x"
          placeholder="选择评估周期"
        />
      </el-form-item>
      <el-form-item label="采集部门" prop="responsibleDept">
        <el-tree-select
          v-model="queryParams.responsibleDept"
          :data="deptList"
          default-expand-all
          :props="defaultProps"
          check-strictly
          node-key="id"
          placeholder="请选择部门"
        />
      </el-form-item>
      <el-form-item label="被考核单位" prop="templateType">
        <el-select
          v-model="queryParams.templateType"
          placeholder="请选择被考核单位"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.TEMPLATE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleQuery"
          ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
        >
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
      <el-row :gutter="10" class="mb-8">
        <el-col :span="1.5">
          <el-button type="primary" plain @click="handleOperation('create')">
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain @click="handleExport" :loading="exportLoading">
            <Icon icon="ep:upload" class="mr-5px" /> 导出
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" align="center" type="index" width="60" />
      <el-table-column label="指标名称" align="center" prop="templateName" />
      <el-table-column label="评估标准" align="center" prop="classify">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TEMPLATE_CLASS_CHECK" :value="scope.row.classify" />
        </template>
      </el-table-column>
      <el-table-column label="评估周期" align="center" width="230">
        <template #default="scope">
          <div v-if="scope.row.collectionStart">
            {{ dayjs(scope.row.collectionStart).format('YYYY-MM-DD') }} ~
            {{ dayjs(scope.row.collectionEnd).format('YYYY-MM-DD') }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="采集部门" align="center" prop="responsibleDeptName" />
      <el-table-column label="被考核单位" align="center" prop="templateType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TEMPLATE_TYPE" :value="scope.row.templateType" />
        </template>
      </el-table-column>
      <el-table-column label="描述" align="center" prop="description" />

      <el-table-column label="操作" align="center" width="250">
        <template #default="scope">
          <el-button link type="primary" @click="handleOperation('view', scope.row.id)">
            查看
          </el-button>
          <el-button link type="primary" @click="handleOperation('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="primary" @click="handleOperation('set', scope.row.id)">
            设置
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TemplateForm ref="formRef" @success="getList" :deptList="deptList" />

  <!-- 设置/查看 -->
  <ViewSet ref="viewRef" @success="getList" />
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { defaultProps, handleTree } from '@/utils/tree'
import download from '@/utils/download'
import { deleteTemplate, exportTemplate, getTemplatePage, TemplateVO } from '@/api/system/template'
import TemplateForm from './TemplateForm.vue'
import ViewSet from './viewSet.vue'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import * as DeptApi from '@/api/system/dept'

/** 指标管理 列表 */
defineOptions({ name: 'Template' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TemplateVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  templateName: undefined,
  classify: undefined,
  description: undefined,
  createTime: [],
  dateRange: [],
  responsibleDept: undefined,
  responsibleDeptName: undefined,
  collectionStart: undefined,
  collectionEnd: undefined,
  queryTime: undefined,
  templateType: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const deptList = ref<any>([]) // 树形结构

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getTemplatePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const viewRef = ref()
const handleOperation = (type: string, id?: number) => {
  if (type === 'create' || type === 'update') {
    formRef.value.open(type, id)
  } else {
    viewRef.value.open(type, id)
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await deleteTemplate(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await exportTemplate(queryParams)
    download.excel(data, '考核指标.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  getList()
  // 加载部门树
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
})
</script>
