<script lang="ts" setup>
import { Icon } from '@/components/Icon'
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'
import { getAccessToken, getRefreshToken } from '@/utils/auth'

defineOptions({ name: 'BackToFront' })

const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('screenfull')
const backUrl = import.meta.env.VITE_APP_FRONT_URL

defineProps({
  color: propTypes.string.def('')
})

const handleBack = () => {
  window.location.href = `${backUrl}?accessToken=${getAccessToken()}&refreshToken=${getRefreshToken()}`
}
</script>

<template>
  <div :class="prefixCls" @click="handleBack">
    <Icon :color="color" icon="ep:back" :size="18" />
  </div>
</template>
