<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form ref="queryFormRef" :inline="true" label-width="88px">
      <el-form-item label="年度" prop="year">
        <QuarterPicker
          ref="quarterPickerRef"
          v-model="quarter1"
          format="YYYY年第q季度"
          valueFormat="YYYY-Q"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="20" class="my-10px">
      <el-col :span="6">
        <el-card shadow="never">
          <template #header>
            <div class="h-3 flex font-bold justify-between"> 机关总部召开安委会会议情况 </div>
          </template>
          <Echart class="mx-auto" :height="295" :options="pieData" />
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card shadow="never">
          <template #header>
            <div class="h-3 flex font-bold justify-between"> 安全生产工作动态数据 </div>
          </template>
          <div class="list">
            <div class="flex gap-20px">
              <div class="w-30%" style="border: 1px solid #d7d7d7">
                <div style="border-bottom: 1px solid #d7d7d7; padding: 10px 20px">会议情况</div>
                <div class="flex text-center justify-around" style="padding: 15px 0">
                  <div style="border-right: 1px solid #d7d7d7; flex: 1">
                    <div>主要负责人</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{ workData.primaryMeetingCount || 0 }}</span
                      >起</div
                    >
                  </div>
                  <div style="flex: 1">
                    <div>分管负责人</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{
                        workData.responsibleMeetingCount || 0
                      }}</span
                      >起</div
                    >
                  </div>
                </div>
              </div>
              <div class="w-70%" style="border: 1px solid #d7d7d7">
                <div style="border-bottom: 1px solid #d7d7d7; padding: 10px 20px">检查情况</div>
                <div class="flex text-center justify-around" style="padding: 15px 0">
                  <div style="border-right: 1px solid #d7d7d7; flex: 1">
                    <div>主要负责人</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{ workData.primaryInspectCount || 0 }}</span
                      >家次</div
                    >
                  </div>
                  <div style="border-right: 1px solid #d7d7d7; flex: 1">
                    <div>分管负责人</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{
                        workData.responsibleInspectCount || 0
                      }}</span
                      >家次</div
                    >
                  </div>
                  <div style="border-right: 1px solid #d7d7d7; flex: 1">
                    <div>含基层</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{ workData.securityInspectCount || 0 }}</span
                      >家次</div
                    >
                  </div>
                  <div style="border-right: 1px solid #d7d7d7; flex: 1">
                    <div>发现隐患</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{ workData.hiddenDangerCount || 0 }}</span
                      >条</div
                    >
                  </div>
                  <div style="flex: 1">
                    <div>已整改隐患</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{
                        workData.rectifyHiddenDangerCount || 0
                      }}</span
                      >条</div
                    >
                  </div>
                </div>
              </div>
            </div>
            <div class="flex gap-20px mt-20px">
              <div class="w-50%" style="border: 1px solid #d7d7d7">
                <div style="border-bottom: 1px solid #d7d7d7; padding: 10px 20px">培训教育情况</div>
                <div class="flex text-center justify-around" style="padding: 15px 0">
                  <div style="border-right: 1px solid #d7d7d7; flex: 1">
                    <div>培训教育</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{ workData.trainCount || 0 }}</span
                      >场</div
                    >
                  </div>
                  <div style="border-right: 1px solid #d7d7d7; flex: 1">
                    <div>涵盖人员</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{ workData.includeCount || 0 }}</span
                      >人</div
                    >
                  </div>
                  <div style="flex: 1">
                    <div>累计学时</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{ workData.timeCount || 0 }}</span
                      >小时</div
                    >
                  </div>
                </div>
              </div>
              <div class="w-50%" style="border: 1px solid #d7d7d7">
                <div style="border-bottom: 1px solid #d7d7d7; padding: 10px 20px">事故情况</div>
                <div class="flex text-center justify-around" style="padding: 15px 0">
                  <div style="border-right: 1px solid #d7d7d7; flex: 1">
                    <div>生产安全事故</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{ workData.safetyAccidentCount || 0 }}</span
                      >起</div
                    >
                  </div>
                  <div style="border-right: 1px solid #d7d7d7; flex: 1">
                    <div>死亡</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{ workData.deathCount || 0 }}</span
                      >人</div
                    >
                  </div>
                  <div style="border-right: 1px solid #d7d7d7; flex: 1">
                    <div>重伤</div>
                    <div style="margin-top: 10px"
                      ><span style="font-size: 26px">{{ workData.injuryCount || 0 }}</span
                      >人</div
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <div>
            <div class="flex flex-wrap">
              <div v-for="(value, key) in showObj">
                <div>{{ key }}</div>
                <div class="flex justify-between">
                  <div v-for="item in value" class="flex items-center flex-col w-160px bgc">
                    <div>{{ item.label }}</div>
                    <div>{{ item.value }} {{ item.unit }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div> -->
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="my-10px">
      <el-col :span="24">
        <el-card shadow="never">
          <template #header>
            <div class="flex items-center">
              <div class="h-3 font-bold mr-50 mt--3"> 安全生产检查情况统计 </div>
              <QuarterPicker
                ref="quarterPicker1Ref"
                v-model="quarter2"
                format="YYYY年第q季度"
                valueFormat="YYYY-Q"
                @change="handleQuery1"
              />
            </div>
          </template>
          <Echart :options="barData" />
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card shadow="never">
          <template #header>
            <div class="h-3 flex font-bold"> 安全生产事故情况统计 </div>
          </template>
          <Echart :options="barData1" />
        </el-card>
      </el-col>
    </el-row>
  </ContentWrap>
</template>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { pieOptions, barOptions, bar1Options } from './echarts-data'
import { set } from 'lodash-es'
import {
  getMeetingData,
  getSafetyWorkData,
  getCheckYearData,
  getAccidentData
} from '@/api/statistics/yearWorkStatistics'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'

dayjs.extend(quarterOfYear)
defineOptions({ name: 'YearWorkStatistic' })

const loading = ref(true) // 列表的加载中
const pieData = ref<any>(pieOptions)
const barData = ref<any>(barOptions)
const barData1 = ref<any>(bar1Options)
let workData = ref<any>([])

let quarter1 = ref<any>(dayjs().format('YYYY-Q'))
let quarter2 = ref<any>(dayjs().format('YYYY-Q'))
const queryFormRef = ref() // 搜索的表单

onMounted(() => {
  getPieData()
  getWorkData()
  getBarData()
  getBar1Data()
})

const getWorkData = async () => {
  const data = await getSafetyWorkData(quarter1.value)
  console.log('data', data)

  workData.value = data ? data : []
}

const getPieData = async () => {
  loading.value = true
  try {
    const data = await getMeetingData({ year: quarter1.value })
    set(pieData.value, 'series[0].data', [
      { value: data.t1, name: '完成率' },
      { value: data.t3, name: '未完成率' }
    ])
  } finally {
    loading.value = false
  }
}

const getBarData = async () => {
  loading.value = true
  try {
    const data = await getCheckYearData(quarter2.value)
    set(
      barData.value,
      'xAxis[0].data',
      data.map((item) => {
        return item.t1
      })
    )
    set(barData.value, 'series', [
      {
        name: '党政主要负责人检查次数',
        type: 'bar',
        emphasis: {
          focus: 'series'
        },
        data: data.map((item) => {
          return item.t2.primaryInspectCount
        })
      },
      {
        name: '分管负责人检查次数',
        type: 'bar',
        emphasis: {
          focus: 'series'
        },
        data: data.map((item) => {
          return item.t2.responsibleInspectCount
        })
      },
      {
        name: '含所有在沪基层单位',
        type: 'bar',
        emphasis: {
          focus: 'series'
        },
        data: data.map((item) => {
          return item.t2.securityInspectCount
        })
      },
      {
        name: '发现隐患',
        type: 'bar',
        stack: 'danger',
        emphasis: {
          focus: 'series'
        },
        data: data.map((item) => {
          return item.t2.hiddenDangerCount
        })
      },
      {
        name: '已整改隐患',
        type: 'bar',
        stack: 'danger',
        emphasis: {
          focus: 'series'
        },
        data: data.map((item) => {
          return item.t2.rectifyHiddenDangerCount
        })
      }
    ])
  } finally {
    loading.value = false
  }
}

const getBar1Data = async () => {
  loading.value = true
  try {
    const data = await getAccidentData(quarter2.value)
    set(
      barData1.value,
      'xAxis[0].data',
      data.map((item) => {
        return item.t1
      })
    )
    set(barData1.value, 'series', [
      {
        name: '生产安全事故',
        type: 'bar',
        emphasis: {
          focus: 'series'
        },
        data: data.map((item) => {
          return item.t2.safetyAccidentCount
        })
      },
      {
        name: '造成死亡',
        type: 'bar',
        emphasis: {
          focus: 'series'
        },
        data: data.map((item) => {
          return item.t2.deathCount
        })
      },
      {
        name: '重伤',
        type: 'bar',
        emphasis: {
          focus: 'series'
        },
        data: data.map((item) => {
          return item.t2.injuryCount
        })
      }
    ])
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = async () => {
  getPieData()
  getWorkData()
}
/** 搜索按钮操作 */
const handleQuery1 = async () => {
  getBarData()
  getBar1Data()
}

/** 重置按钮操作 */
const resetQuery = () => {
  quarter1.value = dayjs().format('YYYY-Q')
  handleQuery()
}
</script>

<style lang="scss" scoped></style>
