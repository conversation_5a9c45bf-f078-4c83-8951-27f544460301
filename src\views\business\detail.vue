<!--
 * @Description: 集团管理详情页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-02 16:23:45
 * @LastEditTime: 2024-08-09 11:18:54
-->

<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="flag === 'detail' ? undefined : formRules"
    label-width="160px"
    v-loading="formLoading"
  >
    <div class="text-22px pl-35px pb-25px font-bold" v-if="!resultFlag && formData.companyName"
      >{{ flag === 'detail' ? formData.companyName + ' - ' : '' }}基本概况
    </div>
    <el-row :gutter="20">
      <el-col :span="10">
        <el-form-item label="企业名称" prop="companyName">
          <div v-if="flag === 'detail'">{{ formData.companyName }}</div>
          <el-input v-else v-model="formData.companyName" placeholder="请输入企业名称" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="统一社会信用代码" prop="creditCode">
          <div v-if="flag === 'detail'">{{ formData.creditCode }}</div>
          <el-input v-else v-model="formData.creditCode" placeholder="请输入统一社会信用代码" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="10">
        <el-form-item label="注册地址" prop="areaId">
          <div style="display: flex; width: 100%">
            <div v-if="flag === 'detail'" placeholder="请输入注册地址"
              >{{ formData.areaValue }}/</div
            >
            <el-cascader
              v-else
              v-model="formData.areaId"
              :props="defaultProps"
              :options="areaList"
              clearable
              placeholder="请选择注册地区"
            />
            <div v-if="flag === 'detail'">{{ formData.companyAddressDetail }}</div>
            <el-input v-else v-model="formData.companyAddressDetail" placeholder="请输入注册地址" />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="成立时间" prop="establishmentDate">
          <div v-if="flag === 'detail'">{{ formData.establishmentDateStr }}</div>
          <el-date-picker
            v-else
            v-model="formData.establishmentDate"
            type="date"
            value-format="x"
            placeholder="选择成立时间"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="10">
        <el-form-item label="经营地区" prop="runAreaId">
          <div style="display: flex; width: 100%">
            <div v-if="flag === 'detail'" placeholder="请输入经营地区"
              >{{ formData.areaValue }}/</div
            >
            <el-cascader
              v-else
              v-model="formData.runAreaId"
              :props="defaultProps"
              :options="areaList"
              clearable
              placeholder="请选择经营地区"
            />
            <div v-if="flag === 'detail'">{{ formData.runAddressDetail }}</div>
            <el-input v-else v-model="formData.runAddressDetail" placeholder="请输入经营地区" />
          </div>
        </el-form-item>
      </el-col>

      <el-col :span="10">
        <el-form-item label="联系电话" prop="legalRepresentativePhone">
          <div v-if="flag === 'detail'">{{ formData.legalRepresentativePhone }}</div>
          <el-input
            v-else
            v-model="formData.legalRepresentativePhone"
            placeholder="请输入法人联系电话"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="10">
        <el-form-item label="法人代表" prop="legalRepresentativeName">
          <div v-if="flag === 'detail'">{{ formData.legalRepresentativeName }}</div>
          <el-input
            v-else
            v-model="formData.legalRepresentativeName"
            placeholder="请输入法人代表"
          />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="负责人手机" prop="managerPhone">
          <div v-if="flag === 'detail'">{{ formData.managerPhone }}</div>
          <el-input v-else v-model="formData.managerPhone" placeholder="请输入负责人手机" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="10">
        <el-form-item label="负责人" prop="managerUser">
          <div v-if="flag === 'detail'">{{ formData.managerUser }}</div>
          <el-input v-else v-model="formData.managerUser" placeholder="请输入负责人" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="员工总数" prop="totalEmployees">
          <div v-if="flag === 'detail'">{{ formData.totalEmployees }}</div>
          <el-input
            v-else
            type="number"
            v-model="formData.totalEmployees"
            placeholder="请输入员工总数"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="10">
        <el-form-item label="国民经济行业类型" prop="industry">
          <div v-if="flag === 'detail'">{{ formData.industry }}</div>
          <el-cascader
            v-else
            v-model="formData.industry"
            :props="industryCascaderProps"
            class="!w-100%"
            filterable
          />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="国民经济类型" prop="nationalEconomyType">
          <div v-if="flag === 'detail'">
            {{ getDictLabel(DICT_TYPE.NATIONAL_ECONOMIC_TYPE, formData.nationalEconomyType) }}</div
          >
          <el-select
            v-else
            v-model="formData.nationalEconomyType"
            placeholder="请输入国民经济类型"
            clearable
            class="!w-100%"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.NATIONAL_ECONOMIC_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="10">
        <el-form-item label="专/兼职安全管理人员" prop="safetyManagementPersonnel">
          <div v-if="flag === 'detail'">{{ formData.safetyManagementPersonnel }}</div>
          <el-input
            v-else
            type="number"
            v-model="formData.safetyManagementPersonnel"
            placeholder="请输入专/兼职安全管理人员"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="特种作业人员数量" prop="specialOperationsPersonnel">
          <div v-if="flag === 'detail'">{{ formData.specialOperationsPersonnel }}</div>
          <el-input
            v-else
            type="number"
            v-model="formData.specialOperationsPersonnel"
            placeholder="请输入特种作业人员数量"
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="10">
        <el-form-item label="企业规模" prop="companyScale">
          <div v-if="flag === 'detail'">{{
            getDictLabel(DICT_TYPE.COMPANY_SIZE, formData.companyScale)
          }}</div>
          <el-radio-group v-else v-model="formData.companyScale">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.COMPANY_SIZE)"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
          <el-popover :width="0" placement="right" trigger="hover">
            <template #reference>
              <img style="position: absolute; right: 0" src="@/assets/imgs/question.png" alt="" />
            </template>
            <template #default>
              <img src="@/assets/imgs/size.png" alt="" />
            </template>
          </el-popover>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="安全投入费用" prop="safetyInvestment">
          <div v-if="flag === 'detail'">{{ formData.safetyInvestment }}万</div>
          <el-input v-else v-model="formData.safetyInvestment" placeholder="请输入安全投入费用" />
          <el-input
            type="number"
            v-else
            v-model="formData.safetyInvestment"
            placeholder="请输入安全投入费用"
          >
            <template #append>万</template>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="10">
        <el-form-item label="企业是否涉及" prop="involvement">
          <el-checkbox-group v-model="formData.involvement" style="width: 100%">
            <div
              v-for="dict in getIntDictOptions(DICT_TYPE.COMPANY_INVOLVED)"
              style="display: flex; justify-content: space-between; margin-bottom: 20px"
            >
              <el-checkbox
                style="width: 30%"
                :disabled="flag === 'detail'"
                :key="dict.value"
                :label="dict.value"
                @change="changeChoose($event, dict.value)"
              >
                {{ dict.label }}
              </el-checkbox>

              <div
                v-if="
                  formData.involvement &&
                  formData.involvement.length > 0 &&
                  formData.involvement.includes(dict.value)
                "
                style="width: 70%; display: flex"
              >
                <div
                  style="
                    font-size: 30px;
                    width: 40px;
                    margin-top: 13px;
                    color: #3594c5;
                    cursor: pointer;
                  "
                  v-if="flag !== 'detail'"
                  @click="addTable(dict.value)"
                  >+</div
                >
                <el-table :data="tableDataGroup[`tableData${dict.value}`]">
                  <el-table-column :label="getDictLabel(DICT_TYPE.COMPANY_INVOLVED, dict.value)">
                    <el-table-column label="名称" prop="relateName" />
                    <el-table-column label="使用情况" prop="relateUsedDetail" />
                    <el-table-column label="操作" v-if="flag !== 'detail'">
                      <template #default="scope">
                        <el-button type="primary" link @click="handleEdit(dict.value, scope.row)">
                          修改
                        </el-button>
                        <el-button link type="danger" @click="handleDelete(scope, dict.value)"
                          >删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
    <!-- <div class="tableList">
      <div v-for="item in formData.involvement">
        <el-button
          v-if="flag !== 'detail'"
          type="primary"
          style="margin-bottom: 10px"
          @click="addTable(item)"
          >新增</el-button
        >
        <el-table :data="tableDataGroup[`tableData${item}`]">
          <el-table-column :label="getDictLabel(DICT_TYPE.COMPANY_INVOLVED, item)">
            <el-table-column label="名称" prop="relateName" />
            <el-table-column label="使用情况" prop="relateUsedDetail" />
            <el-table-column label="操作" v-if="flag !== 'detail'">
              <template #default="scope">
                <el-button type="primary" link @click="handleEdit(item, scope.row)">
                  修改
                </el-button>
                <el-button link type="danger" @click="handleDelete(scope, item)">删除 </el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </div> -->
    <div v-if="!resultFlag" style="display: flex; justify-content: center; margin-top: 30px">
      <el-button @click="handleClose" :disabled="formLoading">关 闭</el-button>
      <el-button v-if="flag !== 'detail'" @click="saveForm" type="primary" :disabled="formLoading"
        >保存</el-button
      >
      <el-button v-if="flag !== 'detail'" @click="submitForm" type="primary" :disabled="formLoading"
        >提交</el-button
      >
    </div>
  </el-form>
  <!-- 添加或修改用户对话框 -->
  <TableForm ref="tableFormRef" @success="pushData" />
</template>
<script lang="ts" setup>
import TableForm from './TableForm.vue'
import { defaultProps, handleTree, nameValueProps } from '@/utils/tree'
import { DICT_TYPE, getIntDictOptions, getDictLabel } from '@/utils/dict'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { cloneDeep } from 'lodash-es'
import { CompanyRegisterApi } from '@/api/examineObj/business'
import * as AreaApi from '@/api/system/area'
import { getIndustryList, getNot0IndustryList } from '@/api/industry'
import { isArray } from '@/utils/is'

defineOptions({ name: 'BusinessDetail' })
const props = defineProps({
  deptId: {
    type: Number
  }
})

const industryCascaderProps = {
  ...nameValueProps,
  lazy: true,
  emitPath: false,
  checkStrictly: true,
  async lazyLoad(node: any, resolve) {
    const { level, data } = node
    const parentId = data?.id
    if (!parentId) {
      const parRes = await getIndustryList()
      resolve(parRes)
    } else {
      const res = await getNot0IndustryList({ parentId: data.id })
      const nodes = res.map((item) => ({
        ...item,
        leaf: level >= 3
      }))
      resolve(nodes)
    }
  }
}
const tableDataGroup = ref({
  tableData1: [],
  tableData2: [],
  tableData3: [],
  tableData4: []
})
const areaList = ref([])
const resultFlag = inject('resultFlag')
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const route: any = useRoute()
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  account: undefined,
  companyName: undefined,
  creditCode: undefined,
  companyAddressDetail: undefined,
  establishmentDate: undefined,
  legalRepresentativeName: undefined,
  legalRepresentativePhone: undefined,
  managerUser: undefined,
  managerPhone: undefined,
  totalEmployees: undefined,
  industry: undefined,
  nationalEconomyType: undefined,
  safetyManagementPersonnel: undefined,
  safetyInvestment: undefined,
  companyScale: undefined,
  specialOperationsPersonnel: undefined,
  involvement: undefined,
  majorHazardSources: undefined,
  hazardousMaterialUsage: undefined,
  remarks: undefined,
  areaId: undefined,
  runAreaId: undefined,
  runAddressDetail: undefined,
  companyRelateInfoList: undefined
})
const flag = ref('add')
const formRules = reactive({
  companyName: [{ required: true, message: '企业名称不能为空', trigger: 'blur' }],
  creditCode: [{ required: true, message: '统一社会信用代码不能为空', trigger: 'blur' }],
  areaId: [{ required: true, message: '注册地址不能为空', trigger: 'blur' }],
  companyAddressDetail: [{ required: true, message: '注册地址不能为空', trigger: 'blur' }],
  runAreaId: [{ required: true, message: '经营地址不能为空', trigger: 'blur' }],
  runAddressDetail: [{ required: true, message: '经营地址不能为空', trigger: 'blur' }],
  establishmentDate: [{ required: true, message: '成立时间不能为空', trigger: 'blur' }],
  legalRepresentativeName: [{ required: true, message: '法人代表不能为空', trigger: 'blur' }],
  legalRepresentativePhone: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }],
  managerUser: [{ required: true, message: '负责人不能为空', trigger: 'blur' }],
  managerPhone: [{ required: true, message: '负责人手机不能为空', trigger: 'blur' }],
  totalEmployees: [{ required: true, message: '员工总数不能为空', trigger: 'blur' }],
  industry: [{ required: true, message: '国民经济行业类型不能为空', trigger: 'blur' }],
  nationalEconomyType: [{ required: true, message: '国民经济类型不能为空', trigger: 'blur' }],
  safetyManagementPersonnel: [
    { required: true, message: '专/兼职安全管理人员不能为空', trigger: 'blur' }
  ],
  safetyInvestment: [{ required: true, message: '安全投入费用不能为空', trigger: 'blur' }],
  companyScale: [{ required: true, message: '企业规模不能为空', trigger: 'blur' }],
  specialOperationsPersonnel: [
    { required: true, message: '特种作业人员数量不能为空', trigger: 'blur' }
  ],
  // involvement: [{ required: true, message: '企业是否涉及不能为空', trigger: 'blur' }],
  majorHazardSources: [{ required: true, message: '是否有重大危险源不能为空', trigger: 'blur' }],
  hazardousMaterialUsage: [{ required: true, message: '危化品使用量不能为空', trigger: 'blur' }],
  account: [{ required: true, message: '账号不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const tableFormRef = ref()

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const changeChoose = (e, val) => {
  if (!e) {
    tableDataGroup.value[`tableData${val}`] = []
  }
}

const addTable = (item) => {
  tableFormRef.value.open('add', item)
}

const handleEdit = (item, row) => {
  tableFormRef.value.open('edit', item, row)
}

const handleDelete = async (scope, item) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    tableDataGroup.value[`tableData${item}`].splice(scope.$index, 1)
    message.success(t('common.delSuccess'))
  } catch {}
}

const pushData = (data, type) => {
  if (type === 'add') tableDataGroup.value[`tableData${data.relateType}`].push(data)
}

const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const submitData = cloneDeep(formData.value)
    submitData.involvement = isArray(submitData.involvement)
      ? submitData.involvement?.join('/')
      : submitData.involvement
    submitData.areaId = isArray(submitData.areaId)
      ? submitData.areaId?.join('/')
      : submitData.areaId
    submitData.runAreaId = isArray(submitData.runAreaId)
      ? submitData.runAreaId?.join('/')
      : submitData.runAreaId
    submitData.accountStatus = 1
    submitData.companyRelateInfoList = [
      ...tableDataGroup.value.tableData1,
      ...tableDataGroup.value.tableData2,
      ...tableDataGroup.value.tableData3,
      ...tableDataGroup.value.tableData4
    ]
    await CompanyRegisterApi.updateCompanyRegister(submitData)
    message.success(t('common.updateSuccess'))
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

//保存
const saveForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const submitData = cloneDeep(formData.value)
    submitData.involvement = isArray(submitData.involvement)
      ? submitData.involvement?.join('/')
      : submitData.involvement
    submitData.areaId = isArray(submitData.areaId)
      ? submitData.areaId?.join('/')
      : submitData.areaId
    submitData.runAreaId = isArray(submitData.runAreaId)
      ? submitData.runAreaId?.join('/')
      : submitData.runAreaId
    submitData.accountStatus = 0
    submitData.companyRelateInfoList = [
      ...tableDataGroup.value.tableData1,
      ...tableDataGroup.value.tableData2,
      ...tableDataGroup.value.tableData3,
      ...tableDataGroup.value.tableData4
    ]
    await CompanyRegisterApi.updateCompanyRegister(submitData)
    message.success(t('common.updateSuccess'))
    // 关闭当前 Tab
    handleClose()
  } finally {
    formLoading.value = false
  }
}

const handleClose = () => {
  delView(unref(currentRoute))
  push(route.query.backPath || '/examineObj/business')
}

const fetchDetailData = async () => {
  formLoading.value = true
  try {
    if (resultFlag || route.query.deptId) {
      flag.value = 'detail'
      if (!props.deptId && !route.query.deptId) return
      const res = await CompanyRegisterApi.getCompanyRegisterPage({
        deptId: props.deptId || route.query.deptId
      })
      formData.value = res.list?.[0] || {
        id: undefined,
        account: undefined,
        companyName: undefined,
        creditCode: undefined,
        companyAddressDetail: undefined,
        establishmentDate: undefined,
        legalRepresentativeName: undefined,
        legalRepresentativePhone: undefined,
        managerUser: undefined,
        managerPhone: undefined,
        totalEmployees: undefined,
        industry: undefined,
        nationalEconomyType: undefined,
        safetyManagementPersonnel: undefined,
        safetyInvestment: undefined,
        companyScale: undefined,
        specialOperationsPersonnel: undefined,
        involvement: undefined,
        majorHazardSources: undefined,
        hazardousMaterialUsage: undefined,
        remarks: undefined,
        areaId: undefined,
        runAreaId: undefined,
        runAddressDetail: undefined,
        companyRelateInfoList: undefined
      }
    } else {
      formData.value = await CompanyRegisterApi.getCompanyRegister(route.query.id)
    }
    formData.value.companyRelateInfoList.forEach((item) => {
      const relateType = item.relateType
      // 将 relateType 转换为合适的键名
      const key = `tableData${relateType}`
      // 检查键是否存在于 tableDataGroup 中
      if (tableDataGroup.value.hasOwnProperty(key)) {
        // 添加数据到对应的数组
        tableDataGroup.value[key].push(item)
      }
    })
    if (formData.value.companyScale) formData.value.companyScale = formData.value.companyScale * 1
    formData.value.areaValue = formData.value.areaValue?.replace(/^上海市\//, '')
    formData.value.involvement = formData.value.involvement
      ? formData.value.involvement.split('/').map(Number)
      : []
    formData.value.areaId = formData.value.areaId
      ? formData.value.areaId.split('/').map(Number)
      : []
    formData.value.runAreaId = formData.value.runAreaId
      ? formData.value.runAreaId.split('/').map(Number)
      : []
  } finally {
    formLoading.value = false
  }
}

onMounted(async () => {
  formLoading.value = true
  flag.value = route.query.type
  if (flag.value !== 'detail') {
    const res = await AreaApi.getAreaTree()
    areaList.value = res[8].children
  }

  if (route.query.id || resultFlag || route.query.deptId) {
    await fetchDetailData()
  }
})

defineExpose({ fetchDetailData })
</script>

<style scoped lang="scss">
.tableList {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}
</style>
