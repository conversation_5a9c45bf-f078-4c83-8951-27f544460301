<!--
 * @Description: specialWork
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-31 13:55:52
 * @LastEditTime: 2024-08-08 10:28:38
-->

<template>
  <section class="index-container">
    <component
      :is="currentView"
      :row="row"
      @updateCurrentView="updateCurrentView"
      :currentTabId="currentTabId"
    ></component>
  </section>
</template>

<script lang="ts" setup>
import YearExamineList from './YearExamineList.vue'
import Detail from './detail.vue'

const props = defineProps({
  currentTabId: {
    type: String,
    default: ''
  }
})
defineOptions({ name: 'SpecialWorkIndex' })
const row = ref({})
const currentView = shallowRef<any>(YearExamineList)

const updateCurrentView = (view, item) => {
  row.value = item
  if (view === 'Detail') {
    currentView.value = Detail
  } else if (view === 'YearExamineList') {
    currentView.value = YearExamineList
  }
}
</script>
