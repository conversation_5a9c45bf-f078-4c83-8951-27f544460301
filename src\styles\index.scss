@import './var.css';
@import 'element-plus/theme-chalk/dark/css-vars.css';

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
      0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}

/* 列表链接跳转文字样式 */
.listLink {
  font-weight: 500;
  color: #1890ff;
  cursor: pointer;
}

.icon-pdf {
  font-size: 24px;
  cursor: pointer;
  color: #f05542;
}

.one-cut-txt {
  /*强制文字在一行文本框内*/
  white-space: nowrap;
  /*溢出部分文字隐藏*/
  overflow: hidden;
  /*溢出部分省略号处理*/
  text-overflow: ellipsis;
}
