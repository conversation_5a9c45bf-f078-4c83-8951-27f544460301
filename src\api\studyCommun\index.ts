/*
 * @Description: 交流学习相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-30 11:35:29
 * @LastEditTime: 2024-07-30 11:49:33
 */
import request from '@/config/axios'

// 交流学习 VO
export interface LearningVO {
  id: number // 公告ID
  title: string // 公告标题
  userId: number // 用户ID
  contentType: number // 内容形式
  exchangeType: number // 交流类型  0-时政  1-热点
  uploadUnit: string // 上传单位
  uploadTime: Date // 上传时间
  publishTime: Date // 发布时间
  content: string // 公告内容
  publishStatus: number // 发布状态（0未发布 1已发布）
  attachments: string // 附件
  reviewResult: number // 审核状态 （0通过 1拒绝）
  reviewOpinions: string // 审核意见
}

// 交流学习 API
export const LearningApi = {
  // 查询交流学习分页
  getLearningPage: async (params: any) => {
    return await request.get({ url: `/system/learning/page`, params })
  },

  // 查询交流学习详情
  getLearning: async (id: number) => {
    return await request.get({ url: `/system/learning/get?id=` + id })
  },

  // 新增交流学习
  createLearning: async (data: LearningVO) => {
    return await request.post({ url: `/system/learning/create`, data })
  },

  // 修改交流学习
  updateLearning: async (data: any) => {
    return await request.put({ url: `/system/learning/update`, data })
  },

  // 删除交流学习
  deleteLearning: async (id: number) => {
    return await request.delete({ url: `/system/learning/delete?id=` + id })
  },

  // 导出交流学习 Excel
  exportLearning: async (params) => {
    return await request.download({ url: `/system/learning/export-excel`, params })
  }
}
