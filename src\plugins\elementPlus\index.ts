/*
 * @Description: Element-plus
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-11-28 16:13:46
 * @LastEditTime: 2024-08-27 14:48:15
 */
import type { App } from 'vue'
// 需要全局引入一些组件，如ElScrollbar，不然一些下拉项样式有问题
import { ElLoading, ElScrollbar, ElButton, ElTable, ElTableColumn, ElDialog } from 'element-plus'

const plugins = [ElLoading]

const components = [ElScrollbar, ElButton]

export const setupElementPlus = (app: App<Element>) => {
  plugins.forEach((plugin) => {
    app.use(plugin)
  })

  components.forEach((component: any) => {
    app.component(component.name, component)
  })

  // 全局修改element-plus的样式
  // 1.获取props
  const TableProps = ElTable.props
  const TableColumnProps = ElTableColumn.props
  const DialogProps = ElDialog.props
  // 2.修改默认props
  // 2.1.全局el-table设置
  TableProps.border = { type: Boolean, default: true } // 边框线
  TableProps.headerCellStyle = {
    type: Object,
    default: { fontSize: '16px', backgroundColor: '#F8F8F8', color: '#333' }
  }
  // 2.2.全局el-table-column设置
  TableColumnProps.align = { type: String, default: 'center' } // 居中
  TableColumnProps.showOverflowTooltip = { type: Boolean, default: false } // 文本溢出
  // 2.3.全局el-dialog设置
  DialogProps.closeOnClickModal.default = false
  DialogProps.center.default = true
}
