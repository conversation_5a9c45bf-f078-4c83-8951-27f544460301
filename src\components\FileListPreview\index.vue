<!--
 * @Description: 详情页文件列表预览组件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-10 15:16:21
 * @LastEditTime: 2024-09-12 10:56:22
-->
<template>
  <div class="flex items-start" v-if="props.fileUrl">
    <el-tooltip v-for="(item, index) in fileList" :content="item.split('/').pop()" placement="top">
      <el-link :key="index" type="primary" :underline="false" @click="handlePreview(item)">
        <Icon :size="22" icon="ep:document" />
      </el-link>
    </el-tooltip>
    <!-- 文件预览 -->
    <PreviewDialog ref="previewDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { isArray } from '@/utils/is'

const props = defineProps({
  fileUrl: {
    type: [String, Array],
    required: true
  }
})
const fileList = ref<any>([])
const previewDialogRef = ref()
const handlePreview = (url) => {
  previewDialogRef.value.fileLoad(url)
}

watch(
  () => props.fileUrl,
  (val) => {
    if (!val) return
    if (isArray(val)) {
      fileList.value = val
    } else {
      fileList.value = val.split(',')
    }
  },
  {
    immediate: true
  }
)
</script>
