<!--
 * @Description: 专项工作列表页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-31 13:55:52
 * @LastEditTime: 2025-07-22 09:10:46
-->

<template>
  <ContentWrap>
    <div v-if="!resultFlag" class="text-20px font-bold mb-20px">年度考评</div>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" align="center" type="index" width="60" />
      <el-table-column label="年度工作名称" align="center" prop="workName" />
      <el-table-column
        v-if="route.path === '/examineProcess/yearExamine'"
        label="被考核单位"
        align="center"
        prop="refUnitName"
      />
      <!-- <el-table-column
        label="下发时间"
        align="center"
        prop="issuanceTime"
        width="250"
        :formatter="dateFormatt<PERSON>"
      />
      <el-table-column
        label="填写时间"
        align="center"
        prop="updateTime"
        width="250"
        :formatter="dateFormatter"
      />
      <el-table-column label="填写账号" align="center" prop="updater" width="150" /> -->
      <el-table-column label="时间" align="center" prop="assessmentYear" width="180">
        <template #default="scope"> {{ scope.row.assessmentYear }}年 </template>
      </el-table-column>
      <el-table-column
        v-if="route.path === '/examineTask/yearExamine'"
        label="状态"
        align="center"
        prop="status"
        width="120px"
      >
        <template #default="scope">
          <dict-tag
            :type="DICT_TYPE.REPORT_STATUS"
            :value="
              route.path === '/examineTask/yearExamine'
                ? scope.row.unitSubmitStatus
                : scope.row.status
            "
          />
        </template>
      </el-table-column>
      <el-table-column v-if="resultFlag" label="分值" align="center" prop="assessmentScore" />
      <el-table-column label="操作" align="center" width="180">
        <template #default="scope">
          <template v-if="!resultFlag">
            <template v-if="route.path === '/examineProcess/yearExamine' && scope.row.status === 0">
              <el-button link type="warning" @click="handleSubmit(scope.row.id)"> 提交 </el-button>
              <el-button link type="primary" @click="handleEditDetail('edit', scope.row)">
                考核
              </el-button>
            </template>
            <template
              v-if="route.path === '/examineTask/yearExamine' && scope.row.unitSubmitStatus === 0"
            >
              <el-button link type="warning" @click="handleSubmit(scope.row.id)"> 提交 </el-button>
              <el-button link type="primary" @click="handleEditDetail('edit', scope.row)">
                自评
              </el-button>
            </template>
          </template>
          <template v-else>
            <el-button
              v-if="!scope.row.assessmentScore"
              link
              type="primary"
              @click="handleExamine(scope.row)"
            >
              考核
            </el-button>
          </template>
          <el-button link type="primary" @click="handleEditDetail('detail', scope.row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <!-- <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    /> -->

    <ExamineDialog ref="examineDialogRef" @fetch-data="getList" :currentTabId="currentTabId" />
  </ContentWrap>
</template>

<script setup lang="ts">
import ExamineDialog from '../examineDialog.vue'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { QuarterExamineApi } from '@/api/examineTask/quarterExamine'
import { SchemeApi } from '@/api/template/examinePlan'
import { uniqBy } from 'lodash-es'

defineOptions({ name: 'YearExamineList' })
const props = defineProps({
  currentTabId: {
    type: String,
    default: ''
  }
})
const resultFlag = inject('resultFlag', false)
const emit = defineEmits(['updateCurrentView'])
const message = useMessage() // 消息弹窗
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list = ref<any>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams: any = reactive({
  pageNo: 1,
  pageSize: -1,
  assessmentYear: undefined,
  templateId: undefined,
  quarter: undefined,
  assessmentUnit: undefined,
  workName: undefined,
  submitTime: [],
  status: undefined,
  createTime: []
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const sessionObj = JSON.parse(sessionStorage.getItem('examineResult') as any)
    const queryData = {
      ...queryParams,
      unitId: resultFlag ? sessionObj.unitId : undefined,
      year: resultFlag ? sessionObj.examineYear : undefined,
      status: resultFlag ? 1 : queryParams.status,
      workType: 3
    }
    const data = await QuarterExamineApi.getWorkPage(queryData)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 新增
const handleEditDetail = (type, row) => {
  emit('updateCurrentView', 'Detail', {
    ...row,
    type,
    deptList: createDeptList(row.refIds, row.refUnitName)
  })
}

const examineDialogRef = ref()
const handleExamine = (row) => {
  examineDialogRef.value.openDialog(row)
}

const handleSubmit = async (id) => {
  // 提交的二次确认
  await message.confirm('提交后不能修改数据，是否确认提交？')
  await QuarterExamineApi.updateWork({ id, status: 1 })
  message.success('操作成功')
  // 刷新列表
  await getList()
}

const createDeptList = (refIds, refUnitName) => {
  // 将字符串分割成数组
  if (!refIds || !refUnitName) return []
  const ids = refIds?.split(',')?.map(Number) // 转换为数字数组
  const names = refUnitName?.split(',')

  // 长度不一致 无法对应
  if (ids.length !== names.length) {
    return []
  }

  // 使用map方法将两个数组合并为一个对象数组
  const deptList = ids.map((id, index) => ({
    id: id,
    name: names[index]
  }))

  return deptList
}
const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(async () => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-link) {
  width: 100%;
  text-align: center;
}
</style>
