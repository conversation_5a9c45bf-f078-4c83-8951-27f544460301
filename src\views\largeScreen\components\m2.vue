<!--
 * @Description: 考核过程预警
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-09-05 10:03:54
 * @LastEditTime: 2024-09-06 09:05:21
-->

<template>
  <div class="con">
    <ul class="rank">
      <li>
        <div class="title">考核过程预警</div>
        <ul class="tab">
          <li
            v-for="item in typeList"
            :class="[item.id == active ? 'active' : '']"
            @click="handleChange(item.id)"
          >
            {{ item.name }}
          </li>
        </ul>
        <div class="echarts-con">
          <Echarts :option="option" />
          <div class="position-absolute right-180px top-55px leading-35px font-bold">
            <div v-for="item in radarData" class="flex">
              <div class="w-120px">{{ item.name }} </div>
               {{ item.value }}%
            </div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>
<script setup lang="ts">
import { graphic } from 'echarts'
const option = ref({})

const typeList = ref([
  { id: 1, name: '各区' },
  { id: 2, name: '成员单位' },
  { id: 3, name: '特定功能区' }
])
const radarData = [
  { name: '责任落实', value: 82 },
  { name: '事故预防', value: 79 },
  { name: '城市运行管理', value: 62 },
  { name: '特色工作', value: 85 },
  { name: '治本攻坚', value: 75 },
  { name: '灾害防治', value: 78 }
]
const active = ref(1)
const handleChange = (id) => {
  active.value = id
}
onMounted(() => {
  option.value = {
    radar: {
      startAngle: 120,
      indicator: [
        { text: radarData[0].name, max: 100 },
        { text: radarData[1].name, max: 100 },
        { text: radarData[2].name, max: 100 },
        { text: radarData[3].name, max: 100 },
        { text: radarData[4].name, max: 100 },
        { text: radarData[5].name, max: 100 }
      ],
      center: ['30%', '50%'],
      radius: '80%',
      axisName: {
        color: '#fff'
      },

      splitArea: {
        areaStyle: {
          color: ['#64AFE9', '#428BD4'],
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          shadowBlur: 2
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(211, 253, 250, 0.1)'
        }
      }
    },
    series: [
      {
        name: '考核过程预警',
        type: 'radar',
        data: [
          {
            value: radarData.map((item) => item.value),
            label: {
              show: true,
              formatter: function (params) {
                return params.value
              },
              color: '#fff'
            },
            areaStyle: {
              color: new graphic.RadialGradient(0.1, 0.6, 1, [
                {
                  color: 'rgba(255, 145, 124, 0.5)',
                  offset: 0
                },
                {
                  color: 'rgba(255, 145, 124, 0.9)',
                  offset: 1
                }
              ])
            }
          }
        ]
      }
    ]
  }
})
</script>

<style scoped lang="scss">
.con {
  position: relative;
  height: 100%;
  color: #fff;
  /* background: #071b3d; */
  background: linear-gradient(#3264a4, #051430 40%, #051430 60%);
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #4189f0;
  .title {
    font-size: 15px;
    font-weight: bold;
  }
  .tab {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 10px;
    display: flex;

    cursor: pointer;
    border: 1px solid #4189f0;
    border-radius: 4px;
    > li {
      width: 70px;
      font-size: 12px;
      text-align: center;
      line-height: 26px;

      color: #767e8e;
      font-weight: bold;
    }
    > li:first-child,
    li:nth-child(2) {
      border-right: 1px solid #4189f0;
    }
    .active {
      color: #fff !important;
      background: transparent !important;
    }
  }
  .rank {
    height: 100%;
    display: flex;
    > li {
      flex: 1;
      padding: 0 4px;
      height: 100%;
      .echarts-con {
        height: calc(100% - 24px);
        padding: 4px;
        position: relative;
      }
    }
  }
}
</style>
