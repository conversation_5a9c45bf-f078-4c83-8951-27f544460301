import { EChartsOption } from 'echarts'
import 'echarts/lib/component/dataZoom'

const { t } = useI18n()

export const pieOptions: EChartsOption = {
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c}家 ({d}%)'
  },
  color: ['#0096fa', '#e9e9e9'],
  legend: {
    orient: 'vertical',
    left: 'left',
    data: ['完成率', '未完成率']
  },
  series: [
    {
      name: '考核完成率',
      type: 'pie',
      radius: '55%',
      center: ['50%', '60%'],
      data: [
        { value: 335, name: '完成率', label: { position: 'outside' } },
        { value: 310, name: '未完成率', label: { position: 'outside' } }
      ]
    }
  ]
}

export const radarOption: EChartsOption = {
  radar: {
    // shape: 'circle',
    indicator: [
      { name: '季报', max: 100 },
      { name: '半年报', max: 100 },
      { name: '基础概况', max: 100 },
      { name: '专项工作', max: 100 },
      { name: '年报', max: 100 }
    ]
  },
  series: [
    {
      name: `xxx${t('workplace.index')}`,
      type: 'radar',
      data: [
        {
          value: [42, 30, 20, 35, 80]
        }
      ],
      label: {
        show: true,
        formatter: function (params) {
          return params.value + '%'
        }
      }
    }
  ]
}

export const barOptions: EChartsOption = {
  dataZoom: [
    {
      // 设置滚动条的隐藏与显示
      show: true,
      // 设置滚动条类型
      type: 'slider',
      // 是否显示detail，即拖拽时候显示详细数值信息
      showDetail: false,
      // 数据窗口范围的起始数值
      startValue: 0,
      // 数据窗口范围的结束数值（一页显示多少条数据）
      endValue: 10,
      // empty：当前数据窗口外的数据，被设置为空。
      // 即不会影响其他轴的数据范围
      filterMode: 'empty',
      // 设置滚动条宽度，相对于盒子宽度
      width: '50%',
      // 设置滚动条高度
      height: 8,
      // 设置滚动条显示位置
      left: 'center',
      // 控制手柄的尺寸
      handleSize: 0,
      // dataZoom-slider组件离容器下侧的距离
      bottom: 3
    },
    {
      // 没有下面这块的话，只能拖动滚动条，
      // 鼠标滚轮在区域内不能控制外部滚动条
      type: 'inside',
      // 控制哪个轴，如果是number表示控制一个轴，
      // 如果是Array表示控制多个轴。此处控制第二根轴
      yAxisIndex: [0, 1],
      // 滚轮是否触发缩放
      zoomOnMouseWheel: false,
      // 鼠标移动能否触发平移
      moveOnMouseMove: true,
      // 鼠标滚轮能否触发平移
      moveOnMouseWheel: true
    }
  ],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {},
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: []
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: []
}

export const bar1Options: EChartsOption = {
  dataZoom: [
    {
      // 设置滚动条的隐藏与显示
      show: true,
      // 设置滚动条类型
      type: 'slider',
      // 是否显示detail，即拖拽时候显示详细数值信息
      showDetail: false,
      // 数据窗口范围的起始数值
      startValue: 0,
      // 数据窗口范围的结束数值（一页显示多少条数据）
      endValue: 10,
      // empty：当前数据窗口外的数据，被设置为空。
      // 即不会影响其他轴的数据范围
      filterMode: 'empty',
      // 设置滚动条宽度，相对于盒子宽度
      width: '50%',
      // 设置滚动条高度
      height: 8,
      // 设置滚动条显示位置
      left: 'center',
      // 控制手柄的尺寸
      handleSize: 0,
      // dataZoom-slider组件离容器下侧的距离
      bottom: 3
    },
    {
      // 没有下面这块的话，只能拖动滚动条，
      // 鼠标滚轮在区域内不能控制外部滚动条
      type: 'inside',
      // 控制哪个轴，如果是number表示控制一个轴，
      // 如果是Array表示控制多个轴。此处控制第二根轴
      yAxisIndex: [0, 1],
      // 滚轮是否触发缩放
      zoomOnMouseWheel: false,
      // 鼠标移动能否触发平移
      moveOnMouseMove: true,
      // 鼠标滚轮能否触发平移
      moveOnMouseWheel: true
    }
  ],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {},
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: []
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: []
}
