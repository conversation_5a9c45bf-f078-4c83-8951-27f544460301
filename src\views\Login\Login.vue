<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-04 17:07:27
 * @LastEditTime: 2025-07-22 10:44:45
-->
<template>
  <div class="bg">
    <el-form
      ref="formLogin"
      :model="loginData.loginForm"
      :rules="LoginRules"
      class="login-form"
      label-position="top"
      label-width="120px"
      size="large"
    >
      <el-row style="margin-right: -10px; margin-left: -10px">
        <div class="title">
          <p>{{ title }}</p>
          <p style="font-size: 20px; color: #333">{{ loginName }}</p>
        </div>
        <div class="w-100% px-50px">
          <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
            <el-form-item prop="username">
              <el-input
                v-model="loginData.loginForm.username"
                :placeholder="t('login.usernamePlaceholder')"
                :prefix-icon="iconAvatar"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
            <el-form-item prop="password">
              <el-input
                v-model="loginData.loginForm.password"
                :placeholder="t('login.passwordPlaceholder')"
                :prefix-icon="iconLock"
                show-password
                @keyup.enter="getCode"
                type="password"
              />
            </el-form-item>
          </el-col>
          <el-col
            :span="24"
            style="padding-right: 10px; padding-left: 10px; margin-top: -10px; margin-bottom: -20px"
          >
            <el-form-item>
              <el-row justify="space-between" style="width: 100%">
                <el-col :span="6">
                  <el-checkbox class="custom-checkbox" v-model="loginData.loginForm.rememberMe">
                    {{ t('login.remember') }}
                  </el-checkbox>
                </el-col>
                <el-col v-if="type && type == '1'" :offset="6" :span="12">
                  <span
                    style="float: right; color: var(--el-color-primary); cursor: pointer"
                    @click="handleRegister"
                    >新用户注册</span
                  >
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="padding-right: 10px; padding-left: 10px">
            <el-form-item>
              <XButton
                :loading="loginLoading"
                :title="t('login.login')"
                class="w-[100%]"
                style="background: var(--el-color-primary); color: #fff"
                @click="getCode"
              />
            </el-form-item>
          </el-col>
        </div>
      </el-row>
    </el-form>
    <Verify
      ref="verify"
      :captchaType="captchaType"
      :imgSize="{ width: '400px', height: '200px' }"
      mode="pop"
      @success="handleLogin"
    />
    <YearSelectionDialog ref="yearSelectionDialog" />
  </div>
</template>
<script lang="ts" setup>
import * as LoginApi from '@/api/login'
import * as authUtil from '@/utils/auth'
import { useFormValid } from './useLogin'
import { useIcon } from '@/hooks/web/useIcon'
import YearSelectionDialog from '@/components/YearSelectionDialog/index.vue'
import { ElLoading } from 'element-plus'
import { encrypt } from '@/utils/jsencrypt'

const title = import.meta.env.VITE_APP_TITLE
const LoginRules = {
  username: [required],
  password: [required],
  tenantName: [required]
}
const verify = ref()
const captchaType = ref('blockPuzzle') // blockPuzzle 滑块 clickWord 点击文字
const loginData: any = reactive({
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    username: '',
    password: '',
    rememberMe: false,
    tenantName: '上海柏科'
  }
})
const { query } = useRoute()
const type = query?.type
const loginName = query.name
const { t } = useI18n()
const message = useMessage()
const iconAvatar = useIcon({ icon: 'ep:avatar' })
const iconLock = useIcon({ icon: 'ep:lock' })
const loginLoading = ref(false)
const loading = ref()
const { push } = useRouter()
const redirectUrl = ref('')
const formLogin = ref()
const yearSelectionDialog = ref()
// 记住我
const getCookie = () => {
  const loginForm = authUtil.getLoginForm()
  if (loginForm) {
    loginData.loginForm = {
      ...loginData.loginForm,
      username: loginForm.username ? loginForm.username : loginData.loginForm.username,
      password: loginForm.password ? loginForm.password : loginData.loginForm.password,
      rememberMe: loginForm.rememberMe ? true : false,
      tenantName: loginForm.tenantName ? loginForm.tenantName : loginData.loginForm.tenantName
    }
  }
}

// 根据域名，获得租户信息
const getTenantByWebsite = async () => {
  const website = location.host
  const res = await LoginApi.getTenantByWebsite(website)
  if (res) {
    loginData.loginForm.tenantName = res.name
    authUtil.setTenantId(res.id)
  }
}
// 新用户注册
const handleRegister = () => {
  push('/register')
}
// 获取验证码
const getCode = async () => {
  // 情况一，未开启：则直接登录
  if (loginData.captchaEnable === 'false') {
    await handleLogin({})
  } else {
    // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
    // 弹出验证码
    verify.value.show()
  }
}
// 获取租户 ID
const getTenantId = async () => {
  if (loginData.tenantEnable === 'true') {
    const res = await LoginApi.getTenantIdByName(loginData.loginForm.tenantName)
    authUtil.setTenantId(res)
  }
}
const { validForm } = useFormValid(formLogin)
// 登录
const handleLogin = async (params) => {
  loginLoading.value = true
  try {
    await getTenantId()
    const data = await validForm()
    if (!data) return
    const loginDataLoginForm = {
      ...loginData.loginForm,
      password: encrypt(loginData.loginForm.password)
    }
    loginDataLoginForm.captchaVerification = params.captchaVerification
    const res = await LoginApi.login(loginDataLoginForm)
    if (!res) {
      return
    }
    authUtil.setToken(res)
    if (loginData.loginForm.rememberMe) {
      authUtil.setLoginForm(loginData.loginForm)
    } else {
      authUtil.removeLoginForm()
    }
    yearSelectionDialog.value.openDialog()
  } finally {
    loginLoading.value = false
  }
}

onMounted(() => {
  if (window.location.href.indexOf('redirect') >= 0) {
    let queryString = window.location.href.split('?')[1]
    let urlParams = new URLSearchParams(queryString)
    let redirect = urlParams.get('redirect')!
    redirectUrl.value = redirect
  }
  // 将账户类型传递过来
  loginData.loginForm.userParty = type
  getCookie()
  getTenantByWebsite()
})
</script>

<style lang="scss" scoped>
.bg {
  width: 100vw;
  height: 100vh;
  background-image: url('@/assets/imgs/login-bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.login-form {
  background: #fff;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px 20px 10px 20px;
  width: 480px;
  border-radius: 10px;
  overflow: hidden;
}
.title {
  font-size: 26px;
  color: var(--el-color-primary);
  line-height: 40px;
  text-align: center;
  width: 100%;
  font-weight: bold;
  padding-bottom: 30px;
}
:deep(.custom-checkbox) {
  .el-checkbox__input .el-checkbox__inner {
    border-color: var(--el-color-primary);
    /*background-color: var(--el-color-primary);  */
  }
}

:deep(.custom-checkbox) {
  .el-checkbox__input .el-checkbox__inner:hover {
    border-color: var(--el-color-primary); /* 鼠标悬停时的边框颜色 */
  }
}

:deep(.custom-checkbox) {
  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: var(--el-color-primary); /* 选中状态的背景颜色 */
    border-color: var(--el-color-primary); /* 选中状态的边框颜色 */
  }
  .el-checkbox__label {
    color: var(--el-color-primary);
  }
}
</style>
