<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="名称" prop="relateName">
            <el-input v-model="formData.relateName" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="使用情况" prop="relateUsedDetail">
            <el-input
              type="textarea"
              v-model="formData.relateUsedDetail"
              placeholder="请描述使用情况"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getDictLabel } from '@/utils/dict'

defineOptions({ name: 'TableForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formType = ref()
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  relateName: '',
  relateUsedDetail: '',
  relateType: ''
})
const formRules = reactive<any>({
  relateName: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
  relateUsedDetail: [{ required: true, message: '描述不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type, item, row?) => {
  resetForm()
  dialogVisible.value = true
  formType.value = type
  dialogTitle.value = `${type === 'add' ? '新增' : '修改'}${getDictLabel(DICT_TYPE.COMPANY_INVOLVED, item)}详情`
  formData.value.relateType = item
  if (row != null && Object.keys(row).length > 0) {
    formData.value = row
    formData.value.relateType = item
  }
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    formType.value === 'add'
      ? message.success(t('common.createSuccess'))
      : message.success('修改成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success', formData.value, formType.value)
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    relateName: '',
    relateUsedDetail: '',
    relateType: ''
  }
  formRef.value?.resetFields()
}
</script>
