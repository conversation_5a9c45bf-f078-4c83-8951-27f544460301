import request from '@/config/axios'

export interface VersionFunctionVO {
  id: number
  name: string
  properties: string
  sort: number
  status: number
}

// 查询系统的版本与功能关系分页
export const getVersionFunctionPage = async (params) => {
  return await request.get({ url: `/system/version-function/page`, params })
}

// 查询系统的版本与功能关系详情
export const getVersionFunction = async (id: number) => {
  return await request.get({ url: `/system/version-function/get?id=` + id })
}

// 新增系统的版本与功能关系
export const createVersionFunction = async (data: VersionFunctionVO) => {
  return await request.post({ url: `/system/version-function/create`, data })
}

// 修改系统的版本与功能关系
export const updateVersionFunction = async (data: VersionFunctionVO) => {
  return await request.put({ url: `/system/version-function/update`, data })
}

// 删除系统的版本与功能关系
export const deleteVersionFunction = async (id: number) => {
  return await request.delete({ url: `/system/version-function/delete?id=` + id })
}

// 导出系统的版本与功能关系 Excel
export const exportVersionFunction = async (params) => {
  return await request.download({ url: `/system/version-function/export-excel`, params })
}