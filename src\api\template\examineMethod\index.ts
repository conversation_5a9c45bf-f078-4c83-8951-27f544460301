/*
 * @Description: 考核方式模块API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-29 16:44:12
 * @LastEditTime: 2024-07-30 09:12:33
 */
import request from '@/config/axios'

// 考核管理——考核方式 VO
export interface MethodVO {
  id: number // 主键(自增策略)
  examineMethod: number // 考核方式
  examineFrequency: number // 考核频率（考核方式评分周期）
  examineUnitId: number // 被考核单位
  examiners: string // 考核人员
  examineWeight: number // 考核成绩占比（该考核方式的分数计入总分时所占比重）
  baseScore: number // 基础分
  upperLimitScore: number // 上限分
  summaryMethod1: number // 汇总方式   可指定取平均还是求和
  summaryMethod2: number // 是否去除最高分和最低分
  assessmentFormat: number // 考核形式（选择线上或者线下考核）
  examineQuotaId: number // 考核指标（考核方式考核的考核指标）
}

// 考核管理——考核方式 API
export const MethodApi = {
  // 查询考核管理——考核方式分页
  getMethodPage: async (params: any) => {
    return await request.get({ url: `/system/method/page`, params })
  },

  // 查询考核管理——考核方式详情
  getMethod: async (id: number) => {
    return await request.get({ url: `/system/method/get?id=` + id })
  },

  // 新增考核管理——考核方式
  createMethod: async (data: MethodVO) => {
    return await request.post({ url: `/system/method/create`, data })
  },

  // 修改考核管理——考核方式
  updateMethod: async (data: MethodVO) => {
    return await request.put({ url: `/system/method/update`, data })
  },

  // 删除考核管理——考核方式
  deleteMethod: async (id: number) => {
    return await request.delete({ url: `/system/method/delete?id=` + id })
  },

  // 导出考核管理——考核方式 Excel
  exportMethod: async (params) => {
    return await request.download({ url: `/system/method/export-excel`, params })
  }
}
