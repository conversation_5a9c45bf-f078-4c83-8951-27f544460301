<!--
 * @Description: 专项工作编辑弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-01 15:10:13
 * @LastEditTime: 2025-07-23 16:51:05
-->
<template>
  <Dialog title="编辑" v-model="dialogVisible" center top="5vh" width="1400">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="类型" prop="primaryElement">
        <span class="font-bold">{{ formData.primaryElement }}</span>
      </el-form-item>
      <el-form-item label="指标名称" prop="secondaryElement">
        <span class="font-bold">{{ formData.secondaryElement }}</span>
      </el-form-item>
      <el-form-item label="评分标准" prop="thirdElement">
        <span class="font-bold">{{ formData.thirdElement }}</span>
      </el-form-item>
      <el-form-item label="分值" prop="standardScore">
        <span class="font-bold">{{ formData.standardScore }}</span>
      </el-form-item>
      <el-form-item label="评分规则" prop="examineContent">
        <span class="font-bold">{{ formData.examineContent }}</span>
      </el-form-item>
      <el-form-item label="附件" prop="annex">
        <UploadFile v-model="formData.annex" :fileSize="2" :fileType="['pdf']" />
        <div class="position-absolute left-130px top-0 text-#d9001b text-16px"
          >涉密文件、内部资料严禁上传。
        </div>
      </el-form-item>
      <el-form-item label="自评分数" prop="score">
        <el-input-number
          class="!w-240px"
          v-model="formData.score"
          :max="formData.standardScore"
          placeholder="请输入自评分数"
        />
      </el-form-item>
      <el-form-item label="自评描述" prop="description">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          v-model="formData.description"
          placeholder="针对评分标准对应工作情况描述"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { QuarterExamineDetailApi } from '@/api/examineTask/quarterExamine'
import { cloneDeep } from 'lodash-es'

/** 考核模板 表单 */
defineOptions({ name: 'SpecialWorkForm' })

const props = defineProps({
  quarterWorkId: {
    type: String,
    default: ''
  },
  unitId: {
    type: Number
  }
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData: any = ref({
  id: undefined,
  primaryElement: undefined,
  secondaryElement: undefined,
  examineContent: undefined,
  annex: undefined,
  description: undefined,
  templateId: undefined,
  score: undefined
})
const formRules = reactive({
  score: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
  description: [{ required: true, message: '必填项不能为空', trigger: 'blur' }]
  // primaryElement: [{ required: true, message: '类型不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (row) => {
  dialogVisible.value = true
  resetForm()
  formData.value = cloneDeep(row)
  formData.value.description = row.remark
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const submitData = { ...cloneDeep(formData.value), ...props }
    await QuarterExamineDetailApi.addEditWorkDetail(submitData)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    primaryElement: undefined,
    secondaryElement: undefined,
    examineContent: undefined,
    annex: undefined,
    description: undefined,
    templateId: undefined,
    score: undefined
  }
  formRef.value?.resetFields()
}
</script>
