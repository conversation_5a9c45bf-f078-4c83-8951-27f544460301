/*
 * @Description: 涉及化学品统计模块相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-15 11:13:06
 * @LastEditTime: 2024-08-15 15:27:47
 */

import request from '@/config/axios'
// 获取涉及化学品模块列表
export async function getChemicalsInvolvedData(params?: any) {
  return await request.get({ url: `/system/statistics/get-chemicals-involved`, params })
}

// /system/company-register/relate-list?relateType=4      topDeptId分组
export async function getRelatePageData(params?: any) {
  return await request.get({ url: `/system/company-register/relate-list`, params })
}
