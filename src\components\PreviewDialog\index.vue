<!--
 * @Description: 预览弹窗组件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-03 10:19:37
 * @LastEditTime: 2025-07-23 15:46:20
-->
<template>
  <div class="filePreviewDialog">
    <el-dialog
      append-to-body
      title="文件预览"
      v-model="dialogFormVisible"
      :close-on-click-modal="false"
      @close="close"
      :key="new Date().getTime()"
      align-center
      width="100%"
    >
      <!-- 只支持.docx格式，.doc格式使用iframe预览 -->
      <vue-office-docx
        v-if="isDocxFile(currentFile)"
        :src="currentFile"
        style="height: calc(100vh - 60px)"
      />
      <vue-office-excel
        v-else-if="isExcelFile(currentFile)"
        :src="currentFile"
        style="height: calc(100vh - 60px)"
      />
      <vue-office-pdf
        v-else-if="isPdfFile(currentFile)"
        :src="currentFile"
        style="height: calc(100vh - 60px)"
      />
      <vue-office-pptx
        v-else-if="isPptxFile(currentFile)"
        :src="currentFile"
        style="height: calc(100vh - 60px)"
      />
      <div v-else-if="isImgPath(currentFile)" style="text-align: center">
        <el-image :src="currentFile" style="height: calc(100vh - 60px)" />
      </div>
      <div v-else>
        该文件格式暂不支持预览
      </div>
      <!-- <iframe
        v-else
        ref="dynamicIframe"
        frameborder="0"
        :src="iframeUrl"
        style="width: 100%; height: calc(100vh - 60px)"
      /> -->
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import VueOfficeDocx from '@vue-office/docx'
import VueOfficeExcel from '@vue-office/excel'
import VueOfficePdf from '@vue-office/pdf'
import VueOfficePptx from '@vue-office/pptx'
import '@vue-office/docx/lib/index.css'
import '@vue-office/excel/lib/index.css'
import { Base64 } from 'js-base64'
import { isDocxFile, isExcelFile, isPdfFile, isDocFile, isImgPath, isPptxFile } from '@/utils'

defineOptions({ name: 'PreviewDialog' })
interface Emit {
  (e: 'closeDialog', value?: any): void
}
const emit = defineEmits<Emit>()
const dialogFormVisible = ref(false)
const iframeUrl = ref('')
const currentFile = ref('')
// const kkFileURL = import.meta.env.VITE_APP_KK_URL

const fileLoad = (url) => {
  // 如果是 .doc 文件，直接下载，不打开预览弹窗
  if (isDocFile(url)) {
    window.open(url, '_blank')
    return
  }

  // 其他文件正常预览
  currentFile.value = url
  dialogFormVisible.value = true
  // iframeUrl.value = `${kkFileURL}/onlinePreview?url=` + encodeURIComponent(Base64.encode(url))
}
const close = () => {
  dialogFormVisible.value = false
  emit('closeDialog')
}

defineExpose({
  fileLoad
})
</script>

<style lang="scss" scoped>
.filePreviewDialog {
  :deep(.el-dialog__body) {
    padding: 0 16px !important;
  }
}
</style>
