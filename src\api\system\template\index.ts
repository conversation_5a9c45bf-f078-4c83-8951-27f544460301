/*
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-05 10:41:01
 * @LastEditTime: 2025-07-25 15:18:39
 */
import request from '@/config/axios'

// 模板管理 VO
export interface TemplateVO {
  id: number // 主键(自增策略)
  templateName: string // 模板名称
  level: number // 层级
  type: number // 类型
  templateType: string // 被考核单位
  description: string // 描述
  templateClass: string // 模板标识
}

// 模板管理 API

// 查询模板管理分页
export const getTemplatePage = async (params: any) => {
  return await request.get({ url: `/system/template/page`, params })
}

// 查询模板管理详情
export const getTemplate = async (id: number) => {
  return await request.get({ url: `/system/template/get?id=` + id })
}

// 新增模板管理
export const createTemplate = async (data: TemplateVO) => {
  return await request.post({ url: `/system/template/create`, data })
}

// 修改模板管理
export const updateTemplate = async (data: TemplateVO) => {
  return await request.put({ url: `/system/template/update`, data })
}

// 删除模板管理
export const deleteTemplate = async (id: number) => {
  return await request.delete({ url: `/system/template/delete?id=` + id })
}

// 导出模板管理 Excel
export const exportTemplate = async (params) => {
  return await request.download({ url: `/system/template/export-excel`, params })
}
