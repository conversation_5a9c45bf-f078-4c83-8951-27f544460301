<!--
 * @Description: 数字化大屏
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-09-05 09:54:23
 * @LastEditTime: 2024-09-05 16:53:43
-->

<template>
  <div id="con">
    <div class="title">数字化大屏</div>
    <ul class="viewcon">
      <li>
        <ul class="lcon">
          <li>
            <L1 />
          </li>

          <li><L2 /></li>
        </ul>
      </li>
      <li>
        <ul class="mcon">
          <li><M1 /></li>
          <li>
            <M2 />
          </li>
          <li>
            <M3 />
          </li>
        </ul>
      </li>
      <li>
        <ul class="rcon">
          <li><R1 /></li>
        </ul>
      </li>
    </ul>
  </div>
</template>
<script setup lang="ts">
import L1 from './components/l1.vue'
import L2 from './components/l2.vue'
import M1 from './components/m1.vue'
import M2 from './components/m2.vue'
import M3 from './components/m3.vue'
import R1 from './components/r1.vue'
</script>
<style scoped lang="scss">
#con {
  color: #fff;
  margin: 0 auto;
  padding-bottom: 20px;
  width: 100%;
  background: url('@/assets/imgs/largeScreen/map_bg.png');
  background-size: 100% 100%;
  position: relative;
  .title {
    position: absolute;
    line-height: 50px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
  }
  .btn {
    position: absolute;
    top: -38px;
    right: 4px;
    font-size: 11px;
    color: #fff;
    padding: 3px 6px;
    cursor: pointer;
    border-radius: 2px;
    background: linear-gradient(#3264a4, #3fa2d1, #3264a4);
  }
  .viewcon {
    list-style: none;
    width: 99%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    padding: 10px;
    padding-top: 50px;
    > li {
      position: relative;
    }
    > li:nth-child(2) {
      flex: 1;
    }
    > li:nth-child(1) {
      width: 22%;
    }
    > li:nth-child(3) {
      width: 22%;
    }
  }
  .col {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 20px;
    justify-content: space-around;
  }
  .rcon {
    @extend .col;
    li {
      height: 100%;
    }
  }
  .mcon {
    @extend .col;
    margin: 0 10px;
    > li:nth-child(1) {
      height: 130px;
    }
    > li:nth-child(2) {
      margin: 10px 0;
      height: 340px;
    }
    > li:nth-child(3) {
      flex: 1;
    }
  }
  .lcon {
    @extend .col;
    > li:first-child {
      height: 500px;
    }
    > li:nth-child(2) {
      margin-top: 10px;
      flex: 1;
    }
  }
}
</style>
