<!--
 * @Description: 消防安全专项治理任务推进表
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-18 10:59:04
 * @LastEditTime: 2024-10-31 13:41:30
-->

<template>
  <ContentWrap>
    <el-row class="mb4" v-if="roles.includes('assessment_unit')">
      <template v-if="flag !== 'detail'">
        <el-button type="warning" plain @click="handleImport">
          <Icon icon="ep:download" />
          导入
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:upload" />导出
        </el-button>
        <el-button type="primary" plain @click="handleSetKeyword"> 设置关键字 </el-button>
      </template>
      <el-button type="warning" plain @click="handleOverall"> 总体情况 </el-button>
    </el-row>
    <el-table v-loading="loading" :data="list" :cell-class-name="handleCellClass">
      <!-- show-summary
       :summary-method="getSummaries" -->
      <el-table-column label="序号" align="center" type="index" width="60" />
      <el-table-column label="区域" align="center" prop="unitName" />
      <el-table-column
        label="发生有影响火灾事故情况"
        align="center"
        prop="fireIncidents"
        width="200"
      >
        <template #default="{ row }">
          <el-input
            v-if="row.isEditing"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            v-model="row.editData.fireIncidents"
          ></el-input>
          <span v-else>{{ row.fireIncidents }}</span>
        </template>
      </el-table-column>
      <el-table-column label="消防安全集中除患攻坚大整治情况" align="center">
        <el-table-column
          label="消防安全信息平台街镇平均录入建筑数（个）"
          align="center"
          prop="avgBuildingsRecorded"
        >
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.avgBuildingsRecorded"
              :min="0"
            />
            <span v-else>{{ row.avgBuildingsRecorded }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="消防安全信息平台街镇平均录入排查单位数（个）"
          align="center"
          prop="avgUnitsInspected"
        >
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.avgUnitsInspected"
              :min="0"
            />
            <span v-else>{{ row.avgUnitsInspected }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="消防安全信息平台街镇平均录入隐患率"
          align="center"
          prop="avgHazardsRate"
        >
          <template #default="{ row }">
            <template v-if="row.isEditing">
              <div class="flex items-center">
                <el-input-number
                  controls-position="right"
                  v-model="row.editData.avgHazardsRate"
                  :min="0"
                  :max="100"
                />%
              </div>
            </template>
            <span v-else>{{ row.avgHazardsRate !== null ? row.avgHazardsRate + '%' : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="重大火灾隐患单位或区域数（个）"
          align="center"
          prop="majorHazardUnitsOrAreas"
        >
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.majorHazardUnitsOrAreas"
              :min="0"
            />
            <span v-else>{{ row.majorHazardUnitsOrAreas }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="畅通“生命通道”情况" align="center">
        <el-table-column
          label="整治违规设置铁栅栏、防盗网、广告牌（处）"
          align="center"
          prop="correctedIrregularGrids"
        >
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.correctedIrregularGrids"
              :min="0"
            />
            <span v-else>{{ row.correctedIrregularGrids }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="高层住宅消防设施整治情况" align="center">
        <el-table-column
          label="完成整改高层住宅小区数（个）"
          align="center"
          prop="rectifiedHighRises"
        >
          <template #default="{ row }">
            <el-input-number
              controls-position="right"
              v-if="row.isEditing"
              v-model="row.editData.rectifiedHighRises"
              :min="0"
            />
            <span v-else>{{ row.rectifiedHighRises }}</span>
          </template>
        </el-table-column>
        <el-table-column label="整改率" align="center" prop="rectificationRate">
          <template #default="{ row }">
            <template v-if="row.isEditing">
              <div class="flex items-center">
                <el-input-number
                  controls-position="right"
                  v-model="row.editData.rectificationRate"
                  :min="0"
                  :max="100"
                />%
              </div>
            </template>
            <span v-else>{{
              row.rectificationRate !== null ? row.rectificationRate + '%' : ''
            }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column v-if="flag !== 'detail'" label="操作" align="center" width="150">
        <template #default="{ row }">
          <el-button v-if="!row.isEditing" link type="primary" @click="handlEdit(row)">
            编辑
          </el-button>
          <template v-else>
            <el-button link type="warning" @click="handlCancel(row)"> 取消 </el-button>
            <el-button link type="success" @click="handlConfirm(row)"> 确定 </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- 导入弹窗 -->
  <ImportForm
    ref="importFormRef"
    @success="getList"
    @importTemplate="importTemplate"
    :upUrl="FireSafetyApi.upUrl"
  />
  <!-- 设置关键词弹窗 -->
  <KeyWordForm ref="formRef" dict-type="examine_fire_safety" />
  <!-- 总体情况弹窗 -->
  <OverallDialog ref="overallDialogRef" :quarter="route.query.quarter" summaryType="4" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { FireSafetyApi } from '@/api/rootCause/fireSafety'
import { cloneDeep } from 'lodash-es'
import KeyWordForm from '../KeyWordForm.vue'
import { useUserStore } from '@/store/modules/user'

defineProps({
  flag: {
    type: String
  }
})
defineOptions({ name: 'FireSafety' })
const roles = useUserStore().getRoles
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const formRef = ref()
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list = ref<any>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams: any = reactive({
  unitId: undefined,
  unitName: undefined,
  fireIncidents: undefined,
  avgBuildingsRecorded: undefined,
  avgUnitsInspected: undefined,
  avgHazardsRate: undefined,
  majorHazardUnitsOrAreas: undefined,
  correctedIrregularGrids: undefined,
  rectifiedHighRises: undefined,
  rectificationRate: undefined,
  status: undefined,
  quarter: route.query.quarter,
  createTime: []
})
const exportLoading = ref(false) // 导出的加载中
const overallDialogRef = ref()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const queryData = {
      ...queryParams
    }
    const data = await FireSafetyApi.getFireSafetyPage(queryData)
    list.value = data.list.map((item) => ({ ...item, isEditing: false }))
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const handlEdit = (row) => {
  row.editData = {
    fireIncidents: row.fireIncidents,
    avgBuildingsRecorded: row.avgBuildingsRecorded,
    avgUnitsInspected: row.avgUnitsInspected,
    avgHazardsRate: row.avgHazardsRate,
    majorHazardUnitsOrAreas: row.majorHazardUnitsOrAreas,
    correctedIrregularGrids: row.correctedIrregularGrids,
    rectifiedHighRises: row.rectifiedHighRises,
    rectificationRate: row.rectificationRate
  }
  row.isEditing = true
}
const handlCancel = (row) => {
  row.editData = {}
  row.isEditing = false
}
const handlConfirm = async (row) => {
  // 提交请求
  try {
    const submitData = {
      ...row.editData,
      id: row.id,
      quarter: route.query.quarter,
      unitId: row.unitId,
      unitName: row.unitName
    }
    await FireSafetyApi.saveOrUpdateFireSafety(submitData)
    message.success('操作成功')
    await getList()
  } finally {
    row.isEditing = false
  }
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await FireSafetyApi.exportFireSafety(queryParams)
    download.excel(data, '消防安全专项治理任务推进.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const importTemplate = async () => {
  const res = await FireSafetyApi.getImportTemplate({ quarter: route.query.quarter })
  download.excel(res, '消防安全专项治理任务推进导入模板.xls')
}
/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open({ quarter: route.query.quarter })
}

// 计算合计
const getSummaries: any = (param) => {
  const { columns, data } = param
  const sums: (string | VNode)[] = []
  columns.forEach((column, index) => {
    if (index === 13) return
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    const values = data.map((item) => Number(item[column.property]))
    if (!values.every((value) => Number.isNaN(value))) {
      sums[index] = `${values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)}`
    } else {
      sums[index] = ''
    }
  })

  return sums
}

const handleCellClass = ({ row, rowIndex, columnIndex }) => {
  if (columnIndex === 4 || columnIndex === 5) {
    if (row.topFlag) return '!bg-#92d050'
    else if (row.bottomFlag) return '!bg-#ffff00'
  } else if (columnIndex === 8 || columnIndex === 9) {
    if (row.rectificationTopFlag) return '!bg-#92d050'
    else if (row.rectificationBottomFlag) return '!bg-#ffff00'
  }
}

const handleSetKeyword = () => {
  formRef.value.open()
}

const handleOverall = () => {
  overallDialogRef.value.openDialog()
}

const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(() => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>
<style lang="scss" scoped>
:deep(.el-input-number) {
  max-width: 100%;
}

:deep() {
  .el-table {
    --el-table-row-hover-bg-color: unset;
  }
}
</style>
