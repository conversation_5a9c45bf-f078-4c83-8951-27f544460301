<!--
 * @Description: 考核结果详情tab页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-05 15:28:56
 * @LastEditTime: 2024-10-24 16:57:46
-->
<template>
  <ContentWrap v-loading="formLoading">
    <div class="flex items-center mb-15px">
      <el-button icon="Back" type="primary" @click="handleBack">返 回</el-button>
      <div class="ml-20px text-16px font-bold pb-5px text-#1e91fc bb-1-#1e91fc w-120px f-c">
        {{ previewPageData.examineYear }}年度
      </div>
    </div>
    <div class="f-s mb-30px">
      <div
        v-for="item in tabList"
        @click="handleTabChange(item)"
        class="w-180px h-35px bg-[#f4f4f4] f-c mr-15px text-14px cursor-pointer"
        :class="{ '!bg-[#c2e1fe]': item.id === currentTab.id }"
      >
        {{ item.label }}
      </div>
    </div>
    <!-- 动态使用四个模块页面组件 -->
    <el-scrollbar class="other-comp" height="500">
      <component
        :is="currentTab.component"
        :currentTabId="currentTab.id"
        :key="currentTab.id"
      ></component>
    </el-scrollbar>
    <template v-if="currentTab.id !== '5'">
      <el-divider />
      <!-- 考核结果填写表单 -->
      <el-form
        class="pb-20px"
        ref="formRef"
        :rules="formData?.scoreStatus === 1 ? undefined : formRules"
        :model="formData"
        label-width="100px"
      >
        <div class="text-22px pl-35px pb-25px font-bold">考核结果</div>
        <el-row :gutter="10">
          <el-col :span="10">
            <el-form-item label="考核分数" prop="score">
              <div v-if="formData?.scoreStatus === 1">
                {{ formData?.score ? formData.score + '分' : '' }}
              </div>
              <el-input
                v-else
                @input="(v) => (formData.score = v.replace(/[^\d.]/g, ''))"
                v-model="formData.score"
                placeholder="请输入考核分数"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="10">
            <el-form-item label="考核意见" prop="opinions">
              <div v-if="formData?.scoreStatus === 1">{{ formData.opinions }}</div>
              <el-input
                type="textarea"
                v-else
                :autosize="{ minRows: 2, maxRows: 4 }"
                v-model="formData.opinions"
                placeholder="请输入考核意见"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" v-if="formData?.scoreStatus !== 1">
          <el-form-item>
            <el-button plain @click="submitForm('save')" type="primary" :disabled="formLoading"
              >保 存</el-button
            >
            <el-button @click="submitForm('submit')" type="primary" :disabled="formLoading"
              >提 交</el-button
            >
          </el-form-item>
        </el-row>
      </el-form>
    </template>
  </ContentWrap>
</template>

<script setup lang="ts">
import AccidentReport from '@/views/rootCause/accidentReport/index.vue'
import SpecialWork from '@/views/examineTask/yearExamine/index.vue'
import Supervision from '@/views/supervision/index.vue'
import NegativeList from '@/views/negativeList/index.vue'
import { cloneDeep } from 'lodash-es'
import { ScoreApi } from '@/api/examineResult/resultScore'
import { ExamineResultTypeMap } from '@/utils/constants'

defineOptions({ name: 'ResultTab' })

provide('resultFlag', true)
const message = useMessage()
const previewPageData = ref<any>({})
const formRef = ref()
const { push } = useRouter()
const currentTab = shallowRef<any>({
  id: '1',
  label: '事故填报',
  component: AccidentReport
})
const tabList = [
  {
    id: '1',
    label: '事故填报',
    component: AccidentReport
  },
  {
    id: '2',
    label: '考核过程',
    component: SpecialWork
  },
  {
    id: '3',
    label: '治本攻坚',
    component: SpecialWork
  },
  {
    id: '4',
    label: '督查督办',
    component: Supervision
  },
  {
    id: '5',
    label: '负面清单',
    component: NegativeList
  }
]
const formRules = reactive({
  score: [{ required: true, message: '考核分数不能为空', trigger: 'blur' }],
  opinions: [{ required: true, message: '考核意见不能为空', trigger: 'blur' }]
})
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  id: undefined,
  unitId: undefined,
  examineType: undefined,
  examineYear: undefined,
  scoreToplimit: undefined,
  score: undefined,
  opinions: undefined,
  status: undefined,
  scoreStatus: undefined,
  specialWorkId: undefined
})
// 查询当前年度当前tab下历史考核信息
const getScoreItem = async () => {
  formLoading.value = true
  try {
    const data = await ScoreApi.getScorePage({
      examineYear: previewPageData.value.examineYear,
      examineType: currentTab.value.id,
      unitId: previewPageData.value.unitId,
      onlyQueryUnit: true
    })
    formData.value = data.list?.[0] || {
      id: undefined,
      unitId: undefined,
      examineType: undefined,
      examineYear: undefined,
      scoreToplimit: undefined,
      score: undefined,
      opinions: undefined,
      status: undefined,
      scoreStatus: undefined,
      specialWorkId: undefined
    }
  } finally {
    formLoading.value = false
  }
}

const handleTabChange = (item) => {
  handleReset()
  currentTab.value = item
  getScoreItem()
}

const handleBack = () => {
  push('/examineResult')
}

const handleReset = () => {
  formData.value = {
    id: undefined,
    unitId: undefined,
    examineType: undefined,
    examineYear: undefined,
    scoreToplimit: undefined,
    score: undefined,
    scoreStatus: undefined,
    opinions: undefined,
    status: undefined,
    specialWorkId: undefined
  }
  formRef.value?.resetFields()
}

const submitForm = async (submitFlag) => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const submitData = {
      ...cloneDeep(formData.value),
      scoreStatus: submitFlag === 'save' ? undefined : 1,
      examineYear: previewPageData.value.examineYear,
      examineType: currentTab.value.id,
      unitId: previewPageData.value.unitId,
      unitName: previewPageData.value.unitName,
      scoreToplimit: previewPageData.value[`${ExamineResultTypeMap[currentTab.value.id]}Toplimit`]
    }
    await ScoreApi.addEditScore(submitData)
    message.success('操作成功')
    handleBack()
  } finally {
    formLoading.value = false
  }
}

const initPage = async () => {
  handleReset()
  const sessionObj: any = sessionStorage.getItem('examineResult')
  previewPageData.value = JSON.parse(sessionObj)
  if (previewPageData.value.compIndex) {
    currentTab.value = tabList[+previewPageData.value.compIndex]
  }
  await getScoreItem()
}

const isMounted = ref(false)
onMounted(async () => {
  if (!isMounted.value) {
    initPage().then(() => {
      isMounted.value = true
    })
  }
})

onActivated(async () => {
  if (isMounted.value) {
    initPage()
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-divider--horizontal) {
  border-top: 5px solid #dcdfe6 !important;
}
.other-comp {
  :deep(.el-card) {
    border: none;
  }
  :deep(.el-card__body) {
    padding: 0;
  }
}
</style>
