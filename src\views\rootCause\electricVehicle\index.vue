<!--
 * @Description: 电动车火灾
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-10-18 10:59:11
 * @LastEditTime: 2024-10-31 13:39:55
-->

<template>
  <ContentWrap>
    <el-row class="mb4" v-if="roles.includes('assessment_unit')">
      <template v-if="flag !== 'detail'">
        <el-button type="warning" plain @click="handleImport">
          <Icon icon="ep:download" />
          导入
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:upload" />导出
        </el-button>
        <el-button type="primary" plain @click="handleSetKeyword"> 设置关键字 </el-button>
      </template>
      <el-button type="warning" plain @click="handleOverall"> 总体情况 </el-button>
    </el-row>
    <el-table v-loading="loading" :data="list" :cell-class-name="handleCellClass">
      <!-- show-summary
       :summary-method="getSummaries" -->
      <el-table-column label="序号" align="center" type="index" width="60" />
      <el-table-column label="区域" align="center" prop="unitName" />
      <el-table-column label="电动自行车火灾事故起数" align="center" prop="fireIncidents">
        <template #default="{ row }">
          <el-input-number
            controls-position="right"
            v-if="row.isEditing"
            v-model="row.editData.fireIncidents"
            :min="0"
          />
          <span v-else>{{ row.fireIncidents }}</span>
        </template>
      </el-table-column>
      <el-table-column label="降幅" align="center" prop="decreaseRate">
        <template #default="{ row }">
          <template v-if="row.isEditing">
            <el-input-number
              controls-position="right"
              v-model="row.editData.decreaseRate"
              :min="0"
              :max="100"
            />%
          </template>
          <span v-else>{{ row.decreaseRate !== null ? row.decreaseRate + '%' : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="电动自行车排摸数（万）" align="center" prop="inspectedBikes">
        <template #default="{ row }">
          <el-input-number
            controls-position="right"
            v-if="row.isEditing"
            v-model="row.editData.inspectedBikes"
            :min="0"
          />
          <span v-else>{{ row.inspectedBikes }}</span>
        </template>
      </el-table-column>
      <el-table-column label="占公安交警部门登记数比率" align="center" prop="registrationRatio">
        <template #default="{ row }">
          <template v-if="row.isEditing">
            <el-input-number
              controls-position="right"
              v-model="row.editData.registrationRatio"
              :min="0"
              :max="100"
            />%
          </template>
          <span v-else>{{
            row.registrationRatio !== null ? row.registrationRatio + '%' : ''
          }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="flag !== 'detail'" label="操作" align="center" width="200">
        <template #default="{ row }">
          <el-button v-if="!row.isEditing" link type="primary" @click="handlEdit(row)">
            编辑
          </el-button>
          <template v-else>
            <el-button link type="warning" @click="handlCancel(row)"> 取消 </el-button>
            <el-button link type="success" @click="handlConfirm(row)"> 确定 </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- 导入弹窗 -->
  <ImportForm
    ref="importFormRef"
    @success="getList"
    @importTemplate="importTemplate"
    :upUrl="ElectricBikeFireApi.upUrl"
  />
  <!-- 设置关键词弹窗 -->
  <KeyWordForm ref="formRef" dict-type="examine_electric_bike_fire" />
  <!-- 总体情况弹窗 -->
  <OverallDialog ref="overallDialogRef" :quarter="route.query.quarter" summaryType="2" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { ElectricBikeFireApi } from '@/api/rootCause/electricVehicle'
import { cloneDeep } from 'lodash-es'
import KeyWordForm from '../KeyWordForm.vue'
import { useUserStore } from '@/store/modules/user'

defineProps({
  flag: {
    type: String
  }
})
defineOptions({ name: 'ElectricVehicle' })
const roles = useUserStore().getRoles
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const formRef = ref()
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list = ref<any>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams: any = reactive({
  unitId: undefined,
  unitName: undefined,
  fireIncidents: undefined,
  decreaseRate: undefined,
  inspectedBikes: undefined,
  registrationRatio: undefined,
  quarter: route.query.quarter,
  status: undefined,
  createTime: []
})
const exportLoading = ref(false) // 导出的加载中
const overallDialogRef = ref()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const queryData = {
      ...queryParams
    }
    const data = await ElectricBikeFireApi.getElectricBikeFirePage(queryData)
    list.value = data.list.map((item) => ({ ...item, isEditing: false }))
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const handlEdit = (row) => {
  row.editData = {
    fireIncidents: row.fireIncidents,
    decreaseRate: row.decreaseRate,
    inspectedBikes: row.inspectedBikes,
    registrationRatio: row.registrationRatio
  }
  row.isEditing = true
}
const handlCancel = (row) => {
  row.editData = {}
  row.isEditing = false
}
const handlConfirm = async (row) => {
  // 提交请求
  try {
    const submitData = {
      ...row.editData,
      id: row.id,
      quarter: route.query.quarter,
      unitId: row.unitId,
      unitName: row.unitName
    }
    await ElectricBikeFireApi.saveOrUpdateElectricBikeFire(submitData)
    message.success('操作成功')
    await getList()
  } finally {
    row.isEditing = false
  }
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ElectricBikeFireApi.exportElectricBikeFire(queryParams)
    download.excel(data, '电动自行车火灾事故及全链条治理重点任务推进.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const importTemplate = async () => {
  const res = await ElectricBikeFireApi.getImportTemplate({ quarter: route.query.quarter })
  download.excel(res, '电动自行车火灾事故及全链条治理重点任务推进导入模板.xls')
}
/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open({ quarter: route.query.quarter })
}

// 计算合计
const getSummaries: any = (param) => {
  const { columns, data } = param
  const sums: (string | VNode)[] = []
  columns.forEach((column, index) => {
    if (index === 13) return
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    const values = data.map((item) => Number(item[column.property]))
    if (!values.every((value) => Number.isNaN(value))) {
      sums[index] = `${values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)}`
    } else {
      sums[index] = ''
    }
  })

  return sums
}

const handleCellClass = ({ row, rowIndex, columnIndex }) => {
  if (columnIndex === 2 || columnIndex === 3) {
    if (row.topFlag) return '!bg-#92d050'
    else if (row.bottomFlag) return '!bg-#ffff00'
  } else if (columnIndex === 4 || columnIndex === 5) {
    if (row.registerTopFlag) return '!bg-#92d050'
    else if (row.registerBottomFlag) return '!bg-#ffff00'
  }
}

const handleSetKeyword = () => {
  formRef.value.open()
}

const handleOverall = () => {
  overallDialogRef.value.openDialog()
}

const isAlreadyEnter = ref(false)
/** 初始化 **/
onMounted(() => {
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>
<style lang="scss" scoped>
:deep() {
  .el-table {
    --el-table-row-hover-bg-color: unset;
  }
}
</style>
