<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" center>
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-row>
        <el-col :span="22">
          <el-form-item label="报告名称" prop="workName">
            <el-input v-model="formData.workName" placeholder="请输入报告名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="类型" prop="workType">
            <el-select
              class="w-full"
              v-model="formData.workType"
              clearable
              placeholder="请选择类型"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.PLAN_REPORT_PAIR)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="22">
          <el-form-item label="文件" prop="annex">
            <UploadFile v-model="formData.annex" :file-size="1024" :limit="1" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="22">
          <el-form-item label="说明" prop="workName">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              v-model="formData.workName"
              placeholder="请输入说明"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">
        {{ formData.workType === '2' ? '下一步' : '确 定' }}
      </el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { WorkApi } from '@/api/examineTask/yearWork'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { useUserStore } from '@/store/modules/user'

defineOptions({ name: 'ReportForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { push } = useRouter()
const props = defineProps({
  year: {
    type: Number
  }
})
const roles = useUserStore().getRoles
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('填报一计划双报告') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<any>({
  workName: '',
  workType: '',
  annex: ''
})

const formRules = reactive<any>({
  workName: [{ required: true, message: '工作名称不能为空', trigger: 'blur' }],
  workType: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
  annex: [{ required: true, message: '文件不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  formType.value = type
  resetForm()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const submitData = {
      ...formData.value,
      year: props.year
    }
    const data = await WorkApi.createWork(submitData)
    if (data) {
      dialogVisible.value = false
      emit('success')
      message.success(t('common.createSuccess'))
      // 选择年末履职报告
      if (formData.value.workType == '2') {
        if (roles.includes('assessed_unit')) {
          // 成员单位
          push(`/memberUnitReportCreate?id=${data}&type=edit`)
        } else if (roles.includes('district')) {
          // 区县
          push(`/districtReportCreate?id=${data}&type=edit`)
        }
      }
    }
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    workName: '',
    workType: '',
    annex: ''
  }
  formRef.value?.resetFields()
}
</script>
