<!--
 * @Description: 督查督办列表页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-08-27 11:28:20
 * @LastEditTime: 2025-07-22 10:47:02
-->
<template>
  <ContentWrap>
    <template v-if="!resultFlag">
      <!-- 搜索工作栏 -->
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="98px">
        <el-form-item label="被考核单位" prop="evaluatedUnitName">
          <el-input
            v-model="queryParams.evaluatedUnitName"
            placeholder="请输入被考核单位"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery"
            ><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button
          >
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        </el-form-item>
      </el-form>
      <el-row class="mb-10px">
        <el-button
          v-hasPermi="['examine:supervision-tasks:create']"
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon class="mr-5px" icon="ep:plus" /> 新 建
        </el-button>
        <el-button
          v-hasPermi="['examine:supervision-tasks:import']"
          type="warning"
          plain
          @click="handleImport"
        >
          <Icon class="mr-5px" icon="ep:download" /> 导 入
        </el-button>
        <el-button
          v-hasPermi="['examine:supervision-tasks:export']"
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
        >
          <Icon class="mr-5px" icon="ep:upload" /> 导 出
        </el-button>
      </el-row>
    </template>
    <el-table v-loading="loading" :data="tableData" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column type="index" label="序号" width="60"></el-table-column>
      <el-table-column label="督查督办名称" align="center" prop="supervisionName" />
      <el-table-column label="督查督办内容" align="center" prop="content" />
      <el-table-column label="被考核单位" align="center" prop="evaluatedUnitName" />
      <el-table-column
        label="截止日期"
        align="center"
        prop="dueDate"
        :formatter="dateFormatter2"
        width="150px"
      />
      <el-table-column label="状态" align="center" prop="status" width="110">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TASK_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="完成情况" align="center" prop="completionStatus" width="110">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.EXAMINE_RESULTS" :value="scope.row.completionStatus" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="185px"
      />
      <el-table-column
        v-if="resultFlag"
        label="考核分值"
        align="center"
        prop="assessmentScore"
        width="150"
      />
      <el-table-column label="操作" align="center" width="200">
        <template #default="scope">
          <template v-if="!resultFlag">
            <el-button
              v-hasPermi="['examine:supervision-tasks:flow']"
              v-if="scope.row.status === 0"
              link
              type="warning"
              @click="handleFlow(scope.row, '下发')"
            >
              下发
            </el-button>
            <el-button
              v-hasPermi="['examine:supervision-tasks:accept']"
              v-if="scope.row.status === 1"
              link
              type="success"
              @click="handleFlow(scope.row, '接收')"
            >
              接收
            </el-button>
            <el-button
              v-hasPermi="['examine:supervision-tasks:report']"
              v-if="scope.row.status === 2"
              link
              type="warning"
              @click="handleDetail(scope.row, 'report')"
            >
              上报
            </el-button>
            <el-button
              v-hasPermi="['examine:supervision-tasks:examine']"
              v-if="scope.row.status === 3"
              link
              type="primary"
              @click="handleDetail(scope.row, 'examine')"
            >
              考核
            </el-button>
          </template>
          <template v-else>
            <el-button
              v-if="!scope.row.assessmentScore"
              link
              type="primary"
              @click="handleExamine(scope.row)"
            >
              考核
            </el-button>
          </template>
          <el-button link type="primary" @click="handleDetail(scope.row, 'detail')">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 表单弹窗：添加/修改 -->
  <SupervisionTasksForm
    :assessmentYear="wsCache.get('year')"
    ref="formRef"
    @success="getList"
  />
  <!-- 导入 -->
  <ImportForm
    ref="importFormRef"
    @success="getList"
    @importTemplate="importTemplate"
    :upUrl="SupervisionTasksApi.upUrl"
  />

  <ExamineDialog ref="examineDialogRef" @fetch-data="getList" :currentTabId="currentTabId" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import SupervisionTasksForm from './SupervisionTasksForm.vue'
import { SupervisionTasksApi } from '@/api/supervision'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import { SchemeApi } from '@/api/template/examinePlan'
import { uniqBy } from 'lodash-es'
import { useCache } from '@/hooks/web/useCache'

defineOptions({ name: 'Supervision' })

const { wsCache } = useCache()
const props = defineProps({
  currentTabId: {
    type: String,
    default: ''
  }
})
const route = useRoute()
const resultFlag = inject('resultFlag', false)
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter()
const loading = ref(true) // 列表的加载中
const tableData = ref<any>([]) // 列表的数据
const formRef = ref()
const total = ref(0) // 列表的总页数
const queryFormRef = ref() // 搜索的表单
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  supervisionName: undefined,
  content: undefined,
  evaluatedUnitName: undefined,
  dueDate: [],
  relatedPenaltyRule: undefined,
  status: undefined,
  completionStatus: undefined,
  createTime: []
})


/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const sessionObj: any = JSON.parse(sessionStorage.getItem('examineResult') as any)
    const data = await SupervisionTasksApi.getSupervisionTasksPage({
      ...queryParams,
      assessmentYear: resultFlag ? sessionObj.examineYear : wsCache.get('year')
    })
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/* 新建 */
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/* 详情 */
const handleDetail = (row, optFlag?) => {
  push({
    path: '/supervision/supervisionDetail',
    query: {
      id: row.id,
      backPath: route.path,
      optFlag
    }
  })
}

/* 接受/下发操作 */
const handleFlow = async (row, text) => {
  // 提交的二次确认
  await message.confirm(`是否确认${text}该条数据？`)
  await SupervisionTasksApi.updateSupervisionTasks({
    id: row.id,
    status: row.status + 1,
    submitFlag: true
  })
  message.success('操作成功')
  // 刷新列表
  await getList()
}


/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}
/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await SupervisionTasksApi.exportSupervisionTasks(queryParams)
    download.excel(data, '督查督办.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
const importTemplate = async () => {
  const res = await SupervisionTasksApi.getImportTemplate({})
  download.excel(res, '督查督办导入模板.xls')
}

const examineDialogRef = ref()
const handleExamine = (row) => {
  examineDialogRef.value.openDialog(row)
}

const isAlreadyEnter = ref(false)
const flag = ref('1')
/** 初始化 **/
onMounted(async () => {
  if (route.path === '/supervision/supervisionExaminedUnit') {
    flag.value = '2'
  }
  getList().then(() => {
    isAlreadyEnter.value = true
  })
})

onActivated(() => {
  if (isAlreadyEnter.value) {
    getList()
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-link) {
  width: 100%;
  text-align: center;
}
</style>
