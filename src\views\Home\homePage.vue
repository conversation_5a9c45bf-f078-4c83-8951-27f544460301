<!--
 * @Description: homePage
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-22 17:05:29
 * @LastEditTime: 2025-07-21 16:15:25
-->
<template>
  <!-- <div>
    <el-card shadow="never">
      <el-skeleton :loading="loading" animated>
        <el-row :gutter="20" justify="space-between">
          <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
            <div class="flex items-center">
              <img :src="avatar" alt="" class="mr-20px h-70px w-70px rounded-[50%]" />
              <div>
                <div class="text-20px">
                  {{ t('workplace.welcome') }} {{ username }} {{ t('workplace.happyDay') }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-skeleton>
    </el-card>
  </div> -->
  <el-row class="mt-5px" :gutter="20" justify="space-between">
    <el-col :span="24">
      <el-card shadow="never" class="mt-5px">
        <template #header>
          <div class="card-title">数据指标</div>
        </template>
        <TargetCompletion ref="targetCompletionRef" />
      </el-card>
    </el-col>
  </el-row>

  <el-row class="mt-5px" :gutter="20" justify="space-between">
    <el-col :span="12" class="mb-10px">
      <el-card shadow="never" class="mt-5px h-340px">
        <template #header>
          <div class="card-title">快捷入口</div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div class="grid grid-cols-5" v-if="menuList.length">
            <div
              class="cursor-pointer f-c flex-col my-20px hover:color-[var(--el-color-primary)]"
              @click="jumpTo(item)"
              v-for="(item, index) in menuList"
            >
              <img
                v-if="item.meta?.customizeIcon"
                class="w-50px"
                :src="item.meta.customizeIcon"
                alt=""
              />
              <Icon v-else :size="44" :icon="item.meta.icon" />
              <div class="mt-10px">{{ item.meta.title }}</div>
            </div>
          </div>
          <el-empty v-else></el-empty>
        </el-skeleton>
      </el-card>
    </el-col>
    <el-col :span="12" class="mb-10px">
      <el-card shadow="never" class="mt-5px">
        <template #header>
          <div class="flex justify-between">
            <div class="card-title"
              >消息通知&nbsp;
              <span class="text-13px text-red-500">({{ publicInfo.length }})</span>
            </div>
            <el-link
              type="primary"
              :underline="false"
              @click="handlePush('/system/notice')"
            >
              {{ t('action.more') }}
              <Icon icon="ep:d-arrow-right" />
            </el-link>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div v-if="publicInfo.length > 0">
            <div v-for="(item, index) in publicInfo" :key="`dynamics-${index}`">
              <div class="position-relative flex justify-between px-10px">
                <span
                  class="before:content-[''] before:position-absolute before:left-4px before:top-9px before:bg-red-500 before:w-1.5 before:h-1.5 before:rounded-full ml-15px"
                >
                  {{ item.title }}
                </span>
                <div>
                  <span>{{ formatTime(item.createTime, 'yyyy-MM-dd') }}</span>
                  <span class="listLink ml-50px" @click="handleDetail(item)">查看</span>
                </div>
              </div>
              <el-divider />
            </div>
          </div>
          <el-empty v-else></el-empty>
        </el-skeleton>
      </el-card>

      <!-- <el-card shadow="never" class="mt-5px">
        <template #header>
          <div class=" flex justify-between">
            <div class="card-title">政策解读</div>
            <el-link type="primary" :underline="false" @click="handlePush('/policyInterpret')">
              {{ t('action.more') }}
            </el-link>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div v-if="policyList.length > 0">
            <div v-for="(item, index) in policyList" :key="`dynamics-${index}`">
              <div class="flex justify-between px-10px">
                <span>{{ item.title }}</span>
                <div>
                  <span>{{ formatTime(item.createTime, 'yyyy-MM-dd') }}</span>
                  <span class="listLink ml-50px" @click="handleDetail(item)">查看</span>
                </div>
              </div>
              <el-divider />
            </div>
          </div>
          <el-empty v-else></el-empty>
        </el-skeleton>
      </el-card> -->
    </el-col>

    <!-- <el-col :span="24" class="mb-10px">
      <el-card shadow="never" class="mt-5px">
        <template #header>
          <div class=" flex justify-between">
            <div class="card-title">政策解读</div>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div>
            <h4 class="text-center text-20px mb-5px">
              {{ policyList[0]?.title }}
            </h4>
            <iframe
              ref="dynamicIframe"
              frameborder="0"
              :src="
                `${kkFileURL}/onlinePreview?url=` +
                encodeURIComponent(Base64.encode(policyList[0]?.attachments))
              "
              class="w-100% h-700px"
            />
          </div>
        </el-skeleton>
      </el-card>
    </el-col> -->
  </el-row>
  <DocumentForm ref="htmlDialogRef" />
  <PreviewDialog ref="previewRef" />
</template>
<script lang="ts" setup>
import TargetCompletion from './components/targetCompletion.vue'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { Base64 } from 'js-base64'
import { formatTime } from '@/utils'
import { isArray } from '@/utils/is'
import { useUserStore } from '@/store/modules/user'
import avatarImg from '@/assets/imgs/avatar.png'
import * as NoticeApi from '@/api/system/notice'
import { InterpretationApi } from '@/api/policyInterpret'
import { LearningApi } from '@/api/studyCommun'

defineOptions({ name: 'HomePage' })

const permissionStore = usePermissionStoreWithOut()
const message = useMessage()
const emit = defineEmits(['updateCurrentView'])
const { push } = useRouter() // 路由
const { t } = useI18n()
const userStore = useUserStore()
const loading = ref(true)
const avatar = userStore.getUser.avatar ? `${userStore.getUser.avatar}` : avatarImg
const username = userStore.getUser.nickname
const queryParams = ref({
  pageNo: 1,
  pageSize: 6
})
const targetCompletionRef = ref()

// 获取消息通知
const publicInfo: any = ref([])
const getNotice = async () => {
  try {
    const data = await NoticeApi.getNoticePage({ ...queryParams.value, status: 0 })
    publicInfo.value = data.list
  } finally {
  }
}
// 获取政策解读
const policyList: any = ref([])
const getPolicyList = async () => {
  try {
    const data = await InterpretationApi.getInterpretationPage({ ...queryParams.value, status: 1 })
    policyList.value = data.list
  } finally {
  }
}
// 获取交流学习
const studyCommunList: any = ref([])
const getStudyCommunList = async () => {
  try {
    const data = await LearningApi.getLearningPage({ ...queryParams.value, status: 1 })
    studyCommunList.value = data.list
  } finally {
  }
}

const getAllApi = async () => {
  await Promise.all([
    getNotice(),
    getPolicyList()
    // getStudyCommunList()
  ])
  loading.value = false
}

const handlePush = (path) => {
  push(path)
}
const previewRef = ref()
const htmlDialogRef = ref()
const handleDetail = (item) => {
  if (!item.contentType) {
    if (!item.attachments) return message.warning('暂无内容查看')
    previewRef.value.fileLoad(item.attachments)
  } else if (item.contentType === 1) {
    htmlDialogRef.value.fileLoad(item.content)
  } else if (item.contentType === 2) {
    previewRef.value.fileLoad(item.attachments)
  }
}

/* ==================== 快捷入口模块相关逻辑 ==================== */
const menuList = ref<any>([])
const filterMenuList = (items) => {
  return items.reduce((acc, item) => {
    // 检查customizeIcon是否存在
    if (item.meta?.customizeIcon) {
      // 将符合条件的对象添加到结果中
      acc.push({ ...item })
    } else if (isArray(item.children) && item.children.length > 0) {
      const filteredChildren = filterMenuList(item.children)
      if (filteredChildren.length > 0) {
        acc = [...acc, ...filteredChildren]
      }
    }
    return acc
  }, [])
}

const jumpTo = (item) => {
  push({ name: item.name })
}

onMounted(() => {
  getAllApi()
  menuList.value = filterMenuList(permissionStore.routers)
  targetCompletionRef.value?.getEchartsData()
})
</script>

<style lang="scss" scoped>
.card-title {
  font-size: 18px;
  line-height: 24px;
  font-weight: bold;
}
:deep(.el-card__body) {
  font-size: 16px;
}
:deep(.el-divider--horizontal) {
  margin: 8px auto;
}
</style>
